package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OcmsPriceIndexWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PriceExtendWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaasPriceServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.PrecentChannelPriceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.PriceEffectWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.StoreBaseQueryRequest;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@InterfaceDoc(
        displayName = "中台价格相关接口",
        type = "restful",
        scenarios = "用于获取中台价格指数, 等能力.",
        description = "用于获取中台价格指数, 等能力",
        host = "https://pieapi-empower.shangou.meituan.com/"
)
@Api("中台价格相关接口")
@Auth
@Slf4j
@RestController
@RequestMapping("/pieapi/priceservice")
public class PriceController {
    @Resource
    private OcmsPriceIndexWrapper priceIndexWrapper;
    @Resource
    private PriceExtendWrapper priceExtendWrapper;
    @Resource
    private PriceEffectWrapper priceEffectWrapper;
    @Resource
    private PrecentChannelPriceWrapper precentChannelPriceWrapper;
    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;

    @MethodDoc(
            description = "门店商品SPU改价信息查询",
            displayName = "门店商品SPU改价信息查询",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品SPU改价信息查询请求",
                            type = PriceIndexStoreCategoryQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951, \"spuId\":\"SPU1234\"}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/queryStoreSpuAdjustPriceInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStoreSpuAdjustPriceInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSpuAdjustPriceInfoVO> queryStoreSpuAdjustPriceInfo(@RequestBody StoreSpuAdjustPriceInfoQueryRequest request) {

        // 校验参数
        request.validate();

        // 查询SPU改价信息
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return saasPriceServiceWrapper.queryStoreSpuAdjustPriceInfo(user, request.getStoreId(), request.getSpuId());
    }

    @MethodDoc(
            description = "SPU改价信息和定价助手聚合接口，只支持菜大全美团渠道",
            displayName = "SPU改价信息和定价助手聚合接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "SPU改价信息和定价助手聚合接口查询请求",
                            type = StoreSpuAdjustPriceInfoQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951, \"spuId\":\"SPU1234\"}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/queryAdjustStrategyAndReferencePrice",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryAdjustStrategyAndReferencePrice", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuAdjustStrategyAndReferencePriceVO> querySingleSkuAdjustStrategyAndReferencePrice(@RequestBody StoreSkuAdjustPriceRequest request) {

        // 校验参数
        request.validate();

        // 查询SPU改价信息
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return saasPriceServiceWrapper.querySingleSkuAdjustStrategyAndReferencePrice(user, request.getStoreId(), request.getSpuId(), request.getSkuId());
    }

    @MethodDoc(
            description = "门店品类价格指数查询",
            displayName = "门店品类价格指数查询",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店品类价格指数查询请求",
                            type = PriceIndexStoreCategoryQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/queryStoreAndCategoryPriceIndex",
            extensions = {
            @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @PostMapping("/priceindex/queryStoreAndCategoryPriceIndex")
    @ApiOperation(value = "门店品类价格指数查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<PriceIndexStoreCategoryVO> queryStoreAndCategoryPriceIndex(@RequestBody PriceIndexStoreCategoryQueryRequest request) {
        StoreBaseQueryRequest req = StoreBaseQueryRequest.builder()
                .tenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())
                .storeId(request.getStoreId())
                .build();
        return priceIndexWrapper.queryStoreCategoryPriceIndex(req);

    }


    @MethodDoc(
            description = "品类下商品分页查询接口",
            displayName = "品类下商品分页查询接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "PriceIndexCategoryProductPageRequest",
                            type = PriceIndexCategoryProductPageRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/pageCategoryProductPriceIndex",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @PostMapping("/priceindex/pageCategoryProductPriceIndex")
    @ApiOperation(value = "品类下商品分页查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<PriceIndexCategorySkuVO> pageCategoryProductPriceIndex(@RequestBody PriceIndexCategoryProductPageRequest request) {
        return priceIndexWrapper.pageCategoryProductPriceIndex(
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                request);
    }



    @MethodDoc(
            description = "门店品类价格扩展值查询",
            displayName = "门店品类价格扩展值查询",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店品类价格扩展值查询请求",
                            type = StorePriceExtendQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/queryStoreAndCategoryPriceIndex",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )

    @ApiOperation(value = "门店品类价格扩展值查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping("/priceExtend/queryStoreAndCategoryPriceExtend")
    public CommonResponse<PriceExtendStoreCategoryVO> queryStoreAndCategoryPriceExtend(@RequestBody StorePriceExtendQueryRequest request) {
        request.validate();

        return priceExtendWrapper.queryStoreCategoryPriceExtend(
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                request.getStoreId(), request.getType(), request.getFilterCore(), request.getFilterCommon());


    }


    @MethodDoc(
            description = "品类下商品分页查询接口",
            displayName = "品类下商品分页查询接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "PriceIndexCategoryProductPageRequest",
                            type = PriceIndexCategoryProductPageRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/pageCategoryProductPriceIndex",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "品类下商品分页查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping("/priceExtend/querySkuPriceExtendList")
    public CommonResponse<PriceExtendCategorySkuVO> pageCategoryProductPriceExtend(@RequestBody PriceExtendCategorySkuReq request) {
        request.validate();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        return priceExtendWrapper.queryCategorySkuPriceExtend(tenantId, request);

    }

    @MethodDoc(
            description = "关注竞品接口",
            displayName = "关注竞品接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "FollowCommand",
                            type = FollowCommand.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/followCompetitor",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "关注竞品接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping("/followCompetitor")
    public CommonResponse<Void> followCompetitor(@RequestBody FollowCommand command) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        return priceEffectWrapper.followSku(tenantId, command.getStoreId(),
                command.getSkuId(), command.getSpuId(), command.getFollow());
    }

    @MethodDoc(
            description = "竞品列表查询接口",
            displayName = "竞品列表查询接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "FollowCommand",
                            type = FollowCommand.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/followCompetitor",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "竞品列表查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping("/queryCompetitorSkuList")
    public CommonResponse<CompetitorSkuListVO> queryCompetitorSkuList(@RequestBody CompetitorSkuQuery query) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        return priceEffectWrapper.queryCompetitorSkuList(tenantId, query.getStoreId(), query.getSkuId(), query.getSpuId());
    }

    @MethodDoc(
            description = "竞争力列表查询接口，包含商品名模糊搜索",
            displayName = "竞争力列表查询接口，包含商品名模糊搜索",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "FollowCommand",
                            type = FollowCommand.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/followCompetitor",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "竞争力列表查询接口，包含商品名模糊搜索")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping("/queryCompetitivenessStoreSkuList")
    public CommonResponse<CompetitivenessSkuListVO> queryCompetitivenessStoreSkuList(@RequestBody CompetitivenessSkuQuery query) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        return priceEffectWrapper.queryCompetitivenessStoreSkuList(tenantId, query.getStoreId(),
                query.getKeyword(), query.getPageSize(), query.getPageNum());
    }


    @MethodDoc(
            description = "商品价格待优化列表",
            displayName = "商品价格待优化列表",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "StorePriceEffectSkuQuery",
                            type = StorePriceEffectSkuQuery.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/priceindex/followCompetitor",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "商品价格待优化列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping("/queryPriceEffectToBeImprovedSkuList")
    public CommonResponse<PriceEffectIndexToBeImprovedVO> queryPriceEffectToBeImprovedSkuList(@Valid @RequestBody StorePriceEffectSkuQuery query) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        return priceEffectWrapper.queryPriceEffectToBeImprovedSkuList(tenantId, query.getStoreId(),
                query.getNeedStoreAggregation(), query.getPriceEffectIndexType(),
                query.getFilterCore(), query.getPageSize(), query.getPageNum(), query.getSortType());
    }


    @MethodDoc(
            description = "查询同城门店商品请求",
            displayName = "查询同城门店商品请求",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询城市门店商品请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/priceservice/queryRegionStoreSku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRegionStoreSkuList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<RegionStoreSkuVO>> queryRegionStoreSkuList(@RequestBody RegionStoreSkuQueryRequest request) {

        // 校验参数
        request.validate();

        // 查询当前门店sku的同城其它门店的商品数据
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return priceEffectWrapper.queryRegionStoreSkuList(user, request.getStoreId(), request.getSpuId(), request.getSkuId());
    }

    @MethodDoc(
            description = "查询定价助手参考价数据",
            displayName = "查询定价助手参考价数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询定价助手参考价数据请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/priceservice/querySkuPriceEffectIndexReferencePrice",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkuPriceEffectIndexReferencePrice", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Map<String, List<PriceEffectIndexReferencePriceVO>>> querySkuPriceEffectIndexReferencePrice(@RequestBody PriceEffectIndexReferencePriceQueryRequest request) {

        // 校验参数
        request.validate();
        // 查询定价助手参考价指标数据
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return priceEffectWrapper.querySkuPriceEffectIndexReferencePrice(user, request.getStoreId(), request.getSkuIds());
    }

    @MethodDoc(
            description = "价格不一致商品列表",
            displayName = "价格不一致商品列表",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "request",
                            type = QueryNotEqualSignSkuRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/priceservice/queryNotEqualSignSkus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "价格不一致商品列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping("/queryNotEqualSignSkus")
    public CommonResponse<PresentChannelPriceNotEqualSignPageVO> queryNotEqualSignSkuList(@Valid @RequestBody QueryNotEqualSignSkuRequest request) {
        try {
            return precentChannelPriceWrapper.queryPresentChannelPriceNotEqualSignSkuList(request.getStoreId(),
                    request.getPage(), request.getSize());
        } catch (Exception e) {
            log.error("获取价格不一致列表异常。", e);
        }
        return CommonResponse.fail2(ResultCode.FAIL);
    }
}