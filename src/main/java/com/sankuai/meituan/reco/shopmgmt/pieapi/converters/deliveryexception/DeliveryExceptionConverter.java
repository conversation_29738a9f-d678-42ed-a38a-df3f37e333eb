package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.deliveryexception;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:21
 * @Description:
 */

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OrderDeliveryExceptionListRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OrderListPageResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OnlineOrderVO;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.deliveryexception.DeliveryExceptionOpenListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.deliveryexception.DeliveryExceptionOrderListVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DistributeStatusUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderKey;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderOperateCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019/11/21
 * 配送异常转换器
 **/
public class DeliveryExceptionConverter {

    private static final Integer ONE_WEEK_DAYS = 7;
    private static final Integer ONE_MONTH_DAYS = 30;

    public static DeliveryExceptionOrderListVo convert2ListVo(OrderListPageResponse response, boolean isOpen) {
        DeliveryExceptionOrderListVo vo = new DeliveryExceptionOrderListVo();
        vo.setTotalRrd(response.getCount());
        vo.setHasMore(response.isHasMore());
        if (CollectionUtils.isNotEmpty(response.getOrderModelList())) {
            vo.setOrderList(response.getOrderModelList().stream().map(e -> convert2OrderVo(e, isOpen)).collect(Collectors.toList()));
        }
        return vo;
    }

    private static DeliveryExceptionOrderListVo.DeliveryExceptionOrderVo convert2OrderVo(OnlineOrderVO order, boolean isOpen) {
        DeliveryExceptionOrderListVo.DeliveryExceptionOrderVo orderVo = new DeliveryExceptionOrderListVo.DeliveryExceptionOrderVo();
        orderVo.setChannelId(ChannelOrderConvertUtils.sourceBiz2Mid(order.getOrderBizType()));
        orderVo.setChannelOrderId(order.getViewOrderId());
        orderVo.setTenantId(order.getTenantId());
        Integer distributeStatus = order.getDeliveryInfoModel().getDistributeStatus();
        orderVo.setDistributeStatus(distributeStatus);
        orderVo.setDistributeStatusTime(order.getDeliveryInfoModel().getOperateTime());
        orderVo.setOrderTime(order.getCreateTime());
        orderVo.setOrderId(order.getOrderId());
        orderVo.setExceptionDescription(order.getDeliveryInfoModel().getExceptionDescription());
        orderVo.setExpectedDeliveryTime(order.getDeliveryInfoModel().getArrivalEndTime());
        orderVo.setRiderName(order.getDeliveryInfoModel().getRiderName());
        orderVo.setRiderPhone(order.getDeliveryInfoModel().getRiderPhone());
        orderVo.setUserName(order.getDeliveryInfoModel().getUserName());
        orderVo.setUserPhone(order.getDeliveryInfoModel().getUserPhone());
        orderVo.setSerialNum(order.getOrderSerialNumber() != null ? order.getOrderSerialNumber().toString() : "");
        if (isOpen && StringUtils.isNotEmpty(order.getDeliveryInfoModel().getDeliveryCancelledReasonDescription())) {
            orderVo.setExceptionDescription(order.getDeliveryInfoModel().getDeliveryCancelledReasonDescription());
        }
        if (isOpen && StringUtils.isNotEmpty(order.getDeliveryInfoModel().getDeliveryRejectedReasonDescription())) {
            orderVo.setExceptionDescription(order.getDeliveryInfoModel().getDeliveryRejectedReasonDescription());
        }
        if (isOpen) {
            orderVo.setOperationList(DistributeStatusUtils.getOpenOperationList(order));
            String distributeStatusName = DistributeStatusUtils.getDistributeStatusName(distributeStatus);
            orderVo.setDistributeStatusName(distributeStatusName);
        } else {
            orderVo.setOperationList(DistributeStatusUtils.getClosedOperationList(distributeStatus));
            DistributeStatusEnum statusEnum = DistributeStatusEnum.enumOf(distributeStatus);
            orderVo.setDistributeStatusName(statusEnum != null ? statusEnum.getDesc() : "");
        }
        return orderVo;
    }

    public static OrderDeliveryExceptionListRequest convert2Request(DeliveryExceptionOpenListRequest req) {
        OrderDeliveryExceptionListRequest request = new OrderDeliveryExceptionListRequest();
        request.setTenantId(req.getTenantId());
        request.setShopId(req.getStoreId());
        request.setBeginTime(DateTime.now().minusDays(ONE_WEEK_DAYS).getMillis());
        request.setEndTime(DateTime.now().getMillis());
        request.setOffset((req.getPageNo() - 1) * req.getPageSize());
        request.setLimit(req.getPageSize());
        return request;
    }




    public static OrderOperateCheckRequest convert2CheckRequest(List<DeliveryExceptionOrderListVo.DeliveryExceptionOrderVo> list) {
        OrderOperateCheckRequest request = new OrderOperateCheckRequest();
        request.setToCheckOperateItems(Lists.newArrayList(OrderCouldOperateItem.FULL_ORDER_REFUND.getValue()));
        List<OrderKey> orderKeys = list.stream().map(e -> {
            OrderKey orderKey = new OrderKey();
            orderKey.setChannelType(e.getChannelId());
            orderKey.setChannelOrderId(e.getChannelOrderId());
            orderKey.setTenantId(e.getTenantId());
            return orderKey;
        }).collect(Collectors.toList());
        request.setOrderList(orderKeys);
        return request;
    }
}

