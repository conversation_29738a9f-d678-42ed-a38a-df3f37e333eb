package com.sankuai.meituan.reco.shopmgmt.pieapi.converters;

import com.meituan.shangou.saas.tenant.thrift.dto.contract.SignerInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.SignContractRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract.ContractSignRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.eleccontract.SignerInfoVO;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 19:40
 * @Description:
 */
public class ElecContractConverter {

    public static SignerInfoVO SignerInfoConvert(SignerInfoDTO dto) {
        SignerInfoVO signerInfoVO = new SignerInfoVO();
        signerInfoVO.setMerchantName(dto.getMerchantName());
        signerInfoVO.setAddress(dto.getAddress());
        signerInfoVO.setLegalPerson(dto.getLegalPerson());
        signerInfoVO.setSignerName(dto.getManager());
        signerInfoVO.setSignerPhoneNum(dto.getPhoneNum());
        signerInfoVO.setSubjectWmPoiId(dto.getSubjectWmPoiId());

        return signerInfoVO;
    }

    public static SignContractRequest signRequestConvert(Long tenantId, Long operatorId,
                                                         String operatorAccount, ContractSignRequest request) {
        SignContractRequest thriftRequest = new SignContractRequest();
        thriftRequest.setTenantId(tenantId);
        thriftRequest.setPoiId(request.getStoreId());
        thriftRequest.setMerchantName(request.getMerchantName());
        thriftRequest.setAddress(request.getAddress());
        thriftRequest.setLegalPerson(request.getLegalPerson());
        thriftRequest.setSignerName(request.getSignerName());
        thriftRequest.setSignerPhoneNum(request.getSignerPhoneNum());
        thriftRequest.setOperatorId(operatorId);
        thriftRequest.setOperatorAccount(operatorAccount);
        thriftRequest.setEmail(request.getEmail());
        thriftRequest.setContractId(request.getContractId());
        thriftRequest.setConfirmLetterId(request.getConfirmLetterId());
        thriftRequest.setRequestCode(request.getRequestCode());
        thriftRequest.setResponseCode(request.getResponseCode());
        thriftRequest.setSubjectWmPoiId(request.getSubjectWmPoiId());

        return thriftRequest;
    }
}
