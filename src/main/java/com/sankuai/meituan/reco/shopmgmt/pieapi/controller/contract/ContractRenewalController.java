package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.contract;


import com.alibaba.fastjson.JSONObject;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.commercial.StoreServiceThriftService;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.common.OptUserDto;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.dto.commercial.StoreServiceDto;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.dto.commercial.request.StoreServicePageRequest;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.dto.commercial.request.StoreServiceRemindRequest;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.dto.commercial.response.StoreServiceListResponse;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.dto.commercial.response.StoreServiceRemindResponse;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.enums.ServiceStatusEnum;
import com.meituan.shangou.qnh.tenant.biz.client.thrift.enums.ServiceTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.TenantThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.ContractExpirationReminderRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.ContractExpirationReminderResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.ContractDownloadDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.ContractDownloadRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.ContractDownloadResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.BillPayRemindVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ContractDownloadVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ContractExpirationReminderVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreServiceRemindVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.contract.RemindConfirmRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.contract.RemindRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.BeanMapperUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.saas.common.file.ExcelUtil;
import com.sankuai.meituan.shangou.saas.common.runtime.RpcInvoker;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.meituan.shangou.qnh.tenant.biz.client.thrift.enums.RemindTypeEnum.BILL_PAY_REMIND;
import static com.meituan.shangou.qnh.tenant.biz.client.thrift.enums.RemindTypeEnum.STORE_SERVICE_REMIND;

@RequestMapping("/pieapi/contract")
@RestController
@Slf4j
public class ContractRenewalController {

    private static final String CONTRACT_CATEGORY = "contract_renewal_confirm";
    private static final String TYPE_CONTRACT_RENEWAL = "contractRenewal";

    @Resource(name = "fnRedisClient")
    private RedisStoreClient fnRedisClient;

    @Autowired
    TenantThriftService tenantThriftService;

    @Autowired
    StoreServiceThriftService storeServiceThriftService;

    @Autowired
    PoiThriftService poiThriftService;

    @Autowired
    private ConfigThriftService configThriftService;

    @MethodDoc(
            displayName = "APP端合同提醒信息",
            description = "APP端合同提醒信息",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/contract/remindInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "APP端合同提醒信息")
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping("/remindInfo")
    public CommonResponse<Map<String, Object>> remindInfo(@RequestBody RemindRequest request) {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        Map<String, Object> resultMap = new HashMap<>();
        if (request.getTypes().contains(TYPE_CONTRACT_RENEWAL)) {
            ContractExpirationReminderRequest reminderRequest = new ContractExpirationReminderRequest();
            reminderRequest.setAccountId(user.getAccountId());
            reminderRequest.setTenantId(user.getTenantId());
            reminderRequest.setAccountType(user.getAccountType());

            ContractExpirationReminderResponse response = RpcInvoker.invoke(() -> tenantThriftService.queryTenantContractExpirationReminder(reminderRequest));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
            resultMap.put(TYPE_CONTRACT_RENEWAL, BeanMapperUtils.beanMapping(response, ContractExpirationReminderVo.class));
        }
        OptUserDto optUserDto = new OptUserDto();
        optUserDto.setTenantId(user.getTenantId());
        optUserDto.setAccountId(user.getAccountId());
        optUserDto.setAccountType(user.getAccountType());
        optUserDto.setAccountName(user.getAccountName());

        StoreServiceRemindRequest reminderRequest = new StoreServiceRemindRequest();
        reminderRequest.setOptUserDto(optUserDto);
        reminderRequest.setRemindTypes(request.getTypes());

        StoreServiceRemindResponse response = RpcInvoker.invokeReturn(() -> storeServiceThriftService.getStoreServiceExpireRemind(reminderRequest));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());

        if (request.getTypes().contains(STORE_SERVICE_REMIND.getType())) {
            resultMap.put(STORE_SERVICE_REMIND.getType(), BeanMapperUtils.beanMapping(response.getStoreServiceExpireRemindDto(), StoreServiceRemindVo.class));
        }
        if (request.getTypes().contains(BILL_PAY_REMIND.getType())) {
            resultMap.put(BILL_PAY_REMIND.getType(), BeanMapperUtils.beanMapping(response.getBillPayRemindDto(), BillPayRemindVo.class));
        }

        return CommonResponse.success(resultMap);


    }

    @MethodDoc(
            displayName = "APP端合同提醒确认",
            description = "APP端合同提醒确认",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/contract/remindConfirm",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "APP端合同提醒确认")
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping("/remindConfirm")
    public CommonResponse remindConfirm(@RequestBody @Validated RemindConfirmRequest request) {

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        ContractExpirationReminderRequest thriftRequest = new ContractExpirationReminderRequest();
        thriftRequest.setAccountId(user.getAccountId());
        thriftRequest.setTenantId(user.getTenantId());
        thriftRequest.setAccountType(user.getAccountType());
        thriftRequest.setConfirmType(request.getConfirmType());
        thriftRequest.setConfirmUser(user.getAccountName());
        thriftRequest.setUpdateUser(user.getAccountName());

        ContractExpirationReminderResponse response = tenantThriftService.saveTenantContractExpirationReminder(thriftRequest);
        if (!response.getStatus().isSuccess()) {
            return CommonResponse.fail(ResultCode.FAIL, response.getStatus().getMessage());
        }

        return CommonResponse.success(null);
    }

    @MethodDoc(
            displayName = "APP端门店服务期到期明细下载凭证获取",
            description = "APP端门店服务期到期明细下载凭证获取",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/contract/storeServicePeriod/token/get",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "APP端门店服务期到期明细下载凭证获取")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/storeServicePeriod/token/get", method = RequestMethod.POST)
    public CommonResponse<String> downloadStoreService() throws IOException {
        // 安全考虑，下载门店明细前先获取临时凭证
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        String token = UUID.randomUUID().toString().replaceAll("-", "");
        fnRedisClient.set(getStoreServiceKey(token), JSONObject.toJSONString(user), 120);
        return CommonResponse.success(token);
    }

    private StoreKey getStoreServiceKey(String token) {
        String redisKey = "storeService:" + token;
        return new StoreKey(CONTRACT_CATEGORY, redisKey);
    }

    @MethodDoc(
            displayName = "APP端门店服务期到期明细下载",
            description = "APP端门店服务期到期明细下载",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/contract/storeServicePeriod/download",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "APP端门店服务期到期明细下载")
//    @MethodLog(logResponse = true, logRequest = true)
    @GetMapping("/storeServicePeriod/download")
    public void downloadStoreService(String jsonParam,HttpServletRequest httpServletRequest,HttpServletResponse httpServletResponse) throws IOException {
        log.info("门店服务期下载参数:{}", jsonParam);
        AssertUtil.notBlank(jsonParam, "参数不能为空");
        jsonParam = URLDecoder.decode(jsonParam, "utf-8");
        jsonParam = jsonParam.replace("”", "\"");
        JSONObject requestParam = JSONObject.parseObject(jsonParam);
        String token = requestParam.getString("token");
        AssertUtil.notBlank(token, "下载凭证不能为空");

        StoreKey storeServiceKey = getStoreServiceKey(token);
        String value = fnRedisClient.get(storeServiceKey);
        AssertUtil.notNull(value, "下载凭证已过期,请重新下载");
        User user = JSONObject.parseObject(value, User.class);

        StoreServicePageRequest storeServiceListRequest = new StoreServicePageRequest();
        storeServiceListRequest.setTenantId(user.getTenantId());
        storeServiceListRequest.setServiceTypes(Collections.singletonList(ServiceTypeEnum.STORE_SERVICE.getType()));
        storeServiceListRequest.setStatusList(Arrays.asList(ServiceStatusEnum.VALID.getCode(), ServiceStatusEnum.INVALID.getCode()));
        OptUserDto optUserDto = new OptUserDto();
        optUserDto.setTenantId(user.getTenantId());
        optUserDto.setAccountId(user.getAccountId());
        optUserDto.setAccountType(user.getAccountType());
        optUserDto.setAccountName(user.getAccountName());

        StoreServiceListResponse response = RpcInvoker.invokeReturn(() -> storeServiceThriftService.queryAllStoreService(storeServiceListRequest, optUserDto));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());

        List<String> colNameList = new ArrayList<>(Arrays.asList("牵牛花门店ID", "牵牛花门店名称", "门店首次上线时间", "门店服务期到期时间", "门店服务期剩余天数"));

        if (hasErp(user.getTenantId())) {
            colNameList.add(0, "ERP门店ID");
        }
        HSSFWorkbook wb = ExcelUtil.exportExcelFile("门店服务期信息", colNameList, toExcelMapList(response.getStoreServiceList()));
        httpServletResponse.setContentType("application/vnd.ms-excel");
        String fileName = "门店服务期信息";
        String codedFileName = java.net.URLEncoder.encode(fileName, "UTF-8");
        // Content-disposition属性设置成以附件方式进行下载
        String agent = httpServletRequest.getHeader("USER-AGENT").toLowerCase();
        if (agent.contains("firefox")) {
            httpServletResponse.setCharacterEncoding(StandardCharsets.UTF_8.name());
            httpServletResponse.setHeader("content-disposition", "attachment;filename=" + new String(fileName.getBytes(), StandardCharsets.ISO_8859_1.name()) + ".xls");
        } else {
            httpServletResponse.setHeader("content-disposition", "attachment;filename=" + codedFileName + ".xls");
        }
        wb.write(httpServletResponse.getOutputStream());
    }

    @MethodDoc(
            displayName = "获取APP端合同的下载url",
            description = "获取APP端合同的下载url",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/contract/downloadUrl",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @RequestMapping(value = "/downloadUrl", method = RequestMethod.POST)
    public CommonResponse<ContractDownloadVo> downloadContract() throws IOException, InterruptedException {
        long currentUserTenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        ContractDownloadRequest contractDownloadRequest = new ContractDownloadRequest();
        contractDownloadRequest.setTenantId(currentUserTenantId);

        ContractDownloadResponse contractDownloadResponse = RpcInvoker.invoke(() -> tenantThriftService.contractDownload(contractDownloadRequest));
        ResponseHandler.checkResponseAndStatus(contractDownloadResponse, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        List<ContractDownloadDTO> electronContractAttachments = contractDownloadResponse.getElectronContractAttachments();
        if (CollectionUtils.isEmpty(electronContractAttachments)) {
            return CommonResponse.fail(9999, "合同附件不存在!");
        }

        return CommonResponse.success(BeanMapperUtils.beanMapping(electronContractAttachments.get(0), ContractDownloadVo.class));
    }

    private boolean hasErp(Long tenantId) {

        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(tenantId);
        configQueryRequest.setConfigId(ConfigItemEnum.HAS_ERP.getKey());
        TenantConfigResponse response = RpcInvoker.invoke(() -> configThriftService.queryTenantConfig(configQueryRequest));
        Object hasErp = JacksonUtils.fromJsonToMap(response.getConfig().getConfigContent()).get("hasErp");
        return "YES".equals(hasErp);
    }

    private List<HashMap<String, String>> toExcelMapList(List<StoreServiceDto> storeServiceList) {
        return storeServiceList.stream().map(dto -> {
            HashMap<String, String> excelMap = new HashMap<>();
            excelMap.put("ERP门店ID", dto.getSubjectAlias());
            excelMap.put("牵牛花门店ID", dto.getNewSubjectId());
            excelMap.put("牵牛花门店名称", dto.getSubjectName());
            List<List<String>> effectiveTimes = dto.getEffectiveTimes();
            excelMap.put("门店首次上线时间", effectiveTimes.get(0).get(0));
            excelMap.put("门店服务期到期时间", effectiveTimes.get(effectiveTimes.size() - 1).get(1));
            excelMap.put("门店服务期剩余天数", dto.getRemainDays() + "天");

            return excelMap;
        }).collect(Collectors.toList());
    }
}
