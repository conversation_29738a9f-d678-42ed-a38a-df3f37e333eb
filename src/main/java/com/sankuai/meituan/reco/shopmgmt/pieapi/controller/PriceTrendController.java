package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pricetrend.StoreSkuChannelPriceTrendBatchQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceWithPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSPriceTrendWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.*;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@InterfaceDoc(
        displayName = "价格趋势相关接口",
        type = "restful",
        scenarios = "查询门店商品价格趋势, 城市商品价格趋势",
        description = "查询门店商品价格趋势, 城市商品价格趋势, 主要包括线下价, 线上价, 基准价, 市调价"
)
@Auth
@Api(value = "价格趋势相关接口")
@RestController
@RequestMapping("/pieapi/priceTrend")
@Slf4j
public class PriceTrendController {

    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    @Resource
    private OCMSPriceTrendWrapper ocmsPriceTrendWrapper;

    @MethodDoc(
            displayName = "查询门店商品渠道价格趋势（包括线下, 线下渠道Id取值: -1）",
            description = "查询门店商品渠道价格趋势（包括线下, 线下渠道Id取值: -1）",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询门店商品渠道价格趋势请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            },
            returnValueDescription = "返回见ChannelSkuPriceWithPriceTrendVO",
            restExampleUrl = "/api/v1/priceTrend/queryStoreSkuChannelPriceTrend",
            restExamplePostData = "{\"channelId\":100,\"poiId\":1000033,\"skuIds\":[\"123\"]}",
            restExampleResponseData="{\"code\":0,\"msg\":\"成功\"}"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    @ApiOperation(value = "查询门店商品渠道价格趋势（包括线下, 线下渠道Id取值: -1）")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/batchQueryStoreSkuChannelPriceTrend", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<List<ChannelSkuPriceWithPriceTrendVO>> batchQueryStoreSkuChannelPriceTrend(@RequestBody StoreSkuChannelPriceTrendBatchQueryRequest request) {

        // 校验参数
        request.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        Long tenantId = user.getTenantId();
        Long storeId = request.getStoreId();
        Integer channelId = request.getChannelId();
        List<String> skuIds = request.getSkuIds();

        // 查询门店商品信息
        List<StoreSkuBaseDetailVO> storeSkuBaseDetailVOList =
                ocmsServiceWrapper.queryStoreSkuInfoBySkuIds(tenantId, storeId, skuIds, false);

        if (CollectionUtils.isEmpty(storeSkuBaseDetailVOList)) {
            throw new CommonLogicException("门店商品不存在", ResultCode.FAIL);
        }

        // 查询门店商品渠道价格趋势
        List<ChannelSkuPriceWithPriceTrendVO> channelSkuPriceWithPriceTrendVOList =
                ocmsPriceTrendWrapper.batchQueryStoreSkuChannelPriceTrend(tenantId, storeId, channelId, skuIds,
                        storeSkuBaseDetailVOList);

        return CommonResponse.success(channelSkuPriceWithPriceTrendVOList);
    }
}
