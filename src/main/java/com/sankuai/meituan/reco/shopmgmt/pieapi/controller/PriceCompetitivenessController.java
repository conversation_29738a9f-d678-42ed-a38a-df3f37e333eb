package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.ContrastStoreServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@InterfaceDoc(
        displayName = "价格门店竞争力相关接口",
        type = "restful",
        scenarios = "用于获取价格门店竞争力等能力.",
        description = "用于获取价格门店竞争力等能力",
        host = "https://pieapi-empower.shangou.meituan.com/"
)
@Api("价格门店竞争力相关接口")
@Auth
@Slf4j
@RestController
@RequestMapping("/pieapi/price/competitiveness")
public class PriceCompetitivenessController {
    @Resource
    private ContrastStoreServiceWrapper contrastStoreServiceWrapper;

    @MethodDoc(
            description = "竞对门店查询接口",
            displayName = "竞对门店查询接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "竞对门店查询接口请求",
                            type = ContrastStoreQueryRequestVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "",
            restExampleUrl = "/pieapi/price/competitiveness/contrast-stores/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/contrast-stores/query")
    @ResponseBody
    public CommonResponse<ContrastStoreQueryVO> queryContrastStore(@Valid @RequestBody ContrastStoreQueryRequestVO request) {
        return contrastStoreServiceWrapper.queryContrastStore(request);
    }

    @MethodDoc(
            description = "竞对门店搜索接口",
            displayName = "竞对门店搜索接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "竞对门店搜索接口请求",
                            type = ContrastStoreQueryRequestVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "",
            restExampleUrl = "/pieapi/price/competitiveness/contrast-stores/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/contrast-stores/search")
    @ResponseBody
    public CommonResponse<ContrastStoreSearchVO> searchContrastStore(@Valid @RequestBody ContrastStoreSearchRequestVO request) {
        return contrastStoreServiceWrapper.searchContrastStore(request);
    }


    @MethodDoc(
            description = "竞对门店关注更新接口",
            displayName = "竞对门店关注更新接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "竞对门店关注更新接口请求",
                            type = ContrastStoreQueryRequestVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "",
            restExampleUrl = "/pieapi/price/competitiveness/contrast-stores/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/contrast-stores/update")
    @ResponseBody
    public CommonResponse<Boolean> updateContrastStore(@Valid @RequestBody ContrastStoreUpdateRequestVO request) {
        return contrastStoreServiceWrapper.updateContrastStore(request);
    }

    @MethodDoc(
            description = "门店间类目对比接口",
            displayName = "门店间类目对比接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店间类目对比接口请求",
                            type = ContrastCategoryBetweenStoresRequestVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "",
            restExampleUrl = "/pieapi/price/competitiveness/query-category-indexes",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/query-category-indexes")
    @ResponseBody
    public CommonResponse<ContrastCategoryBetweenStoresVO> queryContrastCategoryBetweenStores(@Valid @RequestBody ContrastCategoryBetweenStoresRequestVO request) {
        return contrastStoreServiceWrapper.queryContrastCategoryBetweenStores(request);
    }

    @MethodDoc(
            description = "门店间商品对比接口",
            displayName = "门店间商品对比接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店间商品对比接口请求",
                            type = ContrastCategoryBetweenStoresRequestVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "",
            restExampleUrl = "/pieapi/price/competitiveness/query-spu-indexes",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/query-spu-indexes")
    @ResponseBody
    public CommonResponse<ContrastSpuBetweenStoresVO> queryContrastSpuBetweenStores(@Valid @RequestBody ContrastSpuBetweenStoresRequestVO request) {
        return contrastStoreServiceWrapper.queryContrastSpuBetweenStores(request);
    }

    @MethodDoc(
            description = "对比门店的渠道类目集合接口",
            displayName = "对比门店的渠道类目集合接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "对比门店的渠道类目集合请求",
                            type = ContrastCategoryBetweenStoresRequestVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "",
            restExampleUrl = "/pieapi/price/competitiveness/query-categories",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/query-categories")
    @ResponseBody
    public CommonResponse<ContrastChannelCategoryResponseVO> queryContrastChannelCategory(@Valid @RequestBody ContrastStoreCategoryRequestVO request) {
        return contrastStoreServiceWrapper.queryContrastCategory(request);
    }

}