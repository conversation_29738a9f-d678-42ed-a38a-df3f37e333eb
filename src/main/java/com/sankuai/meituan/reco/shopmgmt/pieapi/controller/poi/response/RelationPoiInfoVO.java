package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreDTO;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/1 20:17
 * @Description:
 */
@TypeDoc(
        name = "门店关联信息",
        description = "门店关联信息"
)
@Data
public class RelationPoiInfoVO {

    private String channelId;

    /**
     * 渠道类型 1-公有渠道 0-私有渠道 -1 - pos渠道
     */
    private Integer channelStandard;

    private Long channelAppId;

    private String channelName;

    private String channelPoiId;

    private String channelPoiName;

    private String channelPoiStatus;
    /**
     * 同步数据状态
     * 1、启用 2、停用
     */
    private int syncDataStatus;


    public static RelationPoiInfoVO poiInfoConvert(ChannelStoreDTO channelStoreDTO) {
        RelationPoiInfoVO relationPoiInfoVO = new RelationPoiInfoVO();
        relationPoiInfoVO.setChannelId(String.valueOf(channelStoreDTO.getChannel()));
        relationPoiInfoVO.setChannelName(ChannelTypeEnum.findChannelNameByChannelId(channelStoreDTO.getChannel()));
        relationPoiInfoVO.setChannelPoiId(channelStoreDTO.getChannelPoiCode());
        relationPoiInfoVO.setChannelPoiName(channelStoreDTO.getChannelPoiName());
        relationPoiInfoVO.setChannelPoiStatus(String.valueOf(channelStoreDTO.getChannelStatus()));
        relationPoiInfoVO.setSyncDataStatus(channelStoreDTO.getSyncDataStatus());
        relationPoiInfoVO.setChannelAppId(channelStoreDTO.getAppId());

        return relationPoiInfoVO;
    }
}
