package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.thrift.response.Status;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChainRelationEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.abnormal.AbnormalConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SkuPurchasePriceVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchDeleteStoreSpuApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.PicAuditApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ProblemSpuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QuerySpuRecommendDynamicInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QuerySpuRecommendDynamicInfoAndWeight;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QuerySpuRecommendTag;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryStoreSpuAbnormalRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.SaveStoreSpuApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuDetailApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuPageQueryApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuSpecListUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuSpecialtyUpdateApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.WhiteBackgroundImageGenRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.bo.ChannelSpuBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.BatchOperateSpuFailInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.BatchOperateSpuResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelParamVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalRuleNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.NoSaleChannelInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.PicAuditResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ProblemSpuQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ProblemSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ProductAbnormalVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.RecommendDynamicInfoAndWeightVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.RecommendDynamicInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.SaveStoreSpuPartSuccessResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreBusinessCategoryQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSkuCreateVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuCreateVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.WhiteBackgroundImageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion.ForbidFieldAndActivityVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion.StoreSkuActivityVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion.StoreSpuActivityAndForbidFieldsQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.SaveType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TenantPriceType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.item.AbnormalProductClient;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.CommonThreadPoolHolder;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.PurchaseSkuOperateThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.dto.PurchaseSkuPropertyDto;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.request.PurchaseSkuQueryReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.response.PurchaseSkuQueryResp;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.ChannelStoreFailDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.OperatorDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.common.response.GeneralResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.price.ChannelRetailPriceDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.strategy.SyncStrategyTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.price.CalculateSkuRetailPriceResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.PicAuditDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SpuTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.PicAuditTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.BatchDeleteStoreSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.PicAuditRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.RegionSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.RegionSpuTagSetRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.SaveStoreSpuRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.PicAuditResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.RegionSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.PicAuditThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.RegionSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.QueryTenantTagRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TagThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantFullTagCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagResponse;
import com.sankuai.meituan.shangou.empower.price.client.dto.SkuSpecInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.complex.InitChannelStoreSkuPriceInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.complex.InitSkuPriceInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.PriceSyncStrategyEnum;
import com.sankuai.meituan.shangou.empower.price.client.request.complex.InitPriceInfoForCreateSkuRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.complex.InitPriceInfoForFixValueRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.complex.InitPriceInfoForCreateSkuResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.complex.InitPriceInfoForFixValueResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.ComplexPriceThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalProductInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalSpuDetailDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuV2DTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PlatformSoldOutInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ProblemSpuDetailDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuAbnormalInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuNoSaleChannelInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuCompareTypeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.spu.StoreSpuDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ChannelAbnormalTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.AbnormalSpuDeleteRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchQueryPoiSpuAbnormalRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.CreateStoreSpuAndOnlineRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.NonErpStoreSpuPageQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryAbnormalSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryChannelSpuBySpuListRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuSpecialtyUpdateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.UpdateStoreSpuAndOnlineRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.WhiteBackgroundPicGenRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.diffcompare.QueryStoreSpuDiffCompareRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.BatchQuerySpuAbnormalResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.CreateStoreSpuAndOnlineResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryAbnormalSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryChannelSpuBySpuListResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryProblemSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.StoreSpuSpecialtyUpdateResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.UpdateStoreSpuAndOnlineResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.WhiteBackgroundPicGenResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.diffcompare.QueryStoreSpuDiffCompareResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.spu.NonErpPageQueryStoreSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.AbnormalProductBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ChannelSpuBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ImageThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProblemSpuThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreSpuBizThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelCatePropertyRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelRecommendCateQueryReq;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelRecommendDynamicInfoAndWeightReq;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelRecommendDynamicInfoReq;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelCatePropertyListAndWeightResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelCatePropertyListResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelCategoriesQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.service.ChannelCategoryThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.dto.ForbidFieldAndActivityDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.dto.StoreSkuActivityDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.request.StoreSpuActivityInfoQueryRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.response.StoreSpuActivityInfoQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.service.ChannelPromotionActivityThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.MtChannelStoreBusinessCategoryTypeEnum;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SpuActivityInfoQueryTypeEnum;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storechannelbusiness.dto.StoreChannelBusinessCategoryDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storechannelbusiness.request.StoreMtFirstBusinessCategoryRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storechannelbusiness.response.StoreMtFirstBusinessCategoryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storechannelbusiness.service.StoreChannelBusinessThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.tag.request.QueryTenantTagNameRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.tag.response.TenantTagNameResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.tag.service.TenantTagThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.MerchantSpuIdListCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PoiSpuIdListCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSpuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QueryMerchantSpuListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QueryPoiSpuListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMerchantSpuThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerPoiSpuThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.crm.data.client.enums.ResultCodeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.assertj.core.util.Preconditions;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


/**
 * @Title: OCMSProductServiceWrapper
 * @Description:
 * @Author: zhaolei12
 * @Date: 2020/5/16 11:51 上午
 */
@Service
@Slf4j
public class OCMSProductServiceWrapper {

    @Resource
    private StoreSpuThriftService storeSpuThriftService;

    @Resource
    private EmpowerMerchantSpuThriftService.Iface empowerMerchantSpuThriftService;

    @Resource(name = "newComplexPriceThriftService")
    private ComplexPriceThriftService complexPriceThriftService;

    @Resource
    private PicAuditThriftService picAuditThriftService;

    @Autowired
    private PoiThriftService poiThriftService;

    @Autowired
    private RegionSpuThriftService regionSpuThriftService;

    @Autowired
    private TagThriftService.Iface tagThriftService;

    @Resource
    private StoreSpuBizThriftService storeSpuBizThriftService;
    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private ProblemSpuThriftService problemSpuThriftService;
    @Resource
    private TenantTagThriftService tenantTagThriftService;
    @Resource
    private EmpowerPoiSpuThriftService.Iface empowerPoiSpuThriftService;
    @Resource
    private ChannelSpuBizThriftService channelSpuBizThriftService;

    @Resource
    private PriceConsistencyHelper priceConsistencyHelper;
    @Autowired
    private ImageThriftService imageThriftService;
    @Autowired
    private TenantWrapper tenantWrapper;
    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;

    @Resource
    private ChannelCategoryThriftService channelCategoryThriftService;

    @Autowired
    private ChannelSpuWrapper channelSpuWrapper;

    @Autowired
    private StoreChannelBusinessThriftService storeChannelBusinessThriftService;

    @Autowired
    private AbnormalProductClient abnormalProductClient;

    @Autowired
    private AbnormalProductBizThriftService abnormalProductBizThriftService;

    @Autowired
    private OCMSServiceWrapper ocmsServiceWrapper;
    @Autowired
    private ChannelPromotionActivityThriftService channelPromotionActivityThriftService;

    @Autowired
    @Qualifier("storeSpuQueryPurchaseThreadPool")
    private ExecutorService storeSpuQueryPurchaseThreadPool;

    @Autowired
    private PurchaseSkuOperateThriftService purchaseSkuOperateThriftService;


    private static final int BATCH_LIMIT_10_AMOUNT = 10;

    public CommonResponse<StoreSpuPageQueryResponseVO> pageQueryStoreSpu(StoreSpuPageQueryApiRequest request,
                                                                         User user,
                                                                         String boothId) {
        //查询租户开通渠道列表
        List<Integer> openChannels = tenantWrapper.queryChannelIds(user.getTenantId()).stream().filter(Objects::nonNull)
                .map(ChannelTypeEnumBo::getChannelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(openChannels)) {
            request.setChannelIds(openChannels);
        }
        boolean merchantChargeSpu = tenantWrapper.isMerchantChargeSpu(user.getTenantId());
        boolean isNotMerchantCharge = tenantWrapper.isNotMerchantChargeGray(user.getTenantId());
        boolean isStoreManagementTenant = tenantWrapper.isStoreManagementTenant(user.getTenantId());

        // 获取末级异常类型
        List<String> lastAbnormalCodes = null;
        if (CollectionUtils.isNotEmpty(request.getAbnormalCodes())) {
            try {
                Map<String, AbnormalRuleNodeVO> abnormalRuleMap = abnormalProductClient.getAbnormalRuleMap(null, null);
                // 找出所有叶子节点（含当前节点）
                lastAbnormalCodes = AbnormalProductClient.matchAbnormalCodes(request.getAbnormalCodes(), abnormalRuleMap);
            } catch (Exception e) {
                log.error("获取末级异常类型失败 code:{}", request.getAbnormalCodes());
            }
        }

        try {
            StoreSpuPageQueryResponseVO responseVO;
            Pair<StoreSpuPageQueryResponseVO, NonErpPageQueryStoreSpuResponse> pair = pageQueryStoreSpuFromProductbiz(request, lastAbnormalCodes, user, boothId, isStoreManagementTenant,
                    merchantChargeSpu, isNotMerchantCharge);
            responseVO = pair.getLeft();
            NonErpPageQueryStoreSpuResponse storeSpuResponse = pair.getRight();
            List<StoreSpuCompareTypeDTO> storeSpuCompareTypeDTOS = storeSpuResponse.getStoreSpuCompareTypeDTOList();

            try {
                priceConsistencyHelper.addPriceConsistencyAndPromotionInfo(user.getTenantId(), responseVO, request.getAsyncQueryPromotion());

                Map<Long, Map<String, List<Integer>>> diffCompareStoreSpuTypeMap = Optional.ofNullable(storeSpuCompareTypeDTOS).map(List::stream).orElse(Stream.empty())
                            .filter(Objects::nonNull)
                            .collect(Collectors.groupingBy(StoreSpuCompareTypeDTO::getStoreId, Collectors.groupingBy
                                    (StoreSpuCompareTypeDTO::getSpuId,
                                            Collectors.mapping(StoreSpuCompareTypeDTO::getCompareType, Collectors.toList()))));

                StoreSpuPageQueryResponseVO.addProductPropertyConsistencyInfo(responseVO.getStoreSpuList(),
                         diffCompareStoreSpuTypeMap);

                // 3.填充商品采购信息
                fillSkuPurchasePrice(responseVO, user.getTenantId());

                // 填充采购模式
                if (responseVO != null) {
                    fillPurchaseType(user.getTenantId(), responseVO.getStoreSpuList());
                }
            } catch (Exception e) {
                log.warn("补充商品和价格不一致数据异常", e);
            }
            return CommonResponse.success(responseVO);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> getPlatformSoldOutInfo(Long tenantId, List<StoreSpuDTO> storeSpuDTOList) {
        if (CollectionUtils.isEmpty(storeSpuDTOList)) {
            return Maps.newHashMap();
        }

        List<StoreSpuKeyDTO> queryStoreSpuKeyList = storeSpuDTOList.stream()
                .filter(storeSpuDTO -> CollectionUtils.isNotEmpty(storeSpuDTO.getChannelSpuList()))
                .map(StoreSpuDTO::getChannelSpuList)
                .flatMap(List::stream)
                .filter(channelSpuDTO -> channelSpuDTO.getChannelId().equals(ChannelTypeEnum.MEITUAN.getChannelId()) && channelSpuDTO.isPlatformSoldOut())
                .map(channelSpuDTO -> StoreSpuKeyDTO.builder().storeId(channelSpuDTO.getStoreId()).spuId(channelSpuDTO.getSpuId()).build())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryStoreSpuKeyList)) {
            return Maps.newHashMap();
        }
        QueryAbnormalSpuRequest spuRequest = null;
        try {
            spuRequest = QueryAbnormalSpuRequest.builder()
                    .tenantId(tenantId)
                    .channelId(ChannelTypeEnum.MEITUAN.getChannelId())
                    .abnormalType(ChannelAbnormalTypeEnum.PLATFORM_SOLD_OUT.getCode())
                    .storeSpuKeyList(queryStoreSpuKeyList)
                    .page(1)
                    .pageSize(queryStoreSpuKeyList.size())
                    .build();
            QueryAbnormalSpuResponse spuResponse = problemSpuThriftService.queryAbnormalInfoByStoreSpuList(spuRequest);
            return Optional.ofNullable(spuResponse.getAbnormalSpuDetailDTOS()).orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(detail -> StoreSpuKeyDTO.builder()
                            .storeId(detail.getStoreId()).spuId(detail.getSpuId()).build(), v -> v.getPlatformSoldOutInfo()));
        } catch (Exception e) {
            log.error("获取商品平台下架信息异常, request {}.", spuRequest, e);
        }

        return Maps.newHashMap();
    }

    /**
     * 从Productbiz查询门店商品信息
     */
    private Pair<StoreSpuPageQueryResponseVO, NonErpPageQueryStoreSpuResponse> pageQueryStoreSpuFromProductbiz(StoreSpuPageQueryApiRequest request,
                                                                        List<String> lastAbnormalCodes,
                                                                        User user,
                                                                        String boothId,
                                                                        boolean isStoreManagementTenant,
                                                                        boolean merchantChargeSpu,
                                                                        boolean isNotMerchantCharge) throws TException {
        NonErpStoreSpuPageQueryRequest rpcRequest = StoreSpuPageQueryApiRequest.toBizRpcRequest(request,lastAbnormalCodes, user, boothId,
                isStoreManagementTenant, merchantChargeSpu, isNotMerchantCharge);
        log.info("调用storeSpuThriftService.nonErpPageQueryStoreSpu(), request:{}, user:{}, boothId:{}", rpcRequest, user, boothId);
        NonErpPageQueryStoreSpuResponse response = storeSpuBizThriftService.nonErpPageQueryStoreSpu(rpcRequest);
        log.info("调用storeSpuThriftService.nonErpPageQueryStoreSpu(), response:{}", response);
        Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuAbnormalInfoMap = getPlatformSoldOutInfoForBiz(user.getTenantId(), response.getStoreSpuList());
        return Pair.of(StoreSpuPageQueryResponseVO.convertResponseForBiz(response, storeSpuAbnormalInfoMap), response);
    }

    private Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> getPlatformSoldOutInfoForBiz(Long tenantId, List<StoreSpuDto> storeSpuDTOList) {
        if (CollectionUtils.isEmpty(storeSpuDTOList)) {
            return Maps.newHashMap();
        }

        List<StoreSpuKeyDTO> queryStoreSpuKeyList = storeSpuDTOList.stream()
                .filter(storeSpuDTO -> CollectionUtils.isNotEmpty(storeSpuDTO.getChannelSpuList()))
                .map(StoreSpuDto::getChannelSpuList)
                .flatMap(List::stream)
                .filter(channelSpuDTO -> channelSpuDTO.getChannelId().equals(ChannelTypeEnum.MEITUAN.getChannelId()) && channelSpuDTO.isPlatformSoldOut())
                .map(channelSpuDTO -> StoreSpuKeyDTO.builder().storeId(channelSpuDTO.getStoreId()).spuId(channelSpuDTO.getSpuId()).build())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryStoreSpuKeyList)) {
            return Maps.newHashMap();
        }
        QueryAbnormalSpuRequest spuRequest = null;
        try {
            spuRequest = QueryAbnormalSpuRequest.builder()
                    .tenantId(tenantId)
                    .channelId(ChannelTypeEnum.MEITUAN.getChannelId())
                    .abnormalType(ChannelAbnormalTypeEnum.PLATFORM_SOLD_OUT.getCode())
                    .storeSpuKeyList(queryStoreSpuKeyList)
                    .page(1)
                    .pageSize(queryStoreSpuKeyList.size())
                    .build();
            QueryAbnormalSpuResponse spuResponse = problemSpuThriftService.queryAbnormalInfoByStoreSpuList(spuRequest);
            return Optional.ofNullable(spuResponse.getAbnormalSpuDetailDTOS()).orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(detail -> StoreSpuKeyDTO.builder()
                            .storeId(detail.getStoreId()).spuId(detail.getSpuId()).build(), v -> v.getPlatformSoldOutInfo()));
        } catch (Exception e) {
            log.error("获取商品平台下架信息异常, request {}.", spuRequest, e);
        }

        return Maps.newHashMap();
    }

    private Map<Long, Map<String, List<Integer>>> queryStoreSpuDiffCompareProblem(User user, List<StoreSpuVO> storeSpuVOS) {
        try {
            if (CollectionUtils.isEmpty(storeSpuVOS)) {
                return Maps.newHashMap();
            }

            List<StoreSpuKeyDTO> queryStoreSpuList = storeSpuVOS.stream()
                    .map(dto -> StoreSpuKeyDTO.builder().storeId(dto.getStore().getStoreId()).spuId(dto.getSpuId()).build())
                    .collect(Collectors.toList());
            QueryStoreSpuDiffCompareRequest request = QueryStoreSpuDiffCompareRequest.builder()
                    .tenantId(user.getTenantId())
                    .storeSpuList(queryStoreSpuList)
                    .build();
            QueryStoreSpuDiffCompareResponse response = problemSpuThriftService.queryStoreSpuDiffCompareTypeList(request);
            log.info("query store spu problem info, request [{}], response [{}].", request, response);
            return Optional.ofNullable(response.getCompareTypeDTOS()).map(List::stream).orElse(Stream.empty())
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(StoreSpuCompareTypeDTO::getStoreId, Collectors.groupingBy
                            (StoreSpuCompareTypeDTO::getSpuId,
                                    Collectors.mapping(StoreSpuCompareTypeDTO::getCompareType, Collectors.toList()))));
        } catch (Exception e) {
            log.warn("query store spu problem exception.", e);
        }

        return Maps.newHashMap();
    }

    private void fillSkuPurchasePrice(StoreSpuPageQueryResponseVO storeSpuPageQueryResponseVO, Long tenantId) {
        if (Objects.isNull(storeSpuPageQueryResponseVO) || CollectionUtils.isEmpty(storeSpuPageQueryResponseVO.getStoreSpuList())) {
            return;
        }

        fillSkuPurchasePrice(storeSpuPageQueryResponseVO.getStoreSpuList(), tenantId);
    }

    private void fillSkuPurchasePrice(List<StoreSpuVO> storeSpuVos, Long tenantId) {
        if (CollectionUtils.isEmpty(storeSpuVos)) {
            return;
        }

        List<StoreSkuVO> allSkuList = storeSpuVos.stream()
                .filter(spu -> CollectionUtils.isNotEmpty(spu.getStoreSkuList()))
                .flatMap(spu -> spu.getStoreSkuList().stream())
                .collect(Collectors.toList());

        // 获取门店商品采购价结果
        List<SkuPurchasePriceVo> result = executePurchasePriceQuery(allSkuList, tenantId);

        Map<Pair<Long, String>, SkuPurchasePriceVo> storeSkuPriceMap = ConverterUtils.listStreamMapToMap(result, p -> Pair.of(p.getStoreId(), p.getSkuId()), Function.identity());

        // 填充门店采购价
        allSkuList.forEach(sku -> {
            SkuPurchasePriceVo price = storeSkuPriceMap.get(Pair.of(sku.getStoreId(), sku.getSkuId()));
            if (price == null) {
                return;
            }
            sku.setIsComposeSku(price.getIsComposeSku() ? 1 : 0);
            sku.setLatestPurchasePrice(price.getLatestPurchasePrice());
            sku.setMasterPurchasePrice(price.getMasterPurchasePrice());
            sku.setWeightedInventoryCostPrice(price.getWeightedInventoryCostPrice());
            sku.setWeightedInventoryCostPriceUnit(price.getWeightedInventoryCostPriceUnit());
            sku.setLastDeliverPrice(price.getLastDeliverPrice());
            sku.setLastDeliverPriceUnit(price.getLastDeliverPriceUnit());
            sku.setBaseChannelGoodsPrice(price.getBaseChannelGoodsPrice());
            sku.setSupplierName(price.getSupplierName());
            sku.setLatestTime(price.getLatestTime());
        });
    }

    private List<SkuPurchasePriceVo> executePurchasePriceQuery(List<StoreSkuVO> allSkuList, Long tenantId) {
        Map<Long, List<String>> storeSkuIdsMap = allSkuList.stream()
                .collect(Collectors.groupingBy(StoreSkuVO::getStoreId, Collectors.mapping(StoreSkuVO::getSkuId, Collectors.toList())));

        List<Callable<List<SkuPurchasePriceVo>>> callableList = new ArrayList<>();

        storeSkuIdsMap.forEach((storeId, storeSkuIds) -> {
            Callable<List<SkuPurchasePriceVo>> queryCallable = () -> saasPriceServiceWrapper.querySkuPurchasePriceInfo(tenantId, storeId, storeSkuIds,true);
            callableList.add(queryCallable);
        });

        if (CollectionUtils.isEmpty(callableList)) {
            return Collections.emptyList();
        }

        List<SkuPurchasePriceVo> allPurchasePriceList = new ArrayList<>();

        try {
            // 若callableList数量为1, 则同步执行
            if (callableList.size() == 1) {
                List<SkuPurchasePriceVo> purchasePriceList = callableList.get(0).call();
                Optional.ofNullable(purchasePriceList).filter(CollectionUtils::isNotEmpty).ifPresent(allPurchasePriceList::addAll);
            } else {
                List<Future<List<SkuPurchasePriceVo>>> futureList = new ArrayList<>();
                callableList.forEach(callable -> {
                    Future<List<SkuPurchasePriceVo>> future = CommonThreadPoolHolder.getInstance().submit(callable);
                    futureList.add(future);
                });
                for (Future<List<SkuPurchasePriceVo>> future : futureList) {
                    List<SkuPurchasePriceVo> resultList = future.get();
                    Optional.ofNullable(resultList).filter(CollectionUtils::isNotEmpty).ifPresent(allPurchasePriceList::addAll);
                }
            }
        } catch (Exception e) {
            log.error("查询采购价异常 ", e);
        }
        return allPurchasePriceList;
    }

    private void fillPurchaseType(long tenantId, List<StoreSpuVO> storeSpuVoList) throws Exception {
        if (CollectionUtils.isEmpty(storeSpuVoList)) {
            return;
        }
        // 只有小松鼠需要填充采购模式
        ChainRelationEnum tenantChainRelationMode = tenantWrapper.getTenantChainRelationMode(tenantId);
        if (!ChainRelationEnum.FRANCHISOR_ROLE.equals(tenantChainRelationMode)) {
            return;
        }
        List<PurchaseSkuPropertyDto> purchaseSkuList = queryPurchaseSku(tenantId, storeSpuVoList);
        if (CollectionUtils.isEmpty(purchaseSkuList)) {
            return;
        }
        Map<Pair<Long, String>, PurchaseSkuPropertyDto> storeSku2PurchaseMap = Fun.toMapQuietly(purchaseSkuList,
                purchaseSku -> Pair.of(purchaseSku.getPoiId(), purchaseSku.getSkuId()));
        for (StoreSpuVO storeSpuVO : storeSpuVoList) {
            if (storeSpuVO == null || CollectionUtils.isEmpty(storeSpuVO.getStoreSkuList())) {
                continue;
            }
            for (StoreSkuVO storeSkuVO : storeSpuVO.getStoreSkuList()) {
                if (storeSkuVO == null) {
                    continue;
                }
                PurchaseSkuPropertyDto purchaseSku = storeSku2PurchaseMap.get(Pair.of(storeSkuVO.getStoreId(), storeSkuVO.getSkuId()));
                if (purchaseSku != null) {
                    storeSkuVO.setPurchaseType(purchaseSku.getPurchaseType());
                }
            }
        }
    }

    private List<PurchaseSkuPropertyDto> queryPurchaseSku(long tenantId, List<StoreSpuVO> storeSpuVoList) throws Exception {
        // TODO 等后续采购支持了批量接口后在此替换
        List<Callable<List<PurchaseSkuPropertyDto>>> taskList = storeSpuVoList.stream()
                .filter(Objects::nonNull)
                .filter(storeSpuVo -> storeSpuVo.getStore() != null)
                .map(storeSpuVO -> (Callable<List<PurchaseSkuPropertyDto>>)(() -> {
                    try {
                        PurchaseSkuQueryReq req = new PurchaseSkuQueryReq();
                        req.setTenantId(tenantId);
                        req.setSpuId(storeSpuVO.getSpuId());
                        req.setPoiId(storeSpuVO.getStore().getStoreId());
                        req.setHasComposeSku(true);
                        PurchaseSkuQueryResp resp = purchaseSkuOperateThriftService.queryPurchaseSku(req);
                        if (resp == null || resp.getStatus() == null ||
                                resp.getStatus().getCode() == null || resp.getStatus().getCode() != 0) {
                            log.error("queryPurchaseSku fail, resp:{}", JacksonUtils.toJson(resp));
                            return Collections.emptyList();
                        }
                        if (CollectionUtils.isEmpty(resp.getPurchaseSkuPropertyDtos())) {
                            return Collections.emptyList();
                        }
                        return resp.getPurchaseSkuPropertyDtos();
                    }
                    catch (Exception e) {
                        log.error("queryPurchaseSku fail", e);
                        return Collections.emptyList();
                    }
                })).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskList)) {
            return Collections.emptyList();
        }

        List<Future<List<PurchaseSkuPropertyDto>>> futureList = storeSpuQueryPurchaseThreadPool.invokeAll(taskList);
        List<PurchaseSkuPropertyDto> totalPurchaseSkuPropertyList = new ArrayList<>();
        for (Future<List<PurchaseSkuPropertyDto>> future : futureList) {
            List<PurchaseSkuPropertyDto> purchaseSkuList = future.get();
            if (CollectionUtils.isNotEmpty(purchaseSkuList)) {
                totalPurchaseSkuPropertyList.addAll(purchaseSkuList);
            }
        }
        return totalPurchaseSkuPropertyList;
    }

    public CommonResponse<StoreSpuVO> detailStoreSpu(StoreSpuDetailApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        StoreSpuDetailRequest rpcRequest = StoreSpuDetailApiRequest.toRpcRequest(request, user);
        log.info("OCMSProductServiceWrapper.detailStoreSpu(), 调用storeSpuThriftService.findStoreSpuDetail() request:{}",
                rpcRequest);

        try {
            StoreSpuDetailResponse storeSpuDetailResponse = storeSpuThriftService.findStoreSpuDetail(rpcRequest);
            log.info("OCMSProductServiceWrapper.detailStoreSpu(), 调用storeSpuThriftService.findStoreSpuDetail() response:{}",
                    storeSpuDetailResponse);
            if (storeSpuDetailResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                return new CommonResponse<>(storeSpuDetailResponse.getCode(), storeSpuDetailResponse.getMsg(), null);
            }

            Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuAbnormalInfoMap =
                    getPlatformSoldOutInfo(user.getTenantId(), Lists.newArrayList(storeSpuDetailResponse.getStoreSpu()));
            StoreSpuVO storeSpuVO = StoreSpuVO.ofDTO(storeSpuDetailResponse.getStoreSpu(),
                    storeSpuAbnormalInfoMap.get(StoreSpuKeyDTO.builder().storeId(request.getStoreId()).spuId(request.getSpuId()).build()));
            fillMultiChannelPriceTypeByStoreOpenChannel(storeSpuVO);
            // 填充京东渠道UPC是否校验信息
            filledJdCategoryPropertyInfo(storeSpuVO);
            // 填充不可售渠道信息列表
            fillNoSaleChannelInfoList(request, user, storeSpuDetailResponse, storeSpuVO);
            // 填充美团审核信息
//            fillChannelAuditComment(storeSpuVO);
            try {
                priceConsistencyHelper.addPriceConsistencyAndPromotionInfo(user.getTenantId(), storeSpuVO, request.getQuerySkuPromotion());

                // 查找当前商品是否存在不一致信息
                Map<Long, Map<String, List<Integer>>> diffCompareStoreSpuTypeMap =
                        queryStoreSpuDiffCompareProblem(user,  Lists.newArrayList(storeSpuVO));

                //查找当前商品是否存在平台停售信息
                Map<Integer, Integer> stopSellingStatusMap = queryStopSellingStatus(storeSpuVO);
                storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> channelSpuVO.getChannelId().equals(ChannelTypeEnum.MEITUAN.getChannelId()))
                        .forEach(channelSpuVO -> {
                            channelSpuVO.setStopSellingStatus(stopSellingStatusMap.get(channelSpuVO.getChannelId()));
                        });
                StoreSpuPageQueryResponseVO.addProductPropertyConsistencyInfo(Lists.newArrayList(storeSpuVO),
                         diffCompareStoreSpuTypeMap);
            } catch (Exception e) {
                log.warn("补充价格不一致数据异常", e);
            }

            if (tenantWrapper.isMerchantChargeSpu(user.getTenantId())) {
                MerchantSpuIdListCommand command = new MerchantSpuIdListCommand();
                command.setMerchantId(user.getTenantId());
                command.setSpuIds(Lists.newArrayList(request.getSpuId()));
                QueryMerchantSpuListResult merchantSpuListResult = empowerMerchantSpuThriftService.queryMerchantSpuByIds(command);
                if (CollectionUtils.isNotEmpty(merchantSpuListResult.getSpuList())) {
                    MerchantSpuInfo merchantSpuInfo = merchantSpuListResult.getSpuList().get(0);
                    if (merchantSpuInfo.getOnlineInfo() != null) {
                        storeSpuVO.setSellPoint(merchantSpuInfo.getOnlineInfo().getSellingPoint());
                        storeSpuVO.setDescription(merchantSpuInfo.getOnlineInfo().getDescription());
                    }
                }
            }

            try {
                fillSkuPurchasePrice(Lists.newArrayList(storeSpuVO), user.getTenantId());
            } catch (Exception e) {
                log.warn("填充采购信息异常", e);
            }

            try {
                fillPurchaseType(user.getTenantId(), Collections.singletonList(storeSpuVO));
            }
            catch (Exception e) {
                log.warn("填充采购模式异常", e);
            }
            return new CommonResponse(storeSpuDetailResponse.getCode(), storeSpuDetailResponse.getMsg(), storeSpuVO);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private void fillNoSaleChannelInfoList(StoreSpuDetailApiRequest request, User user, StoreSpuDetailResponse storeSpuDetailResponse, StoreSpuVO storeSpuVO) {
        try {
            StoreSpuDTO storeSpu = storeSpuDetailResponse.getStoreSpu();
            if (storeSpu == null || storeSpuVO == null){
                return;
            }
            StoreSpuKeyDTO storeSpuKeyDTO = StoreSpuKeyDTO.builder().storeId(request.getStoreId()).spuId(request.getSpuId()).build();
            List<SpuNoSaleChannelInfoDTO> spuNoSaleChannelInfoDTOList = abnormalProductClient.querySpuNoSaleChannelInfo(user.getTenantId(), Collections.singletonList(storeSpuKeyDTO), true, true);
            Map<StoreSpuKeyDTO, SpuNoSaleChannelInfoDTO> noSaleChannelInfoDTOMap = Fun.toMapQuietly(spuNoSaleChannelInfoDTOList, t -> StoreSpuKeyDTO.builder().storeId(t.getPoiId()).spuId(t.getSpuId()).build());
            SpuNoSaleChannelInfoDTO spuNoSaleChannelInfoDTO = noSaleChannelInfoDTOMap.get(storeSpuKeyDTO);
            if (spuNoSaleChannelInfoDTO != null){
                storeSpuVO.setNoSaleChannelInfoList(NoSaleChannelInfoVO.ofList(spuNoSaleChannelInfoDTO.getNoSaleChannelInfoList()));
            }
        }catch (Exception e){
            log.error("fillNoSaleChannelInfoList error request: {}, user: {}, storeSpuDetailResponse: {}", request, user, storeSpuDetailResponse);
        }
    }

    private Map<Integer, Integer> queryStopSellingStatus(StoreSpuVO storeSpuVO) {
        QueryAbnormalSpuRequest queryAbnormalSpuRequest = new QueryAbnormalSpuRequest();
        queryAbnormalSpuRequest.setTenantId(storeSpuVO.getTenantId());
        queryAbnormalSpuRequest.setStoreIds(Lists.newArrayList(storeSpuVO.getStore().getStoreId()));
        queryAbnormalSpuRequest.setSpuId(storeSpuVO.getSpuId());
        queryAbnormalSpuRequest.setChannelId(ChannelType.MEITUAN.getValue());
        QueryAbnormalSpuResponse queryAbnormalSpuResponse = problemSpuThriftService.queryStopSellingSpuBySpuId
                (queryAbnormalSpuRequest);
        log.info("query store spu abnormal info, request [{}], response [{}].", queryAbnormalSpuRequest,
                queryAbnormalSpuResponse);
        return Optional.ofNullable(queryAbnormalSpuResponse.getAbnormalSpuDetailDTOS()).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(AbnormalSpuDetailDTO::getChannelId, AbnormalSpuDetailDTO::getAbnormalStatus));
    }

    private void fillMultiChannelPriceTypeByStoreOpenChannel(StoreSpuVO storeSpuVO){
        if(Objects.isNull(storeSpuVO) || Objects.isNull(storeSpuVO.getStore())){
            return;
        }
        Long tenantId = storeSpuVO.getTenantId();
        Long storeId = storeSpuVO.getStore().getStoreId();
        if(Objects.isNull(storeId) || storeId <= 0L){
            return;
        }
        if(Objects.isNull(tenantId) || tenantId <= 0L){
            return;
        }
        List<ChannelTypeEnumBo> channelTypeEnums = tenantWrapper.queryTenantStoreChannelIds(tenantId, storeId);
        List<Integer> channelIdList = Fun.map(channelTypeEnums, ChannelTypeEnumBo::getChannelId);
        StoreSpuVO.fillMultiChannelPriceTypeByStoreOpenChannel(storeSpuVO, channelIdList);
    }

    private void filledJdCategoryPropertyInfo(StoreSpuVO storeSpuVO) {
        if (Objects.isNull(storeSpuVO) || CollectionUtils.isEmpty(storeSpuVO.getChannelSpuList())) {
            return;
        }

       Map<String, ChannelCategoryVO> categoryCodesMap = storeSpuVO.getChannelSpuList().stream()
               .filter(vo -> Objects.nonNull(vo) && Objects.nonNull(vo.getChannelId())
                       && vo.getChannelId() == ChannelTypeEnum.JD2HOME.getChannelId())
               .map(ChannelSpuVO::getChannelCategory)
               .filter(vo -> Objects.nonNull(vo) && StringUtils.isNotBlank(vo.getChannelCategoryCode()))
               .collect(Collectors.toMap(ChannelCategoryVO::getChannelCategoryCode, v -> v, (k1, k2) -> k2));
        if (MapUtils.isEmpty(categoryCodesMap)) {
            return;
        }

        ChannelCatePropertyRequest req = new ChannelCatePropertyRequest();
        req.setTenantId(storeSpuVO.getTenantId());
        req.setChannelId( ChannelTypeEnum.JD2HOME.getChannelId());
        req.setChannelCategoryIds(Lists.newArrayList(categoryCodesMap.keySet()));
        ChannelCatePropertyListResponse response = null;
        try {
            response = channelCategoryThriftService.queryPropertyList(req);
            log.info("获取京东类目属性, request {}, response {}.", req, response);

            if (CollectionUtils.isNotEmpty(response.getChannelCatePropertyList())) {
                response.getChannelCatePropertyList().forEach(dto -> {
                    if (categoryCodesMap.containsKey(dto.getChannelCategoryId())) {
                        categoryCodesMap.get(dto.getChannelCategoryId()).setCheckUpcStatus(dto.getCheckUpcStatus());
                    }
                });
            }
        }catch (Exception e){
            log.error("channelCategoryThriftService.queryPropertyList error. request:{}. reason:{}", req, e);
        }
    }

    public CommonResponse<SaveStoreSpuPartSuccessResponseVO> saveStoreOnlineSku(SaveStoreSpuApiRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();

        // 根据门店路由，将APP端保存门店商品迁移至productbiz
        // tenantWrapper.isCdqStoreMode目前这个方法会返回false 因为业态迁移了
        if (tenantWrapper.isCdqStoreMode(user.getTenantId())
                && !MccConfigUtil.isCdqSaveStoreSpuFromProductBiz(request.getStoreId())) {
            return saveStoreOnlineSkuForCdq(request, user);
        } else {
            return saveStoreOnlineSkuForNew(request, user);
        }
    }

    public CommonResponse<SaveStoreSpuPartSuccessResponseVO> saveStoreOnlineSkuForNew(SaveStoreSpuApiRequest request, User user) {
        try {
            boolean notMerchantChargeGray = tenantWrapper.isNotMerchantChargeGray(user.getTenantId());
            boolean isStoreManagementTenant = tenantWrapper.isStoreManagementTenant(user.getTenantId());
            boolean merchantCharge = tenantWrapper.isMerchantChargeSpu(user.getTenantId());

            if (SaveType.SAVE_TYPE.getValue() == request.getSaveType()) {
                //创建
                CreateStoreSpuAndOnlineRequest createStoreSpuAndOnlineRequest =
                        SaveStoreSpuApiRequest.toCreateStoreSpuAndOnlineRequest(request, user, notMerchantChargeGray, isStoreManagementTenant, merchantCharge);
                CreateStoreSpuAndOnlineResponse response = storeSpuBizThriftService.createStoreSpuAndOnline(createStoreSpuAndOnlineRequest);
                return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(),null);
            } else {
                UpdateStoreSpuAndOnlineRequest updateStoreSpuAndOnlineRequest =
                        SaveStoreSpuApiRequest.toUpdateStoreSpuAndOnlineRequest(request, user, notMerchantChargeGray, isStoreManagementTenant, merchantCharge);
                UpdateStoreSpuAndOnlineResponse response = storeSpuBizThriftService.updateStoreSpuAndOnline(updateStoreSpuAndOnlineRequest);
                return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(),null);
            }
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse<SaveStoreSpuPartSuccessResponseVO> saveStoreOnlineSkuForCdq(SaveStoreSpuApiRequest request, User user) {
        try {
            TenantPriceType tenantPriceType = tenantWrapper.queryTenantPriceConfig(request.getTenantId());
            Preconditions.checkNotNull(tenantPriceType);
            SaveStoreSpuRequest rpcRequest = SaveStoreSpuApiRequest.toRpcRequest(request, user);

            // 非标品重量校验失败
            if (!checkNonStandardWeight(rpcRequest.getStoreSpu())) {
                return new CommonResponse<>(ResponseCodeEnum.FAILED.getValue(), "当前商品是非标品，请选择重量不为0的规格", null);
            }
            boolean isZero = true;

            // 非手动定价需要补全进货价
            if (Objects.isNull(request.getPriceInitType()) || request.getPriceInitType() != SyncStrategyTypeEnum.FIX_PRICE.getValue()) {
                CommonResponse<Boolean> initResponse = installStorePriceAndReturnHasZeroPrice(rpcRequest, user);
                isZero = initResponse.getData();
                if (initResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                    return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "新建商品规格进货价/提价策略/零售价获取失败，请稍后重试", null);
                }
            } else {
                CommonResponse initResponse = initManualPriceAndStorePriceOldVersion(rpcRequest, request, user);
                if (initResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                    return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "新建商品规格手动定价失败，请稍后重试", null);
                }
            }

            CalculateSkuRetailPriceResponse response = storeSpuThriftService.saveStoreSpu(rpcRequest);
            return parseCalculateSkuRetailPriceResponse(response, isZero);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public CommonResponse initManualPriceAndStorePriceOldVersion(SaveStoreSpuRequest rpcRequest,
                                                                 SaveStoreSpuApiRequest request, User user) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(ResultCode.SUCCESS.getCode());
        InitPriceInfoForFixValueRequest priceRequest = new InitPriceInfoForFixValueRequest();
        priceRequest.setTenantId(user.getTenantId());
        priceRequest.setOperatorId(user.getEmployeeId());
        priceRequest.setOperatorName(user.getOperatorName());
        List<InitChannelStoreSkuPriceInfoDTO> channelStoreSkuPriceInfoDTOS = Lists.newArrayList();
        for (StoreSkuCreateVO storeSkuCreateVO : request.getStoreSpu().getStoreSkuList()) {
            InitChannelStoreSkuPriceInfoDTO storeSkuPriceInfoDTO = new InitChannelStoreSkuPriceInfoDTO();
            storeSkuPriceInfoDTO.setStoreId(request.getStoreId());
            storeSkuPriceInfoDTO.setStorePrice(0L);
            storeSkuPriceInfoDTO.setOnlinePrice((long) storeSkuCreateVO.getOnlinePrice());
            storeSkuPriceInfoDTO.setSkuId(storeSkuCreateVO.getSkuId());
            storeSkuPriceInfoDTO.setPriceSyncStrategy(PriceSyncStrategyEnum.FIX_PRICE);
            storeSkuPriceInfoDTO.setChannelType(com.sankuai.meituan.shangou.empower.price.client.enums.ChannelType.MEITUAN);
            channelStoreSkuPriceInfoDTOS.add(storeSkuPriceInfoDTO);
        }

        if (CollectionUtils.isEmpty(channelStoreSkuPriceInfoDTOS)) {
            return commonResponse;
        }

        priceRequest.setChannelStoreSkuPriceInfoDTOS(channelStoreSkuPriceInfoDTOS);
        try {
            InitPriceInfoForFixValueResponse response = complexPriceThriftService.initPriceInfoForFixValue(priceRequest);
            if (response.getCode() != ResultCode.SUCCESS.getCode() || MapUtils.isNotEmpty(response.getChannelStoreSkuErrMsgMap
                    ())) {
                log.error("手动定价初始化失败,response:{}", response);
                commonResponse.setCode(ResultCode.FAIL.getCode());
            }
        } catch (Exception e) {
            log.error("手动定价初始化异常,priceRequest:{}", priceRequest);
            commonResponse.setCode(ResultCode.FAIL.getCode());
        }

        StoreSpuDTO storeSpuDTO = rpcRequest.getStoreSpu();
        for (StoreSkuDTO storeSku : storeSpuDTO.getStoreSkuList()) {
            storeSku.setStorePrice(0d);
        }

        return commonResponse;
    }

    /**
     * 校验非标品重量，不能为0
     *
     * @param storeSpu
     * @return
     */
    private boolean checkNonStandardWeight(StoreSpuDTO storeSpu) {
        // 标品直接返回true
        if (storeSpu.getWeightType() == WeightTypeEnum.NONE.getCode()) {
            return true;
        }

        return storeSpu.getStoreSkuList().stream().noneMatch(sku ->
                (sku.getWeight() == null || sku.getWeight() == 0) && StringUtils.isBlank(sku.getWeightForUnit()));
    }

    /**
     * @param rpcRequest
     * @return
     */
    private CommonResponse<Boolean> installStorePriceAndReturnHasZeroPrice(SaveStoreSpuRequest rpcRequest, User user) {
        StoreSpuDTO storeSpuDTO = rpcRequest.getStoreSpu();
        boolean hasZeroPrice = false;

        CommonResponse<Map<String, Long>> commonResponse = calculateReferStorePrices(storeSpuDTO, rpcRequest.getSaveType() ==
                SaveType.SAVE_TYPE.getValue(), user);

        for (StoreSkuDTO storeSku : storeSpuDTO.getStoreSkuList()) {
            if (commonResponse.getData().containsKey(storeSku.getSkuId())) {
                Long storePrice = commonResponse.getData().get(storeSku.getSkuId());
                storeSku.setStorePrice(MoneyUtils.centToYuan(storePrice));
                if (storePrice != null && storePrice == 0) {
                    hasZeroPrice = true;
                }
            }
        }
        CommonResponse<Boolean> response = CommonResponse.success(hasZeroPrice);
        response.setCode(commonResponse.getCode());
        response.setMessage(commonResponse.getMessage());
        return response;
    }

    private CommonResponse<Map<String, Long>> calculateReferStorePrices(StoreSpuDTO storeSpuDTO, boolean isCreate, User user) {

        // 新增规格进行价格初始化
        List<SkuSpecInfoDTO> createSkuSpecInfoDTOList = storeSpuDTO.getStoreSkuList().stream()
                // 过滤出orgSku和sku不等的。（新增的sku，换绑也算新增）
                .filter(storeSku -> !storeSku.getSkuId().equals(storeSku.getOrgSkuId()))
                .map(storeSku -> {
                    SkuSpecInfoDTO skuSpecInfoDTO = new SkuSpecInfoDTO();
                    skuSpecInfoDTO.setSkuId(storeSku.getSkuId());
                    skuSpecInfoDTO.setWeightType(storeSpuDTO.getWeightType());
                    skuSpecInfoDTO.setWeight(storeSku.getWeight());
                    return skuSpecInfoDTO;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(createSkuSpecInfoDTOList)) {
            return CommonResponse.success(Maps.newHashMap());
        }

        InitPriceInfoForCreateSkuRequest request = new InitPriceInfoForCreateSkuRequest();
        request.setTenantId(storeSpuDTO.getTenantId());
        request.setStoreId(storeSpuDTO.getStore().getStoreId());
        // 目前只给菜大全用
        request.setChannelId(ChannelType.MEITUAN.getValue());
        request.setSpuId(storeSpuDTO.getSpuId());
        request.setSkuSpecInfoDTOS(createSkuSpecInfoDTOList);
        request.setIsCreateStoreSpu(isCreate);
        request.setOperatorId(user.getAccountId());
        request.setOperatorName(user.getOperatorName());

        try {
            InitPriceInfoForCreateSkuResponse response = complexPriceThriftService.initPriceInfoForCreateSku(request);
            log.info("新建门店商品规格初始化价格信息：进货价、零售价和定价策略结果, request:{}, response:{}", request, response);

            if (response.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                // 如果计算进货价接口失败，则进货设置为0。不影响后续流程
                return CommonResponse.fail(response.getCode(), response.getMsg(), storeSpuDTO.getStoreSkuList().stream()
                        // 过滤出orgSku和sku不等的。（新增的sku，换绑也算新增）
                        .filter(storeSku -> !storeSku.getSkuId().equals(storeSku.getOrgSkuId()))
                        .collect(Collectors.toMap(StoreSkuDTO::getSkuId, storeSku -> 0L, (k1, k2) -> k1)));
            } else {
                return CommonResponse.success(response.getInitSkuPriceInfoList().stream().collect(Collectors.toMap
                        (InitSkuPriceInfoDTO::getSkuId, InitSkuPriceInfoDTO::getStorePrice, (k1, k2) -> k2)));
            }
        } catch (Exception e) {
            log.error("新建门店商品规格初始化价格信息：进货价、零售价和定价策略异常, storeSpuDTO:{}", storeSpuDTO, e);
        }

        return CommonResponse.success(Maps.newHashMap());
    }

    private CommonResponse<SaveStoreSpuPartSuccessResponseVO> parseCalculateSkuRetailPriceResponse
            (CalculateSkuRetailPriceResponse response, boolean existZeroStorePrice) {
        CommonResponse<SaveStoreSpuPartSuccessResponseVO> commonResponse;
        // 部分失败代表保存门店商品成功，推送线上失败
        if (ResponseCodeEnum.SUCCESS_PARTITION.getValue() == response.getCode()) {
            SaveStoreSpuPartSuccessResponseVO partitionSuccessResponse = new SaveStoreSpuPartSuccessResponseVO();
            partitionSuccessResponse.setErrorCode(response.getCode());
            partitionSuccessResponse.setErrorMsg(response.getMsg());
            commonResponse = new CommonResponse<>(ResponseCodeEnum.SUCCESS.getValue(), "部分成功", partitionSuccessResponse);
        } else if (ResponseCodeEnum.UPDATE_STORE_BOOTH_SKU_ERROR.getValue() == response.getCode()) {
            // 更新摊位信息失败
            SaveStoreSpuPartSuccessResponseVO updateStoreBoothSkuFailResponse = new SaveStoreSpuPartSuccessResponseVO();
            updateStoreBoothSkuFailResponse.setErrorCode(response.getCode());
            updateStoreBoothSkuFailResponse.setErrorMsg(response.getMsg());
            commonResponse = new CommonResponse<>(ResponseCodeEnum.SUCCESS.getValue(), "更新摊位信息失败",
                    updateStoreBoothSkuFailResponse);
        } else {
            commonResponse = new CommonResponse<>(response.getCode(), response.getMsg(), null);
        }

        // 有0价格需要返回对应的Code（EXIST_ZERO_PRICE）
        if (existZeroStorePrice || existZeroRetailPrice(response.getChannelRetailPrices())) {
            SaveStoreSpuPartSuccessResponseVO partSuccessResponse = commonResponse.getData();
            if (partSuccessResponse == null) {
                partSuccessResponse = new SaveStoreSpuPartSuccessResponseVO();
                commonResponse.setData(partSuccessResponse);
            }

            partSuccessResponse.setHasZeroPrice(true);
        }
        return commonResponse;
    }

    private boolean existZeroRetailPrice(List<ChannelRetailPriceDTO> channelRetailPriceDTOS) {
        return CollectionUtils.isNotEmpty(channelRetailPriceDTOS) && channelRetailPriceDTOS.stream()
                .anyMatch(item -> item.getRetailPrice() == null || item.getRetailPrice() == 0);
    }

    public CommonResponse<PicAuditResultVO> auditPic(PicAuditApiRequest request, User user) {
        try {
            PicAuditRequest picAuditRequest = new PicAuditRequest();
            picAuditRequest.setTenantId(user.getTenantId());
            picAuditRequest.setImgUrl(request.getImgUrl());
            picAuditRequest.setAuditTypeList(Lists.newArrayList(PicAuditTypeEnum.WHITE_BACK));

            PicAuditResponse response = picAuditThriftService.audit(picAuditRequest);
            log.info("商品图片白底检测结果, request:{}, user:{}, response:{}", request, user, response);

            if (response.getCode() == ResponseCodeEnum.SUCCESS.getValue() && CollectionUtils.isNotEmpty(response.getAuditInfo()
            )) {
                List<PicAuditDTO> auditInfoList = response.getAuditInfo();
                return CommonResponse.success(PicAuditResultVO.of(auditInfoList.get(0).isAuditStatus()));
            }

            return CommonResponse.fail(response.getCode(), response.getMsg(), PicAuditResultVO.of(true));
        } catch (TException e) {
            throw new CommonRuntimeException();
        }
    }

    public CommonResponse<WhiteBackgroundImageVO> genWhiteBackgroundImage(WhiteBackgroundImageGenRequest request,
                                                                          User user) {
        WhiteBackgroundPicGenRequest genRequest = new WhiteBackgroundPicGenRequest();
        genRequest.setTenantId(user.getTenantId());
        genRequest.setImgUrl(request.getImgUrl());
        genRequest.setWidth(request.getWidth());
        genRequest.setHeight(request.getHeight());
        try {
            WhiteBackgroundPicGenResponse response = imageThriftService.genWhiteBackgroundPic(genRequest);
            log.info("ImageThriftService.genWhiteBackgroundPic, request:{}, response:{}", request, response);

            Status status = response.getStatus();
            if (status.getCode() != ResultCode.SUCCESS.getCode()) {
                return CommonResponse.fail(status.getCode(), status.getMsg());
            }
            return CommonResponse.success(new WhiteBackgroundImageVO(response.getImgUrl()));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<SaveStoreSpuPartSuccessResponseVO> updateStoreSpuSpecList(StoreSpuSpecListUpdateRequest request, User
            user) {
        try {
            // 查询商品详情补全门店商品信息
            StoreSpuDetailRequest storeSpuDetailRequest = request.toStoreSpuDetailRequest();
            log.info("storeSpuThriftService.findStoreSpuDetail request:{}", request);
            StoreSpuDetailResponse storeSpuDetailResponse = storeSpuThriftService.findStoreSpuDetail(storeSpuDetailRequest);
            log.info("storeSpuThriftService.findStoreSpuDetail response:{}", storeSpuDetailResponse);

            if (storeSpuDetailResponse == null || storeSpuDetailResponse.getStoreSpu() == null) {
                throw new CommonRuntimeException("获取门店商品信息失败");
            }

            // 总部管品or饿了么非总部管品多渠道租户，填充默认价格，后续这个改规格入口会下掉
            boolean merchantChargeSpu = tenantWrapper.isMerchantChargeSpu(user.getTenantId());
            boolean isStoreManagementTenant = tenantWrapper.isStoreManagementTenant(user.getTenantId());
            if (merchantChargeSpu || isStoreManagementTenant) {
                fillDefaultOnlinePriceForMerchantCharge(request.getSkuList(), storeSpuDetailResponse);
            }
            // 构造保存门店商品请求
            SaveStoreSpuRequest saveStoreSpuRequest = buildSaveStoreSpuRequest(request, storeSpuDetailResponse.getStoreSpu(),
                    user);

            // 非标品重量校验失败
            if (!checkNonStandardWeight(saveStoreSpuRequest.getStoreSpu())) {
                return new CommonResponse<>(ResponseCodeEnum.FAILED.getValue(), "当前商品是非标品，请选择重量不为0的规格", null);
            }

            // 执行价格策略初始化
            boolean existZeroStorePrice;
            if (merchantChargeSpu || isStoreManagementTenant) {
                SaveStoreSpuApiRequest saveStoreSpuApiRequest = new SaveStoreSpuApiRequest();
                saveStoreSpuApiRequest.setTenantId(user.getTenantId());
                saveStoreSpuApiRequest.setStoreId(request.getStoreId());
                StoreSpuCreateVO storeSpuCreateVO = new StoreSpuCreateVO();
                storeSpuCreateVO.setSpuId(request.getSpuId());
                storeSpuCreateVO.setOrgStoreSkuList(request.getOrgSkuList());
                storeSpuCreateVO.setStoreSkuList(request.getSkuList());
                saveStoreSpuApiRequest.setStoreSpu(storeSpuCreateVO);
                initManualPriceAndStorePriceForMerchantCharge(saveStoreSpuRequest, saveStoreSpuApiRequest, user);
                existZeroStorePrice = false;
            } else {
                // 需要补全进货价
                CommonResponse<Boolean> initResponse = installStorePriceAndReturnHasZeroPrice(saveStoreSpuRequest, user);
                if (initResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                    return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "新建商品规格进货价/提价策略/零售价获取失败，请稍后重试", null);
                }
                existZeroStorePrice = initResponse.getData();
            }
            log.info("storeSpuThriftService.saveStoreSpu saveStoreSpuRequest:{}", saveStoreSpuRequest);
            CalculateSkuRetailPriceResponse response = storeSpuThriftService.saveStoreSpu(saveStoreSpuRequest);
            log.info("storeSpuThriftService.saveStoreSpu CalculateSkuRetailPriceResponse:{}", response);
            return parseCalculateSkuRetailPriceResponse(response, existZeroStorePrice);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private void fillDefaultOnlinePriceForMerchantCharge(List<StoreSkuCreateVO> skuList, StoreSpuDetailResponse
            storeSpuDetailResponse) {
        if (CollectionUtils.isEmpty(skuList) || storeSpuDetailResponse == null) {
            return;
        }

        StoreSpuVO storeSpuVO = StoreSpuVO.ofDTO(storeSpuDetailResponse.getStoreSpu(), null);
        Map<String, StoreSkuVO> existsStoreSkuMap = storeSpuVO.getStoreSkuList().stream().collect(Collectors.toMap
                (StoreSkuVO::getSkuId, Function.identity(), (o1, o2) -> o2));
        List<ChannelPriceVO> defaultChannelOnlinePriceList = Lists.newArrayList();
        ChannelPriceVO mtPrice = new ChannelPriceVO();
        mtPrice.setChannelId(ChannelType.MEITUAN.getValue());
        mtPrice.setOnlinePrice("9999");
        defaultChannelOnlinePriceList.add(mtPrice);
        ChannelPriceVO elemPrice = new ChannelPriceVO();
        elemPrice.setChannelId(ChannelType.ELEM.getValue());
        elemPrice.setOnlinePrice("9999");
        defaultChannelOnlinePriceList.add(elemPrice);

        ChannelPriceVO jdPrice = new ChannelPriceVO();
        jdPrice.setChannelId(ChannelType.JD2HOME.getValue());
        jdPrice.setOnlinePrice("9999");
        defaultChannelOnlinePriceList.add(jdPrice);

        skuList.forEach(inputStoreSkuCreateVO -> {
            if (existsStoreSkuMap.containsKey(inputStoreSkuCreateVO.getSkuId())) {
                StoreSkuVO existsStoreSkuVO = existsStoreSkuMap.get(inputStoreSkuCreateVO.getSkuId());
                inputStoreSkuCreateVO.setChannelPriceType(existsStoreSkuVO.getChannelPriceType());
                inputStoreSkuCreateVO.setChannelOnlinePrice(existsStoreSkuVO.getChannelOnlinePrice());
                inputStoreSkuCreateVO.setChannelOnlinePriceList(existsStoreSkuVO.getChannelOnlinePriceList());
            } else {
                inputStoreSkuCreateVO.setChannelPriceType(1);
                inputStoreSkuCreateVO.setChannelOnlinePrice("9999");
                inputStoreSkuCreateVO.setChannelOnlinePriceList(defaultChannelOnlinePriceList);
            }
        });

    }

    private SaveStoreSpuRequest buildSaveStoreSpuRequest(StoreSpuSpecListUpdateRequest request, StoreSpuDTO storeSpu, User user) {
        SaveStoreSpuRequest saveStoreSpuRequest = new SaveStoreSpuRequest();
        saveStoreSpuRequest.setTenantId(user.getTenantId());
        saveStoreSpuRequest.setOperatorId(user.getAccountId());
        saveStoreSpuRequest.setOperatorName(user.getOperatorName());
        saveStoreSpuRequest.setOperateSource(OperateSourceEnum.EMPOWER_ASSISTENT_APP);
        saveStoreSpuRequest.setStoreId(request.getStoreId());
        saveStoreSpuRequest.setSaveType(SaveType.EDIT_TYPE.getValue());
        saveStoreSpuRequest.setInfiniteInventory(request.getInfiniteInventory());

        // 换规格参数转换
        List<StoreSkuDTO> storeSkuDTOList = StoreSpuCreateVO.compareAndConvertToStoreSkuDTOS(request.getOrgSkuList(), request
                .getSkuList());
        storeSpu.setStoreSkuList(storeSkuDTOList);
        saveStoreSpuRequest.setStoreSpu(storeSpu);

        return saveStoreSpuRequest;
    }


    /**
     * 增加易差评标签.
     *
     * @param tenantId 租户ID
     * @param poiId    门店ID
     * @param spuIds   SPUID
     */
    public CommonResponse addBadCaseTag(long tenantId, long poiId, List<String> spuIds, long operatorId, String operatorName) {
        try {
            PoiMapResponse poiMapResponse = poiThriftService.queryTenantPoiInfoMapByPoiIds(Arrays.asList(poiId), tenantId);

            PoiInfoDto poiInfoDto = poiMapResponse.getOne(poiId);
            if (poiInfoDto == null) {
                throw new CommonRuntimeException("门店不存在");
            }

            List<RegionSpuDTO> regionSpuDtos = getRegionSpu(tenantId, poiInfoDto.getDistrict().getCityId().longValue(), spuIds);

            Long badCaseTagId = getBadCaseTagId(tenantId);

            for (RegionSpuDTO regionSpuDto : regionSpuDtos) {
                addSpuTags(tenantId, poiInfoDto.getDistrict().getCityId().longValue(), regionSpuDto, badCaseTagId, operatorId,
                        operatorName);
            }

            return CommonResponse.success(null);
        } catch (Exception e) {
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), e.getMessage());
        }

    }


    private List<RegionSpuDTO> getRegionSpu(long tenantId, long regionId, List<String> spuIds) throws TException {
        List<RegionSpuDTO> regionSpuDtoList = Lists.newArrayList();

        List<List<String>> spuIdPartitions = Lists.partition(spuIds, 50);
        for (List<String> spuIdPartition : spuIdPartitions) {
            List<RegionSpuDTO> regionSpuList = getRegionSpuFromOcms(tenantId, regionId, spuIdPartition);
            if (CollectionUtils.isNotEmpty(regionSpuList)) {
                regionSpuDtoList.addAll(regionSpuList);
            }
        }

        return regionSpuDtoList;
    }


    private List<RegionSpuDTO> getRegionSpuFromOcms(long tenantId, long regionId, List<String> spuIds) throws TException {
        RegionSpuDetailRequest request = new RegionSpuDetailRequest();
        request.setTenantId(tenantId);
        request.setSpuIds(spuIds);
        request.setRegionId(regionId);
        RegionSpuDetailResponse regionSpuDetail = regionSpuThriftService.findRegionSpuDetail(request);

        log.info("response:{}", regionSpuDetail);
        if (regionSpuDetail.getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException("查询商品服务失败," + regionSpuDetail.getMsg());
        }

        return regionSpuDetail.getRegionSpus();
    }

    private Long getBadCaseTagId(long tenantId) throws TException {
        QueryTenantTagRequest tagRequest = new QueryTenantTagRequest();
        tagRequest.setTenantId(tenantId);
        TenantTagResponse tenantTagResponse = tagThriftService.queryTags(tagRequest);
        if (CollectionUtils.isEmpty(tenantTagResponse.getData())) {
            throw new CommonRuntimeException("不存在任何标签");
        }

        Long badCaseTagId = tenantTagResponse.getData().stream().map(TenantFullTagCategoryDTO::getTagList)
                .flatMap(List::stream).filter(t -> MccConfigUtil.getBadCaseTagName().equals(t.getName())).findFirst()
                .map(TenantTagDTO::getId).orElse(null);

        if (badCaseTagId == null) {
            throw new CommonRuntimeException("没有找到\"" + MccConfigUtil.getBadCaseTagName() + "\"标签");
        }

        return badCaseTagId;
    }


    private void addSpuTags(long tenantId, long regionId, RegionSpuDTO regionSpuDto, Long badCaseTagId, long operatorId,
                            String operatorName) throws TException {
        List<Long> tagIds = ConverterUtils.convertList(regionSpuDto.getTags(), SpuTagDTO::getTagId);
        if (tagIds.contains(badCaseTagId)) {
            return;
        }

        tagIds.add(badCaseTagId);

        RegionSpuTagSetRequest setTagRequest = new RegionSpuTagSetRequest();
        setTagRequest.setTenantId(tenantId);
        setTagRequest.setSpuId(regionSpuDto.getSpuId());
        setTagRequest.setTagIds(tagIds);
        setTagRequest.setRegionId(regionId);
        setTagRequest.setOperator(new OperatorDTO(operatorId, operatorName));
        regionSpuThriftService.setRegionSpuTag(setTagRequest);
    }


    public CommonResponse updateStoreSpuSpecialty(StoreSpuSpecialtyUpdateApiRequest request, User user) {

        try {
            // 构建参数
            StoreSpuSpecialtyUpdateRequest rpcRequest = StoreSpuSpecialtyUpdateApiRequest.toRpcRequest(request, user);

            // 更新门店商品`力荐`
            StoreSpuSpecialtyUpdateResponse rpcResponse = storeSpuBizThriftService.updateStoreSpuSpecialty(rpcRequest);
            log.info("StoreSpuBizThriftService updateStoreSpuSpecialty, request:{}, response:{}", rpcRequest, rpcResponse);

            // 解析结果
            if (rpcResponse.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), rpcResponse.getStatus().getMsg(), null);
            }

            return CommonResponse.success(null);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse initManualPriceAndStorePrice(SaveStoreSpuRequest rpcRequest, SaveStoreSpuApiRequest request, User
            user) {
        if (tenantWrapper.isMerchantChargeSpu(request.getTenantId())
                || tenantWrapper.isStoreManagementTenant(request.getTenantId())) {
            return initManualPriceAndStorePriceForMerchantCharge(rpcRequest, request, user);
        }
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(ResultCode.SUCCESS.getCode());
        InitPriceInfoForFixValueRequest priceRequest = new InitPriceInfoForFixValueRequest();
        priceRequest.setTenantId(user.getTenantId());
        priceRequest.setOperatorId(user.getEmployeeId());
        priceRequest.setOperatorName(user.getOperatorName());
        List<InitChannelStoreSkuPriceInfoDTO> channelStoreSkuPriceInfoDTOS = Lists.newArrayList();
        try {
            Map<@NotNull String, StoreSkuCreateVO> skuCreateVOMap = Optional.ofNullable(request.getStoreSpu())
                    .map(StoreSpuCreateVO::getStoreSkuList)
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .collect(Collectors.toMap(StoreSkuCreateVO::getSkuId, Function.identity()));

            for (StoreSkuDTO storeSkuDTO : rpcRequest.getStoreSpu().getStoreSkuList()) {
                //所有新增规格  需要定价的
                if (Objects.equals(storeSkuDTO.getOrgSkuId(), storeSkuDTO.getSkuId())) {
                    continue;
                }
                //手动定价新增规格的进货价设置为0；
                storeSkuDTO.setStorePrice(0D);
                StoreSkuCreateVO skuCreateVO = skuCreateVOMap.get(storeSkuDTO.getSkuId());

                //如果没填零售价，这里不进行零售价的初始化。
                if (Objects.isNull(skuCreateVO.getOnlinePrice())) {
                    continue;
                }
                InitChannelStoreSkuPriceInfoDTO storeSkuPriceInfoDTO = new InitChannelStoreSkuPriceInfoDTO();
                storeSkuPriceInfoDTO.setStoreId(request.getStoreId());
                storeSkuPriceInfoDTO.setStorePrice(0L);
                storeSkuPriceInfoDTO.setOnlinePrice(skuCreateVO.getOnlinePrice().longValue());
                storeSkuPriceInfoDTO.setSkuId(storeSkuDTO.getSkuId());
                storeSkuPriceInfoDTO.setPriceSyncStrategy(PriceSyncStrategyEnum.FIX_PRICE);
                storeSkuPriceInfoDTO.setChannelType(com.sankuai.meituan.shangou.empower.price.client.enums.ChannelType.MEITUAN);
                channelStoreSkuPriceInfoDTOS.add(storeSkuPriceInfoDTO);

            }
            priceRequest.setChannelStoreSkuPriceInfoDTOS(channelStoreSkuPriceInfoDTOS);
            if (CollectionUtils.isEmpty(channelStoreSkuPriceInfoDTOS)) {
                return commonResponse;
            }
            InitPriceInfoForFixValueResponse response = complexPriceThriftService.initPriceInfoForFixValue(priceRequest);
            if (response.getCode() != ResultCode.SUCCESS.getCode() || MapUtils.isNotEmpty(response.getChannelStoreSkuErrMsgMap
                    ())) {
                log.error("手动定价初始化失败,response:{}", response);
                commonResponse.setCode(ResultCode.FAIL.getCode());
            }
        } catch (Exception e) {
            log.error("手动定价初始化异常,priceRequest:{}", priceRequest);
            commonResponse.setCode(ResultCode.FAIL.getCode());
        }

        return commonResponse;
    }

    private CommonResponse initManualPriceAndStorePriceForMerchantCharge(SaveStoreSpuRequest rpcRequest, SaveStoreSpuApiRequest
            request, User user) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setCode(ResultCode.SUCCESS.getCode());
        try {
            List<ChannelTypeEnumBo> channelTypeEnums = tenantWrapper.queryTenantStoreChannelIds(request.getTenantId(), request
                    .getStoreId());
            if (CollectionUtils.isEmpty(channelTypeEnums)) {
                return commonResponse;
            }
            Set<Integer> channelIdSet = channelTypeEnums.stream().map(ChannelTypeEnumBo::getChannelId).collect(Collectors.toSet());
            Map<@NotNull String, StoreSkuCreateVO> skuCreateVOMap = Optional.ofNullable(request.getStoreSpu())
                    .map(StoreSpuCreateVO::getStoreSkuList)
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .collect(Collectors.toMap(StoreSkuCreateVO::getSkuId, Function.identity()));
            Map<String, ChannelSkuDTO> mtChannelSkuMap = queryExistsChannelSkuMap(rpcRequest, ChannelType.MEITUAN.getValue());
            Map<String, ChannelSkuDTO> elemChannelSkuMap = queryExistsChannelSkuMap(rpcRequest, ChannelType.ELEM.getValue());
            Map<String, ChannelSkuDTO> jdChannelSkuMap = queryExistsChannelSkuMap(rpcRequest, ChannelType.JD2HOME.getValue());
            Map<String, ChannelSkuDTO> yzChannelSkuMap = queryExistsChannelSkuMap(rpcRequest, ChannelType.YOU_ZAN.getValue());
            List<InitChannelStoreSkuPriceInfoDTO> channelSyncStrategyList = Lists.newArrayList();
            for (StoreSkuDTO storeSkuDTO : rpcRequest.getStoreSpu().getStoreSkuList()) {
                // 多渠道设置默认进货价
                storeSkuDTO.setStorePrice(9999D);
                StoreSkuCreateVO skuCreateVO = skuCreateVOMap.get(storeSkuDTO.getSkuId());
                // 价格list有值是多渠道
                if (CollectionUtils.isNotEmpty(skuCreateVO.getChannelOnlinePriceList())) {
                    skuCreateVO.getChannelOnlinePriceList().forEach(price -> {
                        if (!channelIdSet.contains(price.getChannelId())) {
                            return;
                        }
                        InitChannelStoreSkuPriceInfoDTO storeSkuPriceInfoDTO = new InitChannelStoreSkuPriceInfoDTO();
                        if (price.getChannelId() == ChannelType.MEITUAN.getValue()) {
                            if (mtChannelSkuMap.containsKey(storeSkuDTO.getSkuId())) {
                                return;
                            } else {
                                storeSkuPriceInfoDTO.setOnlinePrice(MoneyUtils.yuanToCent(price.getOnlinePrice()));
                            }
                        } else if (price.getChannelId() == ChannelType.ELEM.getValue()) {
                            if (elemChannelSkuMap.containsKey(storeSkuDTO.getSkuId())) {
                                return;
                            } else {
                                storeSkuPriceInfoDTO.setOnlinePrice(MoneyUtils.yuanToCent(price.getOnlinePrice()));
                            }
                        } else if (price.getChannelId() == ChannelType.JD2HOME.getValue()) {
                            if (jdChannelSkuMap.containsKey(storeSkuDTO.getSkuId())) {
                                return;
                            } else {
                                storeSkuPriceInfoDTO.setOnlinePrice(MoneyUtils.yuanToCent(price.getOnlinePrice()));
                            }
                        } else if (price.getChannelId() == ChannelType.YOU_ZAN.getValue()) {
                            if (yzChannelSkuMap.containsKey(storeSkuDTO.getSkuId())) {
                                return;
                            } else {
                                storeSkuPriceInfoDTO.setOnlinePrice(MoneyUtils.yuanToCent(price.getOnlinePrice()));
                            }
                        }
                        storeSkuPriceInfoDTO.setStoreId(request.getStoreId());
                        storeSkuPriceInfoDTO.setStorePrice(999900L);
                        storeSkuPriceInfoDTO.setSkuId(storeSkuDTO.getSkuId());
                        storeSkuPriceInfoDTO.setPriceSyncStrategy(PriceSyncStrategyEnum.FIX_PRICE);
                        storeSkuPriceInfoDTO.setChannelType(com.sankuai.meituan.shangou.empower.price.client.enums.ChannelType
                                .findByValue(price.getChannelId()));
                        channelSyncStrategyList.add(storeSkuPriceInfoDTO);

                    });
                }
            }
            if (CollectionUtils.isEmpty(channelSyncStrategyList)) {
                return commonResponse;
            }
            InitPriceInfoForFixValueRequest priceRequest = new InitPriceInfoForFixValueRequest();
            priceRequest.setTenantId(user.getTenantId());
            priceRequest.setOperatorId(user.getEmployeeId());
            priceRequest.setOperatorName(user.getOperatorName());
            priceRequest.setChannelStoreSkuPriceInfoDTOS(channelSyncStrategyList);
            InitPriceInfoForFixValueResponse response = complexPriceThriftService.initPriceInfoForFixValue(priceRequest);
            log.error("总部管品手动定价初始化请求,request:{}, response:{}", request, response);
            if (response.getCode() != ResultCode.SUCCESS.getCode() || MapUtils.isNotEmpty(response.getChannelStoreSkuErrMsgMap
                    ())) {
                commonResponse.setCode(ResultCode.FAIL.getCode());
            }
        } catch (Exception e) {
            log.error("手动定价初始化异常,rpcRequest:{}", rpcRequest, e);
            commonResponse.setCode(ResultCode.FAIL.getCode());
        }
        return commonResponse;
    }

    private Map<String, ChannelSkuDTO> queryExistsChannelSkuMap(SaveStoreSpuRequest rpcRequest, Integer channelId) {
        QueryChannelSpuBySpuListRequest request = new QueryChannelSpuBySpuListRequest();
        request.setTenantId(rpcRequest.getTenantId());
        request.setStoreId(rpcRequest.getStoreId());
        request.setSpuIdList(Lists.newArrayList(rpcRequest.getStoreSpu().getSpuId()));
        request.setChannelId(channelId);
        QueryChannelSpuBySpuListResponse response = channelSpuBizThriftService.batchQueryChannelSpuBySpuList(request);
        log.info("查询渠道商品请求，request:{}, response:{}.", request, response);
        if (response == null || !response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            throw new CommonRuntimeException("保存商品出错");
        }
        List<ChannelSpuV2DTO> channelSpuV2DTOList = response.getChannelSpuV2DTOList();
        if (CollectionUtils.isEmpty(channelSpuV2DTOList)) {
            return Collections.emptyMap();
        }
        if (channelSpuV2DTOList.get(0).getOnlineStatus() == -1) {
            return Collections.emptyMap();
        }
        List<ChannelSkuDTO> channelSkuList = channelSpuV2DTOList.get(0).getChannelSkuList();
        if (CollectionUtils.isEmpty(channelSkuList)) {
            return Collections.emptyMap();
        }
        return channelSkuList.stream().filter(sku -> sku.getOnlineStatus() != -1).collect(Collectors.toMap
                (ChannelSkuDTO::getSkuId, Function.identity(), (o1, o2) -> o2));
    }


    /**
     * author: <EMAIL>
     * date: 2021-03-23 16:53:14
     * <p>
     * method: queryProblemSpuList
     * params: [request]
     * return: Result<PageResultV2<ProblemSpuVO>>
     * desc: 查询问题商品列表，并组装前端需要数据
     */
    public CommonResponse<ProblemSpuQueryResponseVO> queryProblemSpuList(User user, ProblemSpuQueryRequest request) {

        // 1.获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }

        // 2.获取不一致的商品列表
        QueryProblemSpuResponse response = problemSpuThriftService.queryProblemSpuList(request.to(user.getTenantId()));
        if (!response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }

        ProblemSpuQueryResponseVO responseVO = new ProblemSpuQueryResponseVO();
        responseVO.setPageInfo(new PageInfoVO(response.getPageInfoDTO()));
        if (CollectionUtils.isEmpty(response.getProblemSpuDetailDTOS())) {
            responseVO.setProblemSpuList(Collections.emptyList());
            return CommonResponse.success(responseVO);
        }

        // 3.获取商品标签信息
        Map<String, List<String>> spuTagListMap = queryProblemSpuTags(response.getProblemSpuDetailDTOS(),
                user.getTenantId(), request.getStoreId());
        List<ProblemSpuVO> problemSpuVOS = response.getProblemSpuDetailDTOS().stream()
                .map(spuDetail -> ProblemSpuVO.of(spuDetail, spuTagListMap.get(spuDetail.getSpuId())))
                .collect(Collectors.toList());
        responseVO.setProblemSpuList(problemSpuVOS);

        return CommonResponse.success(responseVO);
    }

    private Map<String, List<String>> queryProblemSpuTags(List<ProblemSpuDetailDTO> spuDetailDTOS, Long tenantId, Long storeId) {
        if (CollectionUtils.isEmpty(spuDetailDTOS)) {
            return Maps.newHashMap();
        }

        // 获取商品的所有标签
        Map<String, List<Long>> spuTagIds = Maps.newHashMap();
        Set<Long> tagIds = Sets.newHashSet();

        PoiSpuIdListCommand poiSpuIdListCommand = new PoiSpuIdListCommand();
        poiSpuIdListCommand.setMerchantId(tenantId);
        poiSpuIdListCommand.setPoiId(storeId);
        poiSpuIdListCommand.setSpuIds(spuDetailDTOS.stream().map(ProblemSpuDetailDTO::getSpuId).filter(Strings::isNotEmpty)
                .collect(Collectors.toList()));

        try {
            QueryPoiSpuListResult result = empowerPoiSpuThriftService.queryPoiSpuByIds(poiSpuIdListCommand);
            if (result.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode() ||
                    CollectionUtils.isEmpty(result.getSpuList())) {
                log.info("查询商品信息异常：request [{}], response [{}].", poiSpuIdListCommand, result);
            } else {
                result.getSpuList().forEach(spu -> {
                    spuTagIds.put(spu.getSpuId(), spu.getTagIds());
                    if (CollectionUtils.isNotEmpty(spu.getTagIds())) {
                        tagIds.addAll(spu.getTagIds());
                    }
                });

            }
        } catch (TException e) {
            log.warn("查询商品信息异常，request [{}].", poiSpuIdListCommand);
        }

        // 查询标签对应的名称
        try {
            QueryTenantTagNameRequest tagNameRequest = QueryTenantTagNameRequest.builder()
                    .tenantId(tenantId)
                    .tagIds(Lists.newArrayList(tagIds))
                    .build();
            TenantTagNameResponse response = tenantTagThriftService.queryTagNames(tagNameRequest);
            if (response.getCode().intValue() != ResultCodeEnum.SUCCESS.getCode()) {
                log.info("查询商品标签信息异常， request [{}], response [{}].", tagNameRequest, response);
                return com.google.common.collect.Maps.newHashMap();
            }
            Map<Long, String> tagIdNameMap = response.getTagIdNameMap();
            Map<String, List<String>> spuTagNameMap = com.google.common.collect.Maps.newHashMap();
            spuDetailDTOS.forEach(spu -> {
                if (CollectionUtils.isNotEmpty(spuTagIds.get(spu.getSpuId()))) {
                    List<String> tagNameList = spuTagIds.get(spu.getSpuId()).stream()
                            .map(tagId -> tagIdNameMap.get(tagId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    spuTagNameMap.put(spu.getSpuId(), tagNameList);
                }
            });

            return spuTagNameMap;
        } catch (Exception e) {
            log.warn("查询租户标签对应的名称信息异常.", e);
        }
        return Maps.newHashMap();
    }

    /**
     * 根据商品名称或upc 查询渠道推荐类目
     *
     * @param querySpuRecommendTag
     * @param storeId
     * @return
     */
    public CommonResponse<ChannelCategoryDTO> queryRecommendTag(User user, QuerySpuRecommendTag querySpuRecommendTag, Long storeId) {
        try {
            if (Objects.isNull(querySpuRecommendTag) ||
                    Objects.isNull(querySpuRecommendTag.getChannelId()) ||
                    (StringUtils.isBlank(querySpuRecommendTag.getName()) && StringUtils.isBlank(querySpuRecommendTag.getUpcCode()))) {
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "请求不合法-商品名称、渠道存在为空", null);
            }

            Long tenantId = user.getTenantId();

            ChannelRecommendCateQueryReq channelRecommendCateQueryReq = new ChannelRecommendCateQueryReq();
            channelRecommendCateQueryReq.setTenantId(tenantId);
            channelRecommendCateQueryReq.setChannelId(querySpuRecommendTag.getChannelId());
            channelRecommendCateQueryReq.setName(querySpuRecommendTag.getName());
            channelRecommendCateQueryReq.setUpcCode(querySpuRecommendTag.getUpcCode());
            channelRecommendCateQueryReq.setStoreId(storeId);

            ChannelCategoriesQueryResponse response = channelCategoryThriftService.queryReCommendCategoryById(channelRecommendCateQueryReq);

            if (Objects.isNull(response) || response.getCode() != 0) {
                log.error("查询推荐类目失败: request: {}, response: {}", querySpuRecommendTag, response);
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), response.getMessage(), null);
            }

            if (CollectionUtils.isEmpty(response.getChannelCategories())) {
                // 未查询到渠道推荐类目 正常返回，前端兼容
                return CommonResponse.success(null);
            }

            // 只会查询一个类目信息
            return CommonResponse.success(response.getChannelCategories().get(0));
        } catch (Exception e) {
            log.error("queryRecommendTag异常, Request:{}", querySpuRecommendTag, e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(),
                    Optional.ofNullable(e.getMessage()).orElse("查询推荐类目异常"), null);
        }
    }

    /**
     * 批量删除门店商品 流程参考eapi：com.sankuai.meituan.shangou.saas.crm.eapi.web.controller.spu.StoreSpuController#batchDelete
     *
     * @return
     */
    public CommonResponse<BatchOperateSpuResultVO> batchDelete(BatchDeleteStoreSpuApiRequest apiRequest) {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

            BatchDeleteStoreSpuRequest rpcRequest = apiRequest.toRpcRequest(user);

            GeneralResponse rpcResponse = storeSpuThriftService.batchDeleteStoreSpu(rpcRequest);

            //删除本地 渠道不一致对比记录
            batchDeleteAbnormalSpuBySpuIds(user, apiRequest);

            List<ChannelSpuBO> channelSpuBOS = apiRequest.toChannelSkuBOList();

            BatchOperateSpuResultVO batchOperateResultVO = handleBatchOperateResult(rpcResponse, channelSpuBOS);

            if (rpcResponse.getCode() == com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.success(batchOperateResultVO);
            }

            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "批量删除失败", batchOperateResultVO);
        } catch (Exception e) {
            log.error("批量删除门店或渠道商品异常, Request:{}", apiRequest, e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "批量删除异常");
        }
    }


    /**
     * 找出删除失败记录
     *
     * @param rpcResponse
     * @param channelSpuBOS 含渠道名称、店名、spu
     * @return
     */
    private BatchOperateSpuResultVO handleBatchOperateResult(GeneralResponse rpcResponse, List<ChannelSpuBO> channelSpuBOS) {
        if (rpcResponse == null) {
            return new BatchOperateSpuResultVO();
        }
        BatchOperateSpuResultVO batchOperateResultVO = new BatchOperateSpuResultVO();
        batchOperateResultVO.setTaskId(rpcResponse.getTaskId());

        if (rpcResponse.getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue() &&
                CollectionUtils.isNotEmpty(rpcResponse.getFailedList())) {

            Map<ChannelSpuBO.ChanelSpuPoiTuple, ChannelSpuBO> spuMappings = channelSpuBOS.stream()
                    .filter(c -> StringUtils.isNotEmpty(c.getSpuId()))
                    .collect(Collectors.toMap(ChannelSpuBO::toTuple, a -> a, (nValue, oValue) -> nValue));

            List<BatchOperateSpuFailInfoVO> failInfoList = new ArrayList<>();
            for (ChannelStoreFailDTO batchOperateFailInfoBO : rpcResponse.getFailedList()) {
                BatchOperateSpuFailInfoVO batchOperateFailInfoVO = new BatchOperateSpuFailInfoVO();
                ChannelSpuBO.ChanelSpuPoiTuple chanelSpuPoiTuple = new ChannelSpuBO.ChanelSpuPoiTuple(batchOperateFailInfoBO
                        .getSpuId(), batchOperateFailInfoBO.getStoreId(), batchOperateFailInfoBO.getChannelId());

                if (spuMappings.get(chanelSpuPoiTuple) != null) {
                    ChannelSpuBO channelSpuBO = spuMappings.get(chanelSpuPoiTuple);
                    if (channelSpuBO != null) {
                        if (ChannelTypeEnum.findByChannelId(channelSpuBO.getChannelId()) != null) {
                            batchOperateFailInfoVO.setChannelName(channelSpuBO.getChannelName());
                        }
                        batchOperateFailInfoVO.setSpuId(channelSpuBO.getSpuId());
                        batchOperateFailInfoVO.setHasRetailPriceToReview(batchOperateFailInfoBO.isHasRetailPriceToReview());
                        batchOperateFailInfoVO.setHasOfflinePriceToReview(batchOperateFailInfoBO.isHasOfflinePriceToReview());
                        batchOperateFailInfoVO.setHasZeroRetailPrice(batchOperateFailInfoBO.isHasZeroRetailPrice());
                        batchOperateFailInfoVO.setHasZeroOfflinePrice(batchOperateFailInfoBO.isHasZeroOfflinePrice());
                        batchOperateFailInfoVO.setErrorMsg(batchOperateFailInfoBO.getErrorMsg());
                        failInfoList.add(batchOperateFailInfoVO);
                    }
                }
            }
            batchOperateResultVO.setFailInfoList(failInfoList);
        }
        return batchOperateResultVO;
    }

    /**
     * 删除不一致比对记录
     *
     * @param user
     * @param request
     */
    private void batchDeleteAbnormalSpuBySpuIds(User user, BatchDeleteStoreSpuApiRequest request) {
        Long tenantId = user.getTenantId();

        if (Objects.isNull(user) || Objects.isNull(request) ||
                CollectionUtils.isEmpty(request.getChannelList()) ||
                CollectionUtils.isEmpty(request.getStoreSpuList())) {
            return;
        }

        Long storeId = request.getStoreId();
        for (ChannelParamVO channelParamVO : request.getChannelList()) {
            AbnormalSpuDeleteRequest abnormalSpuDeleteRequest = new AbnormalSpuDeleteRequest();
            abnormalSpuDeleteRequest.setTenantId(tenantId);
            abnormalSpuDeleteRequest.setStoreId(storeId);
            abnormalSpuDeleteRequest.setChannelId(channelParamVO.getChannelId());
            abnormalSpuDeleteRequest.setSpuIds(request.getStoreSpuList());
            problemSpuThriftService.batchDeleteAbnormalSpuBySpuIds(abnormalSpuDeleteRequest);
        }
    }

    public CommonResponse<RecommendDynamicInfoVo> queryRecommendDynamicInfo(Long tenantId, Long storeId, QuerySpuRecommendDynamicInfo request) {

        ChannelRecommendDynamicInfoReq recommendDynamicInfoReq = new ChannelRecommendDynamicInfoReq();
        recommendDynamicInfoReq.setTenantId(tenantId);
        recommendDynamicInfoReq.setChannelId(request.getChannelId());
        recommendDynamicInfoReq.setSpuName(request.getSpuName());
        recommendDynamicInfoReq.setChannelCategoryId(request.getChannelCategoryId());
        recommendDynamicInfoReq.setStoreId(storeId);
        ChannelCatePropertyListResponse response;
        try {
            response = channelCategoryThriftService.queryRecommendDynamicInfo(recommendDynamicInfoReq);
        } catch (Exception e) {
            log.error("查询推荐动态信息失败. request: {}", recommendDynamicInfoReq, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询推荐动态信息异常，请稍后重试");
        }

        if (Objects.isNull(response) || response.getCode() != 0) {
            log.error("查询推荐类目失败: request: {}, response: {}", recommendDynamicInfoReq, response);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), Objects.nonNull(response) ? response.getMessage() : "查询推荐动态信息异常，请稍后重试");
        }

        if (CollectionUtils.isEmpty(response.getChannelCatePropertyList())) {
            // 未查询到渠道推荐类目 正常返回，前端兼容
            return CommonResponse.success(null);
        }
        List<ChannelDynamicInfoVO> channelDynamicInfoVOList = ChannelDynamicInfoVO.ofPlatformDTOList(response.getChannelCatePropertyList().get(0).getChannelDynamicInfoDTOList());
        return CommonResponse.success(RecommendDynamicInfoVo.builder()
                .spuName(request.getSpuName())
                .channelId(request.getChannelId())
                .channelCategoryId(request.getChannelCategoryId())
                .channelDynamicInfoVOList(channelDynamicInfoVOList)
                .build());
    }

    public CommonResponse<RecommendDynamicInfoAndWeightVo> queryRecommendDynamicInfoAndWeight(QuerySpuRecommendDynamicInfoAndWeight request) {
        ChannelRecommendDynamicInfoAndWeightReq recommendDynamicInfoAndWeightReq = request.convertRpcRequest();
        ChannelCatePropertyListAndWeightResponse response;
        try {
            response = channelCategoryThriftService.queryRecommendDynamicInfoAndWeight(recommendDynamicInfoAndWeightReq);
        } catch (Exception e) {
            log.error("查询推荐动态信息和重量失败. request: {}", recommendDynamicInfoAndWeightReq, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        if (Objects.isNull(response) || response.getCode() != 0) {
            log.error("查询推荐动态信息和重量失败: request: {}, response: {}", recommendDynamicInfoAndWeightReq, response);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), Objects.isNull(response) ? "查询推荐动态信息和重量异常，请稍后重试" : response.getMessage());
        }

        if (CollectionUtils.isEmpty(response.getChannelCatePropertyList()) && Objects.isNull(response.getWeightForUnit()) && Objects.isNull(response.getWeighUnit())) {
            // 未查询到渠道推荐类目或者查询到的重量信息不完整。 直接返回空data给前端
            return CommonResponse.success(null);
        }
        List<ChannelDynamicInfoVO> channelDynamicInfoVOList = CollectionUtils.isEmpty(response.getChannelCatePropertyList()) ? null : ChannelDynamicInfoVO.ofPlatformDTOList(response.getChannelCatePropertyList().get(0).getChannelDynamicInfoDTOList());
        String weightForUnit = Objects.isNull(response.getWeighUnit()) ? null : Double.toString(response.getWeightForUnit());
        return CommonResponse.success(RecommendDynamicInfoAndWeightVo.builder()
                .spuName(request.getSpuName())
                .channelId(request.getChannelId())
                .channelCategoryId(request.getChannelCategoryId())
                .channelDynamicInfoVOList(channelDynamicInfoVOList)
                .weightForUnit(weightForUnit)
                .weightUnit(response.getWeighUnit())
                .build());
    }

    public CommonResponse<StoreBusinessCategoryQueryVO> queryStoreFirstLevelBusinessCategory(Long tenantId, Long storeId, Integer channelId) {
        StoreBusinessCategoryQueryVO storeBusinessCategoryQueryVO = new StoreBusinessCategoryQueryVO();
        List<StoreChannelBusinessCategoryDTO> storeChannelBusinessCategoryDTOList = batchQueryStoreBusinessCategory(tenantId,
                Lists.newArrayList(storeId), channelId);
        if (CollectionUtils.isEmpty(storeChannelBusinessCategoryDTOList)) {
            storeBusinessCategoryQueryVO.setBusinessCategory(MtChannelStoreBusinessCategoryTypeEnum.NO_BIND_STORE.getCode());
            return CommonResponse.success(storeBusinessCategoryQueryVO);
        }
        Map<Long, Integer> storeFirstBusinessTypeMap = storeChannelBusinessCategoryDTOList.stream()
                .collect(Collectors.toMap(
                        StoreChannelBusinessCategoryDTO::getStoreId,
                        StoreChannelBusinessCategoryDTO::getBusinessCategoryId,
                        (existing, replacement) -> existing // 如果有重复的key，保留第一个
                ));
        storeBusinessCategoryQueryVO.setBusinessCategory(storeFirstBusinessTypeMap.getOrDefault(storeId,
                MtChannelStoreBusinessCategoryTypeEnum.NO_BIND_STORE.getCode()));
        return CommonResponse.success(storeBusinessCategoryQueryVO);
    }

    /**
     * 批量查询某个租户下多个门店的经营品类
     * @param tenantId
     * @param storeIdList
     * @param channelId
     * @return
     */
    private List<StoreChannelBusinessCategoryDTO> batchQueryStoreBusinessCategory(Long tenantId, List<Long> storeIdList,
                                                                                 Integer channelId) {
        if (tenantId == null || CollectionUtils.isEmpty(storeIdList) || channelId == null){
            return new ArrayList<>();
        }
        List<Long> distinctStoreIdList = storeIdList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(distinctStoreIdList)) {
            return new ArrayList<>();
        }
        List<StoreChannelBusinessCategoryDTO> poiBusinessCategoryDTOList = new ArrayList<>();
        Lists.partition(distinctStoreIdList, BATCH_LIMIT_10_AMOUNT).forEach(partitionStoreIds -> {
            try {
                StoreMtFirstBusinessCategoryRequest request = new StoreMtFirstBusinessCategoryRequest();
                request.setTenantId(tenantId);
                request.setStoreIdList(partitionStoreIds);
                request.setChannelId(channelId);
                StoreMtFirstBusinessCategoryResponse response = storeChannelBusinessThriftService.queryStoreMtFirstBusinessCategory(request);
                if (response == null || !Objects.equals(response.getCode(), 0) ||
                        CollectionUtils.isEmpty(response.getStoreChannelBusinessCategoryDTOList())) {
                    if(!Objects.equals(response.getCode(), 0)){
                        log.error("storeChannelBusinessThriftService.queryStoreMtFirstBusinessCategory err, request:{}|respones:{}",
                                request, response);
                    }
                    return;
                }
                poiBusinessCategoryDTOList.addAll(response.getStoreChannelBusinessCategoryDTOList());
            }
            catch (BizException e) {
                log.error("查询门店在美团渠道的一级经营品类失败，tenantId:{}|channelId:{}|storeId:{}", tenantId, channelId, partitionStoreIds, e);
            }
            catch (Exception e) {
                log.error("查询门店在美团渠道的一级经营品类发生异常，tenantId:{}|channelId:{}|storeId:{}", tenantId, channelId, partitionStoreIds, e);
            }
        });
        return poiBusinessCategoryDTOList;
    }

    /**
     * 获取门店商品异常详情
     * @param request 请求体
     * @return
     */
    public CommonResponse<ProductAbnormalVO> getSpuAbnormalDetail(QueryStoreSpuAbnormalRequest request) {
        try {
            if (request.getStoreId() == null){
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "门店id不能为空");
            }
            if (StringUtils.isBlank(request.getSpuId())){
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "spuId不能为空");
            }
            BatchQueryPoiSpuAbnormalRequest queryRequest = AbnormalConverter.convertToPoiSpuAbnormalQueryRequest(request);
            // 查询门店商品异常信息
            BatchQuerySpuAbnormalResponse response = abnormalProductBizThriftService.batchQueryPoiSpuAbnormalInfo(queryRequest);
            ResponseHandler.checkResponseAndStatus(response, resp -> response.getStatus().getCode(),
                    resp -> response.getStatus().getMsg());

            Map<String, List<AbnormalProductInfoDTO>> map = Fun.toMap(response.getSpuAbnormalInfos(), SpuAbnormalInfoDTO::getSpuId,
                    SpuAbnormalInfoDTO::getAbnormalProductInfos);
            List<AbnormalProductInfoDTO> abnormalProductInfoDTOS = map.get(request.getSpuId());
            Pair<List<Integer>, List<AbnormalProductInfoDTO>> pair = AbnormalConverter.mergeDiffCompareAbnormals(abnormalProductInfoDTOS);
            return CommonResponse.success(ProductAbnormalVO.of(pair.getRight(), pair.getLeft()));
        } catch (BizException e) {
            log.error("获取门店商品异常详情业务异常， request: {}",  request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e){
            log.error("获取门店商品异常详情异常， request: {}",  request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "系统异常");
        }
    }

    public StoreSpuActivityAndForbidFieldsQueryVO queryStoreSpuActivityInfo(Long tenantId, Long storeId, Integer channelId, String spuId,
                                                                            Integer spuActivityInfoQueryType) {
        StoreSpuActivityInfoQueryRequest rpcRequest = new StoreSpuActivityInfoQueryRequest();
        rpcRequest.setTenantId(tenantId);
        rpcRequest.setStoreId(storeId);
        rpcRequest.setChannelId(channelId);
        rpcRequest.setSpuId(spuId);
        rpcRequest.setSpuActivityInfoQueryType(spuActivityInfoQueryType);
        StoreSpuActivityInfoQueryResponse response = new StoreSpuActivityInfoQueryResponse();
        try {
            response = channelPromotionActivityThriftService.queryActivityInfoByStoreSpu(rpcRequest);
        } catch (Exception e) {
            log.error("queryStoreSpuActivityInfo error, tenantId:{},storeId:{},channelId:{},spuId:{},spuActivityInfoQueryType:{}",
                    tenantId ,storeId, channelId, spuId, spuActivityInfoQueryType, e);
            return new StoreSpuActivityAndForbidFieldsQueryVO();
        }
        if (response == null || response.getCode() != ResultCode.SUCCESS.getCode()) {
            log.error("queryStoreSpuActivityInfo failed, tenantId:{},storeId:{},channelId:{},spuId:{},spuActivityInfoQueryType:{}," +
                            "response:{}",
                    tenantId ,storeId, channelId, spuId, spuActivityInfoQueryType, response);
            // 返回活动为空，不阻塞正常流程
            return new StoreSpuActivityAndForbidFieldsQueryVO();
        }
        return ConvertActivityAndForbidFieldsQueryVO(response.getStoreSpuActivityDTOList(),
                response.getSpuForbidFiledAndActivitysMap(), response.getSkuForbidFiledAndActivitysMap(), spuActivityInfoQueryType);
    }
    private StoreSpuActivityAndForbidFieldsQueryVO ConvertActivityAndForbidFieldsQueryVO(List<StoreSkuActivityDTO> storeSpuActivityDTOList,
                                                                                         Map<String, List<ForbidFieldAndActivityDTO>> spuForbidFiledAndActivitysMap,
                                                                                         Map<String, List<ForbidFieldAndActivityDTO>> skuForbidFiledAndActivitysMap,
                                                                                         Integer spuActivityInfoQueryType) {
        StoreSpuActivityAndForbidFieldsQueryVO vo = new StoreSpuActivityAndForbidFieldsQueryVO();
        List<StoreSkuActivityVO> storeSkuActivityVOS = new ArrayList<>();
        // 全部的活动
        storeSpuActivityDTOList.forEach(dto -> storeSkuActivityVOS.add(StoreSkuActivityVO.fromDto(dto)));
        // 仅针对spu的活动
        Map<String, List<ForbidFieldAndActivityVO>> spuForbiddenOperationMap = buidldActivityVoMap(spuForbidFiledAndActivitysMap);
        // 仅针对sku的活动
        Map<String, List<ForbidFieldAndActivityVO>> skuForbiddenOperationMap = buidldActivityVoMap(skuForbidFiledAndActivitysMap);
        if (Objects.equals(spuActivityInfoQueryType, SpuActivityInfoQueryTypeEnum.QUERY_ALL_ACTIVITY.getCode())) {
            vo.setStoreSpuActivityVOList(storeSkuActivityVOS);
        }else if (Objects.equals(spuActivityInfoQueryType, SpuActivityInfoQueryTypeEnum.QUERY_FILED_ACTIVITY_IMPACT.getCode())) {
            vo.setSpuForbiddenOperationMap(spuForbiddenOperationMap);
            vo.setSkuForbiddenOperationMap(skuForbiddenOperationMap);
        }else {
            vo.setStoreSpuActivityVOList(storeSkuActivityVOS);
            vo.setSpuForbiddenOperationMap(spuForbiddenOperationMap);
            vo.setSkuForbiddenOperationMap(skuForbiddenOperationMap);
        }
        return vo;
    }
    private Map<String, List<ForbidFieldAndActivityVO>> buidldActivityVoMap(Map<String, List<ForbidFieldAndActivityDTO>> forbidFiledAndActivitysMap) {
        if(org.apache.commons.collections4.MapUtils.isEmpty(forbidFiledAndActivitysMap)){
            return new HashMap<>();
        }
        Map<String, List<ForbidFieldAndActivityVO>> result = new HashMap<>();
        forbidFiledAndActivitysMap.forEach((key, value) -> {
            List<ForbidFieldAndActivityVO> activityVOS = value.stream()
                    .map(ForbidFieldAndActivityVO::fromDto)
                    .collect(Collectors.toList());
            result.put(key, activityVOS);
        });
        return result;
    }
}
