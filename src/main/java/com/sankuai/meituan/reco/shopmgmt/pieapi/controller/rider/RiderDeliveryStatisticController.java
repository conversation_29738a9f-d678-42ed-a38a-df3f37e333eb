package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.statistic.RiderDaysDeliveryStatisticRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.statistic.RiderMonthsDeliveryStatisticRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.statistic.RiderDeliveryStatisticResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.PoiService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo.PoiInfoBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderDeliveryStatisticWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 自营骑手配送数据统计 Controller
 * <AUTHOR>
 * @since 2022/8/23 16:47
 **/

@InterfaceDoc(
        displayName = "自营骑手配送数据统计",
        type = "restful",
        scenarios = "包含骑手多日维度、月维度配送数据",
        description = "包含骑手多日维度、月维度配送数据",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "自营骑手配送数据统计")
@RestController
@RequestMapping("/pieapi/rider/delivery-statistic")
public class RiderDeliveryStatisticController {

    @Resource
    private RiderDeliveryStatisticWrapper riderDeliveryStatisticWrapper;

    @Resource
    private PoiService poiService;


    @MethodDoc(
            displayName = "自营骑手配送数据统计-多日维度",
            description = "自营骑手配送数据统计-多日维度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营骑手多天配送数据统计请求",
                            type = RiderDaysDeliveryStatisticRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery-statistic/days",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "自营骑手配送数据统计-多日维度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/days", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryStatisticResponse> daysDeliveryStatistics(@Valid @RequestBody RiderDaysDeliveryStatisticRequest request) {
        try {
            RiderDeliveryStatisticResponse response = riderDeliveryStatisticWrapper.queryDaysDeliveryStatistics(
                    request.getBeginTime(), request.getEndTime());
            setStoreOperationMode(response, request.getStoreId());
            return CommonResponse.success(response);
        } catch (CommonRuntimeException e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询骑手多日维度配送数据失败");
        }
    }

    @MethodDoc(
            displayName = "自营骑手配送数据统计-多月维度",
            description = "自营骑手配送数据统计-多月维度",
            parameters = {},
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery-statistic/months",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "自营骑手配送数据统计-多月维度")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/months", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryStatisticResponse> monthsDeliveryStatistics(@Valid @RequestBody RiderMonthsDeliveryStatisticRequest request) {
        try {
            RiderDeliveryStatisticResponse response = riderDeliveryStatisticWrapper.queryMonthsDeliveryStatistics();
            setStoreOperationMode(response, request.getStoreId());
            return CommonResponse.success(response);
        } catch (CommonRuntimeException e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询骑手多日维度配送数据失败");
        }
    }

    private void setStoreOperationMode(RiderDeliveryStatisticResponse response, Long storeId) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        PoiInfoBO poiInfo = poiService.getPoiById(tenantId, storeId);
        if (poiInfo != null && response != null) {
            response.setOperationMode(poiInfo.getOperationMode());
        }
    }
}
