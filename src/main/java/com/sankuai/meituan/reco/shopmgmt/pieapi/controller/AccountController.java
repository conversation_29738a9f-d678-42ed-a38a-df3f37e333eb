package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.shangou.sac.dto.request.manager.account.CreateManagerAndRelTenantRequest;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DxSSOConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonV2Response;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountConfigQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountHomePageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.YodaResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.AccountService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.SsoUserService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AccountWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountSessionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@InterfaceDoc(
        displayName = "账号管理和配置服务",
        type = "restful",
        scenarios = "账号管理和配置服务",
        description = "账号管理和配置服务",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "账号管理和配置服务")
@RestController
@RequestMapping("/pieapi/account")
public class AccountController {

    @Autowired
    private AccountWrapper accountWrapper;

    @Autowired
    private SsoUserService ssoUserService;

    @Resource
    private AccountService accountService;

    @MethodDoc(
            displayName = "我的账号信息首页",
            description = "我的账号信息首页",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/account/homepage",
            extensions = {
            @ExtensionDoc(
                    name = "SECURITY_PRIVILEGE",
                    content = "数据鉴权逻辑: 用户token获取数据"
            ),
            @ExtensionDoc(
                    name = "SECURITY_PRIVILEGE_VERIFY",
                    content = "True"
            ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "账号首页")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/homepage", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<AccountHomePageVO> homepage() {
        return accountWrapper.homepage();
    }

    @MethodDoc(
            displayName = "查询账号配置",
            description = "查询账号配置",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/account/queryconfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "查询账号配置")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryconfig", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<AccountConfigQueryResponse> queryConfig() {
        return accountWrapper.queryAccountConfig();
    }

    @MethodDoc(
            displayName = "设置账号配置",
            description = "设置账号配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "设置提醒配置",
                            type = AccountConfigVO.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/account/setconfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "设置账号配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/setconfig", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse setConfig(@Valid @RequestBody AccountConfigVO request) {
        return accountWrapper.setAccountConfig(request);
    }

    @MethodDoc(
            description = "绑定大象账号与租户关系",
            displayName = "绑定大象账号与租户关系",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/createManagerAndRelTenant",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "绑定大象账号与租户关系")
    @RequestMapping(value = "/createManagerAndRelTenant", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<String> createManagerAndRelTenant(HttpServletRequest request,
                                                            @Valid @RequestBody BindTenantRequest bindTenantRequest) {
        String ssoToken = request.getHeader(DxSSOConstants.SSO_TOKEN);
        User user = ssoUserService.getUserInfoByToken(ssoToken);
        if (user == null) {
            return CommonResponse.fail2(ResultCode.UNAUTHORIZED);
        }
        String eToken = bindTenantRequest.getEToken();
        AccountSessionVO accountSessionVO = accountService.queryAccountSession(eToken);
        if(accountSessionVO == null) {
           return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
        log.info("绑定大象账号与租户关系{}", user);
        CreateManagerAndRelTenantRequest createManagerAndRelTenantRequest = new CreateManagerAndRelTenantRequest();
        createManagerAndRelTenantRequest.setTenantId(accountSessionVO.getTenantId());
        createManagerAndRelTenantRequest.setMisId(user.getLogin());
        createManagerAndRelTenantRequest.setUserName(user.getName());
        createManagerAndRelTenantRequest.setEToken(eToken);
        CommonResponse response = accountWrapper.createManagerAndRelTenant(createManagerAndRelTenantRequest);
        return response;
    }

    @MethodDoc(
            description = "查询用户大象mis",
            displayName = "查询用户大象mis",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/queryMisId",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询用户大象mis")
    @RequestMapping(value = "/queryMisId", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<String> queryMisId(HttpServletRequest request) {
        String ssoToken = request.getHeader(DxSSOConstants.SSO_TOKEN);
        User user = ssoUserService.getUserInfoByToken(ssoToken);
        if (user == null) {
            return CommonResponse.fail2(ResultCode.UNAUTHORIZED);
        }
        return CommonResponse.success(user.getLogin());
    }

    @MethodDoc(
            description = "",
            displayName = "发送绑定手机号验证码",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/mobile/sendBindVerificationCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "发送绑定手机号验证码")
    @RequestMapping(value = "/mobile/sendBindVerificationCode", method = {RequestMethod.POST, RequestMethod.GET})
    @Auth
    public CommonResponse<String> sendBindMobileVerificationCode(@RequestBody SendBindMobileVerificationCodeReq req) {
        req.validate();
        return  accountWrapper.sendBindMobileVerificationCode(req.toThriftRequest());
    }

    @MethodDoc(
            description = "",
            displayName = "接口方式发送绑定手机号验证码",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/mobile/interface/sendBindVerificationCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "接口方式发送绑定手机号验证码")
    @RequestMapping(value = "/mobile/interface/sendBindVerificationCode", method = {RequestMethod.POST, RequestMethod.GET})
    @Auth
    public CommonV2Response<YodaResultVO> sendBindMobileInterfaceVerificationCode(@RequestBody SendInterfaceVerificationCodeReq req) {
        req.validate();
        YodaResultVO yodaResultVO = accountWrapper.sendBindMobileInterfaceVerificationCode(req.toThriftRequest());
        return CommonV2Response.success(yodaResultVO);
    }


    @MethodDoc(
            description = "",
            displayName = "接口方式验证并绑定手机号",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/mobile/interface/verifyAndBind",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "接口方式验证并绑定手机号")
    @RequestMapping(value = "/mobile/interface/verifyAndBind", method = {RequestMethod.POST, RequestMethod.GET})
    @Auth
    public CommonV2Response verifyInterfaceAndBindMobile(@RequestBody VerifyInterfaceAndBindMobileReq req) {
        req.validate();
        return accountWrapper.verifyInterfaceAndBindMobile(req.toThriftRequest());
    }

    @MethodDoc(
            description = "",
            displayName = "获取mark手机号",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/password/verifyAndModify",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "获取mark手机号")
    @RequestMapping(value = "/getMarkMobile", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonV2Response getMarkMobile(@RequestBody MarkMobileReq req) {
        req.validate();
        return  accountWrapper.getMarkMobile(req.getAccountName());
    }

    @MethodDoc(
            description = "",
            displayName = "发送忘记密码验证码",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/password/sendForgetVerificationCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "发送忘记密码验证码")
    @RequestMapping(value = "/password/sendForgetVerificationCode", method = {RequestMethod.POST})
    public CommonV2Response<YodaResultVO> sendForgetPasswordVerificationCode(@RequestBody SendForgetPasswordVerificationCodeReq req) {
        req.validate();
        return accountWrapper.sendForgetPasswordVerificationCode(req.toThriftRequest());
    }

    @MethodDoc(
            description = "",
            displayName = "修改密码验证并修改密码",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/password/verifyAndModify",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "修改密码验证并修改密码")
    @RequestMapping(value = "/password/verifyAndModify", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonV2Response verifyAndModifyPassword(@RequestBody VerifyAndModifyPasswordReq req) {
        req.validate();
        return  accountWrapper.verifyAndModifyPassword(req.toThriftRequest());
    }

    @MethodDoc(
            description = "",
            displayName = "验证并绑定手机",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/mobile/verifyAndBind",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "验证并绑定手机")
    @RequestMapping(value = "/mobile/verifyAndBind", method = {RequestMethod.POST, RequestMethod.GET})
    @Auth
    public CommonResponse<String> verifyAndBindMobile(@RequestBody VerifyAndBindMobileReq req) {
        req.validate();
        return  accountWrapper.verifyAndBindMobile(req.toThriftRequest());
    }

    @MethodDoc(
            description = "注销当前账号",
            displayName = "注销当前账号",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/account/deactivationAccount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "注销当前账号")
    @RequestMapping(value = "/deactivationAccount", method = {RequestMethod.POST})
    public CommonResponse<Boolean> deactivationAccount() {
        accountWrapper.deactivationAccount();
        return CommonResponse.success(Boolean.TRUE);
    }

}
