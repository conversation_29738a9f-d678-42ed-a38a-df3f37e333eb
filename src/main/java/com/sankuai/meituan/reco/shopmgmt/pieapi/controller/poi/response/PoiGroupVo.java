package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@TypeDoc(
    description = "门店分组列表"
)
@ApiModel("门店分组列表")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class PoiGroupVo {

  @FieldDoc(
      description = "分组系统id"
  )
  @ApiModelProperty(value = "分组系统id", required = true)
  private Integer poiGroupId;

  @FieldDoc(
          description = "分组id展示界面展示"
  )
  @ApiModelProperty(value = "分组id展示界面展示", required = true)
  private Integer poiGroupViewId;


  @FieldDoc(
          description = "分组名称"
  )
  @ApiModelProperty(value = "分组名称", required = true)
  private String poiGroupName;


  @FieldDoc(
          description = "分组下门店数量"
  )
  @ApiModelProperty(value = "分组下门店数量", required = true)
  private Integer poiGroupSize;

  @FieldDoc(
          description = "分组下关联的门店ID列表"
  )
  @ApiModelProperty(value = "分组下关联的门店ID列表", required = true)
  private List<Long> poiIds;

}