package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.CreateFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySearchRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySortRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategoryTopRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ModifyFrontCategoryLevelRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ModifyFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryFrontCategoryByPoiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.FrontCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.FrontCategoryWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/21 5:10 下午
 **/
@InterfaceDoc(
        displayName = "店内分类管理",
        type = "restful",
        scenarios = "店内分类管理",
        description = "店内分类管理"
)
@Api(value = "店内分类管理")
@Slf4j
@RestController
@RequestMapping("/pieapi/store/frontCategory")
public class FrontCategoryController {

    @Autowired
    private FrontCategoryWrapper frontCategoryWrapper;

    @MethodDoc(
            description = "按门店维度查询前台分类信息",
            displayName = "按门店维度查询前台分类信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = QueryFrontCategoryByPoiRequest.class,
                            description = "租户商品规格查询接口请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/queryByPoi",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryByPoi", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<FrontCategoryVO>> queryByPoi(@RequestBody QueryFrontCategoryByPoiRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.queryByPoi(request, user);
    }

    @MethodDoc(
            description = "创建前台分类数据",
            displayName = "创建前台分类数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = CreateFrontCategoryRequest.class,
                            description = "创建前台分类数据"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/queryByPoi",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/createFrontCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse createFrontCategory(@RequestBody CreateFrontCategoryRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.createCategory(request, user);
    }

    @MethodDoc(
            description = "修改前台分类名称",
            displayName = "修改前台分类名称",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = ModifyFrontCategoryRequest.class,
                            description = "修改前台分类名称"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/modifyFrontCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/modifyFrontCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse modifyFrontCategory(@RequestBody ModifyFrontCategoryRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.modifyFrontCategory(request, user);
    }

    @MethodDoc(
            description = "修改前台分类等级",
            displayName = "修改前台分类等级",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = ModifyFrontCategoryLevelRequest.class,
                            description = "租户商品规格查询接口请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/modifyFrontCategoryLevel",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/modifyFrontCategoryLevel", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse modifyFrontCategoryLevel(@RequestBody ModifyFrontCategoryLevelRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.modifyFrontCategoryLevel(request, user);
    }

    @MethodDoc(
            description = "删除店内分类",
            displayName = "删除店内分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = FrontCategoryRequest.class,
                            description = "删除店内分类"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/deleteFrontCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/deleteFrontCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse deleteFrontCategory(@RequestBody FrontCategoryRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.deleteFrontCategory(request, user);
    }


    @MethodDoc(
            description = "修改店内分类排序",
            displayName = "修改店内分类排序",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = FrontCategorySortRequest.class,
                            description = "删除店内分类"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/resortCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/resortCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse resortCategory(@RequestBody FrontCategorySortRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.resortCategory(request, user);
    }


    @MethodDoc(
            description = "设置限时置顶店内分类",
            displayName = "设置限时置顶店内分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = FrontCategoryTopRequest.class,
                            description = "删除店内分类"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/topCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/topCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse topCategory(@RequestBody FrontCategoryTopRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.topCategory(request, user);
    }


    @MethodDoc(
            description = "店内分类详情",
            displayName = "店内分类详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = FrontCategoryRequest.class,
                            description = "店内分类详情"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/detail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<FrontCategoryVO> detail(@RequestBody FrontCategoryRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.detail(request, user);
    }

    @MethodDoc(
            description = "搜索店内分类",
            displayName = "搜索店内分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = FrontCategorySearchRequest.class,
                            description = "搜索店内分类"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/frontCategory/searchCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/searchCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<FrontCategoryVO>> searchCategory(@RequestBody FrontCategorySearchRequest request) {
        // 检验参数
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return frontCategoryWrapper.searchCategory(request, user);
    }
}
