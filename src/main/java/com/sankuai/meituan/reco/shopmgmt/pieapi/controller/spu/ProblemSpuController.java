package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ProblemProductServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@InterfaceDoc(
        displayName = "问题商品SPU相关接口",
        type = "restful",
        scenarios = "问题商品SPU相关接口",
        description = "问题商品SPU相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "问题商品SPU相关接口")
@RestController
@RequestMapping("/pieapi/ocms/problem/spu")
public class ProblemSpuController {

    @Resource
    private ProblemProductServiceWrapper problemProductServiceWrapper;

    @MethodDoc(
            displayName = "不一致商品类型统计列表",
            description = "不一致商品类型统计列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = ProblemSpuCountQueryRequest.class)
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryProblemSpuCount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemSpuCountResponseVO> queryProblemSpuCount(@RequestBody ProblemSpuCountQueryRequest request) throws TException, InterruptedException {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return problemProductServiceWrapper.queryProblemSpuCount(user, request);
    }

    @MethodDoc(
            displayName = "不一致商品类型统计列表",
            description = "不一致商品类型统计列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = ProblemSpuCountQueryRequest.class)
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryProblemSpuCountList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemSpuCountListResponseVO> queryProblemSpuCountList(@RequestBody ProblemSpuCountQueryRequest request) {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return problemProductServiceWrapper.queryProblemSpuTypeCount(user, request);
    }

    @MethodDoc(
            displayName = "按类型查询不一致商品列表",
            description = "按类型查询不一致商品列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = ProblemSpuTypeQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryProblemSpuListByType", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemListInfoResponseVO> queryProblemSpuListByType(@RequestBody ProblemSpuTypeQueryRequest request) {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return problemProductServiceWrapper.queryProblemSpuListByType(user, request);
    }

    @MethodDoc(
            displayName = "查询门店商品的不一致信息",
            description = "查询门店商品的不一致信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = StoreSpuProblemInfoRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStoreSpuProblemBySpuId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CompareRecordDetailResponseVO> queryStoreSpuProblemBySpuId(@RequestBody StoreSpuProblemInfoRequest request) {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return problemProductServiceWrapper.queryStoreSpuProblemBySpuId(user, request);
    }

    @MethodDoc(
            displayName = "一致性同步操作",
            description = "一致性同步操作, 结果中返回失败列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = ProblemSpuOptRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sync/operate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemSpuOptResponseVO> syncOperate(@RequestBody ProblemSpuOptRequest request) {
        request.validate();
        return problemProductServiceWrapper.syncOperate(request);
    }

    @MethodDoc(
            displayName = "商品问题列表",
            description = "商品问题列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = ProblemSpuCountQueryRequest.class)
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySpuProblemList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SpuProblemListResponseVO> querySpuProblemList(@RequestBody SpuProblemListRequest request) {
        request.validate();
        return problemProductServiceWrapper.querySpuProblemList(request);
    }

    @MethodDoc(
            displayName = "按类型查询不一致商品列表",
            description = "按类型查询不一致商品列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = ProblemSpuTypeQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryDiffCompareSpuList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<DiffCompareSpuResponseVO> queryDiffCompareSpuList(@RequestBody DiffCompareSpuQueryRequest request) {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return problemProductServiceWrapper.queryDiffCompareSpuListByType(user, request);
    }

    @MethodDoc(
            displayName = "不一致商品类型统计列表",
            description = "不一致商品类型统计列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = DiffCompareSpuQueryRequest.class)
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryDiffCompareSpuCountList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemSpuTypeVo> queryDiffCompareSpuCountList(@RequestBody DiffCompareSpuQueryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return problemProductServiceWrapper.queryDiffCompareSpuTypeCount(user, request);
    }

    @MethodDoc(
            displayName = "一致性同步操作",
            description = "一致性同步操作, 结果中返回失败列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = DiffCompareSpuOptRequest.class,
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sync/diffCompareOperate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemSpuOptResponseVO> syncDiffCompareOperate(@RequestBody DiffCompareSpuOptRequest request) {
        request.validate();
        return problemProductServiceWrapper.syncDiffCompareOperate(request);
    }

}
