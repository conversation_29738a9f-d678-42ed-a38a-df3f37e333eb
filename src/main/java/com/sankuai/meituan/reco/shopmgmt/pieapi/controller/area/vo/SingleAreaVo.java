package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.area.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.SingleAreaBo;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SingleAreaDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/19 19:38
 **/
@Data
public class SingleAreaVo {

    @FieldDoc(
            description = "地区id"
    )
    private Long areaId;

    @FieldDoc(
            description = "地区名"
    )
    private String areaName;

    @FieldDoc(
            description = "地区类型 0-未知，1-国内，2-国外"
    )
    private Integer areaType;

    @FieldDoc(
            description = "父地区id"
    )
    private Long parentId;

    @FieldDoc(
            description = "地区id路径"
    )
    private String areaIdPath;

    @FieldDoc(
            description = "地区名路径"
    )
    private String areaNamePath;

    @FieldDoc(
            description = "地区级别 1-国家，2-省份，3-地市，4-区县"
    )
    private Integer level;

    @FieldDoc(
            description = "叶子结点 1-是，2-否"
    )
    private Integer leafNode;

    public static List<SingleAreaVo> ofDTO(List<SingleAreaDTO> singleAreaDTOList){
        if(CollectionUtils.isEmpty(singleAreaDTOList)){
            return Lists.newArrayList();
        }
        List<SingleAreaVo> singleAreaVos = new ArrayList<>();
        for(SingleAreaDTO singleAreaDTO : singleAreaDTOList){
            SingleAreaVo singleAreaVo = new SingleAreaVo();
            singleAreaVo.setAreaId(singleAreaDTO.getAreaId());
            singleAreaVo.setAreaName(singleAreaDTO.getAreaName());
            singleAreaVo.setAreaIdPath(singleAreaDTO.getAreaIdPath());
            singleAreaVo.setAreaNamePath(singleAreaDTO.getAreaNamePath());
            singleAreaVo.setAreaType(singleAreaDTO.getAreaType());
            singleAreaVo.setParentId(singleAreaDTO.getParentId());
            singleAreaVo.setLevel(singleAreaDTO.getLevel());
            singleAreaVo.setLeafNode(singleAreaDTO.getLeafNode());
            singleAreaVos.add(singleAreaVo);
        }
        return singleAreaVos;
    }

    public static List<SingleAreaVo> ofBO(List<SingleAreaBo> singleAreaBoList){
        if(CollectionUtils.isEmpty(singleAreaBoList)){
            return Lists.newArrayList();
        }
        List<SingleAreaVo> singleAreaVos = new ArrayList<>();
        for(SingleAreaBo singleAreaBo : singleAreaBoList){
            SingleAreaVo singleAreaVo = new SingleAreaVo();
            singleAreaVo.setAreaId(singleAreaBo.getAreaId());
            singleAreaVo.setAreaName(singleAreaBo.getAreaName());
            singleAreaVo.setAreaIdPath(singleAreaBo.getAreaIdPath());
            singleAreaVo.setAreaNamePath(singleAreaBo.getAreaNamePath());
            singleAreaVo.setAreaType(singleAreaBo.getAreaType());
            singleAreaVo.setParentId(singleAreaBo.getParentId());
            singleAreaVo.setLevel(singleAreaBo.getLevel());
            singleAreaVo.setLeafNode(singleAreaBo.getLeafNode());
            singleAreaVos.add(singleAreaVo);
        }
        return singleAreaVos;
    }

}
