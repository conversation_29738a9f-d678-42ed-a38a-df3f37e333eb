package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.qualification.ChannelCategoryQualificationVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.BatchSkuReviewRecallApplyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeChannelSkuFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeMultiChannelSkuStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelCategoryQueryByChannelIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChgChannelSkuStatusForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.PriceChangePreviewRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryCartonMeasureRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryCdqStoreSkuDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryChannelCategoryAfterSaleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QueryChannelCategoryInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateBatchDeleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateBatchSaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateDeleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateGetRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReasonTemplateSaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReviewBatchRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaveCdqStoreSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuListPageByDayAndOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuListPageForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewApplyListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewApplySaveRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewInfoGetRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuStoreToReviewCountGetRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuTotalCountOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StandardSkuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.UpdatePriceAndStockForAppRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.CartonMeasureVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeChannelSkuFrontCategoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeChannelSkuStatusForAppResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChangeMultiChannelSkuStatusResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryAfterSaleResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryInfoResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelCategoryRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelQualificationByCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.PageQueryStandardSkuResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.PriceChangePreviewResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QueryStoreOnlineSkuDetailResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReasonTemplateBatchSaveResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ReviewBatchResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SaveCdqStoreSkuPartitionSuccessResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuListPageForAppResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuReviewListQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuStoreToReviewCountGetResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTotalCountOpTypeResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.UpdatePriceStockResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ProductBizServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaasPriceServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantProductWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-07 10:21
 * @Description:
 */
@InterfaceDoc(
        displayName = "ocms商品库存服务",
        type = "restful",
        scenarios = "ocms商品库存服务",
        description = "ocms商品库存服务",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "ocms商品库存服务")
@RestController
@RequestMapping("/pieapi/ocms/skuAndStock")
public class SkuAndStockController {
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;

    @Resource
    private TenantProductWrapper tenantProductWrapper;

    @Autowired
    private ProductBizServiceWrapper productBizServiceWrapper;

    @MethodDoc(
            description = "查询商品信息列表，分页查询商品信息",
            displayName = "查询商品信息列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取订单详情请求",
                            type = SkuListPageForAppRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"skuInfoList\": [\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"sku\": \"2322307\",\n" +
                    "                \"name\": \"(特价)1.5米凉席\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": null,\n" +
                    "                \"images\": [\n" +
                    "                    \"123\"\n" +
                    "                ],\n" +
                    "                \"spec\": \"无\",\n" +
                    "                \"weight\": 0,\n" +
                    "                \"basicUnit\": null,\n" +
                    "                \"upcInfo\": null,\n" +
                    "                \"channels\": [\n" +
                    "                    {\n" +
                    "                        \"channel\": 100,\n" +
                    "                        \"price\": 8.2,\n" +
                    "                        \"stockCount\": \"50\",\n" +
                    "                        \"status\": 3\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"channel\": 200,\n" +
                    "                        \"price\": 8.2,\n" +
                    "                        \"stockCount\": \"50\",\n" +
                    "                        \"status\": 3\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"channel\": 300,\n" +
                    "                        \"price\": 8.2,\n" +
                    "                        \"stockCount\": \"50\",\n" +
                    "                        \"status\": 3\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"channel\": -1,\n" +
                    "                        \"price\": 5.5,\n" +
                    "                        \"stockCount\": \"100\",\n" +
                    "                        \"status\": 0\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"sku\": \"2660592\",\n" +
                    "                \"name\": \"四和旅行壶666\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": null,\n" +
                    "                \"images\": [\n" +
                    "                    \"\\nhttp://msstest.vip.sankuai.com/eapi/86ba4f8e0e626179ca55bc2f2896ee85_1551162514416.jpg?AWSAccessKeyId=553a9470c28941048f816eee09aa1198&Expires=1866522516&Signature=VFUXSiJdtUh0cnRN4GhOTj4GD1I%3D\"\n" +
                    "                ],\n" +
                    "                \"spec\": \"120ML\",\n" +
                    "                \"weight\": 0,\n" +
                    "                \"basicUnit\": null,\n" +
                    "                \"upcInfo\": null,\n" +
                    "                \"channels\": [\n" +
                    "                    {\n" +
                    "                        \"channel\": 100,\n" +
                    "                        \"price\": 8.2,\n" +
                    "                        \"stockCount\": \"50\",\n" +
                    "                        \"status\": 3\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"channel\": 200,\n" +
                    "                        \"price\": 8.2,\n" +
                    "                        \"stockCount\": \"50\",\n" +
                    "                        \"status\": 3\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"channel\": 300,\n" +
                    "                        \"price\": 8.2,\n" +
                    "                        \"stockCount\": \"50\",\n" +
                    "                        \"status\": 3\n" +
                    "                    },\n" +
                    "                    {\n" +
                    "                        \"channel\": -1,\n" +
                    "                        \"price\": 5.5,\n" +
                    "                        \"stockCount\": \"100\",\n" +
                    "                        \"status\": 0\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            }\n" +
                    "        ],\n" +
                    "        \"pageInfo\": {\n" +
                    "            \"page\": 1,\n" +
                    "            \"size\": 10,\n" +
                    "            \"totalPage\": 1,\n" +
                    "            \"totalSize\": 2\n" +
                    "        }\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/querySkuList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkuList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuListPageForAppResponseVO> querySkuListForApp(@Valid @RequestBody SkuListPageForAppRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<String> boothIdList = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, String::valueOf);
        String boothId = null;
        if (user.getAccountType() == AccountTypeEnum.BOOTH.getValue() && !CollectionUtils.isEmpty(boothIdList)) {
            boothId = boothIdList.get(0);
        }
        return  ocmsServiceWrapper.querySkuListForApp(request, user, boothId);
    }


    @MethodDoc(
            description = "按天查未报价/未上架商品",
            displayName = "按天查未报价/未上架商品",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "按天查未报价/未上架商品",
                            type = SkuListPageByDayAndOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/querySkuListByDayAndOpType",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkuListByDayAndOpType", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuListPageForAppResponseVO> querySkuListForAppByDayAndOpType(@Valid @RequestBody SkuListPageByDayAndOpTypeRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.querySkuListForAppByDayAndOpType(request, user);
    }


    @MethodDoc(
            description = "按天查未报价/未上架商品总数",
            displayName = "按天查未报价/未上架商品总数",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "按天查未报价/未上架商品总数",
                            type = SkuTotalCountOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/querySkuTotalCountByDayAndOpType",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkuTotalCountByDayAndOpType", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuTotalCountOpTypeResponseVO> querySkuTotalCountForAppByDayAndOpType(@Valid @RequestBody SkuTotalCountOpTypeRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.querySkuTotalCountForAppByDayAndOpType(request, user);
    }


    @MethodDoc(
            description = "修改商品价格和库存",
            displayName = "修改商品价格和库存",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "修改线上商品价格和库存请求",
                            type = UpdatePriceAndStockForAppRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"errorCode\": 0,\n" +
                    "        \"comment\": null,\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/updatePriceAndStock",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updatePriceAndStock", method = RequestMethod.POST)
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<UpdatePriceStockResponseVO> updateProductPriceAndStockForApp(@Valid @RequestBody UpdatePriceAndStockForAppRequest updatePriceAndStockForAppRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.updateProductPriceAndStockForApp(updatePriceAndStockForAppRequest, user);
    }

    @MethodDoc(
            description = "渠道商品上下架",
            displayName = "渠道商品上下架",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "渠道商品上下架请求",
                            type = ChangeMultiChannelSkuStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"channelStatusChangeResults\": [\n" +
                    "            {\n" +
                    "                \"channelId\": 100,\n" +
                    "                \"skuChannelStatus\": 1,\n" +
                    "                \"success\": 1,\n" +
                    "                \"failReason\": \"\"\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/changeMultiChannelSkuStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/changeMultiChannelSkuStatus")
    @ResponseBody
    public CommonResponse<ChangeMultiChannelSkuStatusResponse> changeMultiChannelSkuStatus(@Valid @RequestBody ChangeMultiChannelSkuStatusRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.changeMultiChannelSkuStatus(user, request);
    }

    @MethodDoc(
            description = "app商品批量上下架",
            displayName = "app商品批量上下架",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品批量上下架请求",
                            type = ChgChannelSkuStatusForAppRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/changeChannelSkuStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/changeChannelSkuStatus", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChangeChannelSkuStatusForAppResponseVO> changeChannelSkuStatusForApp(@Valid @RequestBody ChgChannelSkuStatusForAppRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.changeChannelSkuStatusForApp(request, user);
    }

    @MethodDoc(
            description = "创建门店商品并上线(菜大全)",
            displayName = "创建门店商品并上线(菜大全)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "创建门店商品并上线",
                            type = SaveCdqStoreSkuRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/saveStoreOnlineSku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/saveStoreOnlineSku")
    @ResponseBody
    public CommonResponse<SaveCdqStoreSkuPartitionSuccessResponse> saveStoreOnlineSku(@Valid @RequestBody SaveCdqStoreSkuRequest request) {
        return ocmsServiceWrapper.saveStoreOnlineSku(request);
    }

    @MethodDoc(
            description = "门店商品变价预览(菜大全)",
            displayName = "门店商品变价预览(菜大全)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品变价预览(菜大全)",
                            type = PriceChangePreviewRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/previewPriceChange",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/previewPriceChange")
    @ResponseBody
    public CommonResponse<PriceChangePreviewResponse> previewPriceChange(@Valid @RequestBody PriceChangePreviewRequest request) {
        return saasPriceServiceWrapper.previewPriceChange(request);
    }

    @MethodDoc(
            description = "获取门店商品详情(菜大全)",
            displayName = "获取门店商品详情(菜大全)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取门店商品详情",
                            type = QueryCdqStoreSkuDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/detailStoreOnlineSku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detailStoreOnlineSku", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryStoreOnlineSkuDetailResponse> queryDetailStoreOnlineSku(@Valid @RequestBody QueryCdqStoreSkuDetailRequest request) {
        return ocmsServiceWrapper.queryDetailStoreOnlineSku(request);
    }

    @MethodDoc(
            description = "批量修改线上商品前台分类",
            displayName = "批量修改线上商品前台分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "修改线上商品前台分类",
                            type = ChangeChannelSkuFrontCategoryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/changeChannelSkuFrontCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/changeChannelSkuFrontCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChangeChannelSkuFrontCategoryResponse> changeChannelSkuFrontCategory(@Valid @RequestBody ChangeChannelSkuFrontCategoryRequest request) {
        return ocmsServiceWrapper.changeChannelSkuFrontCategory(request);
    }


    @MethodDoc(
            description = "保存商品提报信息",
            displayName = "保存商品提报信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "保存商品提报信息",
                            type = SkuReviewApplySaveRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/saveSkuReviewApply",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/saveSkuReviewApply", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<String> saveSkuReviewApply(@Valid @RequestBody SkuReviewApplySaveRequest request) {
        try {
            request.selfCheck();
        }
        catch (IllegalArgumentException e) {
            return CommonResponse.fail(100, e.getMessage());
        }
        return ocmsServiceWrapper.saveSkuReviewApply(request);
    }


    @MethodDoc(
            description = "查询审核信息列表",
            displayName = "查询审核信息列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询审核信息列表",
                            type = SkuReviewListQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/querySkuReviewList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkuReviewList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuReviewListQueryResponse> querySkuReviewList(@Valid @RequestBody SkuReviewListQueryRequest request) {
        // return ocmsServiceWrapper.querySkuReviewList(request);
        return productBizServiceWrapper.queryProductReviewList(request);
    }

    @MethodDoc(
            description = "获取门店待审核记录数量",
            displayName = "获取门店待审核记录数量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取单个商品审核信息",
                            type = SkuReviewInfoGetRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/getStoreToReviewCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getStoreToReviewCount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuStoreToReviewCountGetResponse> getStoreToReviewCount(@Valid @RequestBody SkuStoreToReviewCountGetRequest request) {
        return ocmsServiceWrapper.getStoreToReviewCount(request);
    }

    @MethodDoc(
            description = "获取单个商品审核信息",
            displayName = "获取单个商品审核信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取单个商品审核信息",
                            type = SkuReviewInfoGetRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/getSkuReviewInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getSkuReviewInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse getSkuReviewInfo(@Valid @RequestBody SkuReviewInfoGetRequest request) {
        return ocmsServiceWrapper.getSkuReviewInfo(request);
    }

    @MethodDoc(
            description = "撤回提报申请",
            displayName = "撤回提报申请",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "撤回提报申请",
                            type = BatchSkuReviewRecallApplyRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/batchRecallApply",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchRecallApply", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse batchRecallApply(@Valid @RequestBody BatchSkuReviewRecallApplyRequest request) {
        // return ocmsServiceWrapper.batchRecallApply(request);
        return productBizServiceWrapper.batchRecallReport(request);
    }


    @MethodDoc(
            description = "保存审核拒绝原因模板",
            displayName = "保存审核拒绝原因模板",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "保存审核拒绝原因模板",
                            type = ReasonTemplateSaveRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/saveReasonTemplate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/saveReasonTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse saveReasonTemplate(@Valid @RequestBody ReasonTemplateSaveRequest request) {
        return ocmsServiceWrapper.saveReasonTemplate(request);
    }

    @MethodDoc(
            description = "批量保存审核拒绝原因模板",
            displayName = "批量保存审核拒绝原因模板",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量保存审核拒绝原因模板",
                            type = ReasonTemplateBatchSaveRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/batchSaveReasonTemplate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchSaveReasonTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ReasonTemplateBatchSaveResponse> batchSaveReasonTemplate(@RequestBody ReasonTemplateBatchSaveRequest request) {
        return ocmsServiceWrapper.batchSaveReasonTemplate(request);
    }

    @MethodDoc(
            description = "删除审核拒绝原因模板",
            displayName = "删除审核拒绝原因模板",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "删除审核拒绝原因模板",
                            type = ReasonTemplateDeleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/deleteReasonTemplate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/deleteReasonTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse deleteReasonTemplate(@Valid @RequestBody ReasonTemplateDeleteRequest request) {
        return ocmsServiceWrapper.deleteReasonTemplate(request);
    }

    @MethodDoc(
            description = "批量删除审核拒绝原因模板",
            displayName = "批量删除审核拒绝原因模板",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量删除审核拒绝原因模板",
                            type = ReasonTemplateBatchDeleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/batchDeleteReasonTemplate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchDeleteReasonTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse batchDeleteReasonTemplate(@RequestBody ReasonTemplateBatchDeleteRequest request) {
        return ocmsServiceWrapper.batchDeleteReasonTemplate(request);
    }

    @MethodDoc(
            description = "获取审核拒绝原因模板",
            displayName = "获取审核拒绝原因模板",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取审核拒绝原因模板",
                            type = ReasonTemplateGetRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/getReasonTemplate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getReasonTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse getReasonTemplate(@Valid @RequestBody ReasonTemplateGetRequest request) {
        return ocmsServiceWrapper.getReasonTemplate(request);
    }


    @MethodDoc(
            description = "获取审核拒绝/通过原因模板列表",
            displayName = "获取审核拒绝/通过原因模板列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取审核拒绝/通过原因模板列表",
                            type = ReasonTemplateListQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/queryReasonTemplateList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryReasonTemplateList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse queryReasonTemplateList(@Valid @RequestBody ReasonTemplateListQueryRequest request) {
        return ocmsServiceWrapper.queryReasonTemplateList(request);
    }


    @MethodDoc(
            description = "批量审核接口",
            displayName = "批量审核接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量审核接口",
                            type = ReviewBatchRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/batchReview",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchReview", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ReviewBatchResponse> batchReview(@Valid @RequestBody ReviewBatchRequest request) {
        return ocmsServiceWrapper.batchReview(request);
    }


    @MethodDoc(
            description = "查询商品审核提报信息列表",
            displayName = "查询商品审核提报信息列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询商品审核提报信息列表",
                            type = SkuReviewApplyListQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/storemanagement/ocms/skuandstock/querySkuReviewApplyList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkuReviewApplyList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SkuReviewListQueryResponse> querySkuReviewApplyList(@Valid @RequestBody SkuReviewApplyListQueryRequest request) {
        // return ocmsServiceWrapper.querySkuReviewApplyList(request);
        return productBizServiceWrapper.queryProductReviewApplyList(request);
    }


    @MethodDoc(
            displayName = "查询所有商品标签",
            description = "查询所有商品标签",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "",
            restExampleUrl = "/storemanagement/ocms/skuandstock/listSkuTags",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\"}"
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/listSkuTags", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<SkuTagCategoryVO>> queryAllSkuTags() {
        return ocmsServiceWrapper.queryTags();
    }

    @MethodDoc(
            displayName = "根据商品后台类目(ERP)查询渠道类目信息",
            description = "根据商品后台类目(ERP)查询渠道类目信息",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "根据商品后台类目(ERP)查询渠道类目信息",
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleUrl = "/pieapi/ocms/skuAndStock/queryChannelCategoryByErpCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryChannelCategoryByErpCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChannelCategoryRelationVO> queryChannelCategoryByErpCategory(@Valid @RequestBody QueryChannelCategoryInfoRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryChannelCategoryByErpCategory(user, request);
    }


    /**
     * 查询类目
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询渠道类目",
            description = "查询渠道类目",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道类目"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/item/queryChannelCategory",
            restExamplePostData = "{\"parentId\": \"0\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"list\": [{\"hasChildren\": 0,\"id\": \"001\",\"level\": 1,\"parentId\": \"01\",\"title\": \"水果\"}]},\"msg\": \"成功\"}"
    )
    @Auth
    @MethodLog(logRequest = true,logResponse = true,logger = "http")
    @RequestMapping(value = "/queryChannelCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChannelCategoryInfoResponse> queryChannelCategory(@RequestBody ChannelCategoryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryChannelCategorys(user.getTenantId(), request.getCategoryCode(), request.getChannelId(), request.getLevel());
    }


    @MethodDoc(
            displayName = "根据渠道id查询渠道类目",
            description = "根据渠道id查询渠道全部的渠道类目",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道类目"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/pieapi/ocms/skuAndStock/queryChannelCategoryByChannelId",
            restExamplePostData = "{\"channelId\": \"1000\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"list\": [{\"hasChildren\": 0,\"id\": \"001\",\"level\": 1,\"parentId\": \"01\",\"title\": \"水果\"}]},\"msg\": \"成功\"}"
    )
    @Auth
    @MethodLog(logRequest = true,logResponse = false,logger = "http")
    @RequestMapping(value = "/queryChannelCategoryByChannelId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChannelCategoryInfoResponse> queryChannelCategoryByChannelId(
            @RequestBody ChannelCategoryQueryByChannelIdRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        // 牵牛花零售商家获取美团渠道类目需要同时获取零售类目和配置的医药类目，背景：https://km.sankuai.com/collabpage/20379289
        boolean isMedUnmanned = ocmsServiceWrapper.isMedicineUnmannedMode(user.getTenantId());
        boolean isErp = ocmsServiceWrapper.isErpTenant(user.getTenantId());
        if (MccConfigUtil.isDisplayMtMedicineCategoryTree(user.getTenantId())) {
            if (!isMedUnmanned && !isErp && Objects.equals(request.getChannelId(), EnhanceChannelType.MT.getChannelId())) {
                return ocmsServiceWrapper.queryRetailAndMedCategoryTreeByChannelId(user.getTenantId(), request.getChannelId());
            }
        }
        return ocmsServiceWrapper.queryChannelCategoryByChannelId(user.getTenantId(), request.getChannelId());
    }


    @MethodDoc(
            displayName = "查询渠道类目资质",
            description = "查询渠道类目资质",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道类目资质"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/pieapi/ocms/skuAndStock/getQualification",
            restExamplePostData = "{\"channelId\": \"1000\",\"categoryId\": \"234214\"}"
    )
    @Auth
    @MethodLog(logRequest = true,logResponse = false,logger = "http")
    @RequestMapping(value = "/getQualification", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChannelCategoryQualificationVo> getQualification(@RequestBody ChannelQualificationByCategoryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setTenantId(user.getTenantId());
        return ocmsServiceWrapper.queryQualification(request);
    }


    @MethodDoc(
            displayName = "查询渠道类目售后服务信息",
            description = "查询渠道类目售后服务信息",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道类目售后服务信息"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/pieapi/ocms/skuAndStock/queryAfterSale",
            restExamplePostData = "{\"channelId\": \"1000\",\"categoryId\": \"234214\"}"
    )
    @Auth
    @MethodLog(logRequest = true,logResponse = false,logger = "http")
    @RequestMapping(value = "/queryAfterSale", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChannelCategoryAfterSaleResponse> queryAfterSale(
            @RequestBody QueryChannelCategoryAfterSaleRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryAfterSale(user.getTenantId(), request);
    }


    @MethodDoc(
            description = "查询标品库商品",
            displayName = "查询标品库商品",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品查询请求参数",
                            type = StandardSkuQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/querySp",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySp", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<PageQueryStandardSkuResponse> pageQuerySp(@Valid @RequestBody StandardSkuQueryRequest request) {
        return tenantProductWrapper.pageQuerySp(request);
    }

    @MethodDoc(
            description = "查询商品管理的所有货品单位列表",
            displayName = "查询商品管理的所有货品单位列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询商品管理的所有货品单位列表请求参数",
                            type = QueryCartonMeasureRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/skuAndStock/queryCartonMeasure",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCartonMeasure", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<CartonMeasureVO>> queryCartonMeasure(@RequestBody QueryCartonMeasureRequest request){
        return ocmsServiceWrapper.queryCartonMeasure(request);
    }
}
