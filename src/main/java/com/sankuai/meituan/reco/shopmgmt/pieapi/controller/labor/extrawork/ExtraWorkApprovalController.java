package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.labor.extrawork;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.AttendanceApprovalCreateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.ExtraWorkAttendanceApprovalCreateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.LaborExtraWorkApprovalServerWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

@InterfaceDoc(
        displayName = "加班申请相关接口",
        type = "restful",
        scenarios = "加班申请相关接口",
        description = "加班申请相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "加班申请相关接口")
@RestController
@RequestMapping("/pieapi/labor/management/extrawork/approval")
public class ExtraWorkApprovalController {

    @Resource
    private LaborExtraWorkApprovalServerWrapper extraWorkApprovalServerWrapper;

    @MethodDoc(
            displayName = "发起加班申请审批",
            description = "发起加班申请审批",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起加班申请审批",
                            type = AttendanceApprovalCreateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/extrawork/approval/create",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "发起加班申请审批")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/create", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Long> create(@RequestBody ExtraWorkAttendanceApprovalCreateRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), validateResult.get(), null);
        }
        return extraWorkApprovalServerWrapper.create(request);
    }
}
