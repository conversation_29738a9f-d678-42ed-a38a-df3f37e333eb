package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.sac.dto.model.PoiSelectDto;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/6/26
 */
@TypeDoc(
        name = "POI 选择的选项",
        description = "POI 选择的选项"
)
@Data
@ToString
public class PoiSelectVo {

    @FieldDoc(
            description = "POI ID，-1 特殊值，表示全部门店"
    )
    private Long poiId;

    @FieldDoc(
            description = "POI 名称"
    )
    private String poiName;

    @FieldDoc(
            description = "POI 状态"
    )
    private Integer poiStatus;

    @FieldDoc(
            description = "POI 类型"
    )
    private Integer entityType;

    @FieldDoc(
            description = "外部门店ID"
    )
    private String outPoiId;

    @FieldDoc(
            description = "门店发货方式"
    )
    private Integer shippingMode;

    @FieldDoc(
            description = "权限应用ID"
    )
    private Integer authAppId;

    @FieldDoc(
            description = "权限子应用ID，无子应用时和权限应用ID一致"
    )
    private Integer authSubAppId;

    public static PoiSelectVo fromDto(PoiSelectDto dto) {
        PoiSelectVo vo = new PoiSelectVo();
        if (dto != null) {
            vo.setPoiId(dto.getPoiId());
            vo.setPoiName(dto.getPoiName());
            vo.setPoiStatus(dto.getPoiStatus());
            vo.setEntityType(dto.getEntityType());
            vo.setOutPoiId(dto.getOutPoiId());
            vo.setShippingMode(dto.getShippingMode());
            vo.setAuthAppId(dto.getAuthAppId());
            vo.setAuthSubAppId(dto.getAuthSubAppId());
        }
        return vo;
    }

}
