package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant;

import com.meituan.linz.boot.enums.EnumBase;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.munich.assistant.client.constant.ExtraMapKey;
import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.munich.assistant.client.to.task.PageInfo;
import com.meituan.shangou.munich.assistant.client.to.task.SpuTaskTo;
import com.meituan.shangou.munich.assistant.client.to.task.TaskSummaryTo;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant.SpuPropertyVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant.SpuTaskVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant.TaskSummaryVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuGroupEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/1/25 15:44
 */
public class AssistantTaskConverter {

    private static final String INNER_TASK_JUMP_URL_PREFIX = "shuguopai://mrn?mrn_biz=sgemp&mrn_entry=merchandise-processor&mrn_component" +
            "=merchandise-processor&type=";

    public static final String TASK_QUERY_TYPE = "taskQueryType";

    public static final String POI_NAME_KEY = "poiName";

    public static final String POI_ID_KEY = "poiId";

    public static final String COLOR_KEY =  "color";

    public static final String TASK_COUNT_KEY = "dataValue";

    public static final String TIME_OUT_TASK_COUNT_KEY = "timeOutTaskCount";

    /**
     * 歪马场景，在多门店+今日待办场景时，该待办是否显示对应的门店列表，true显示，false不显示，默认显示
     */
    public static final String SHOW_TODO_TABLE_IN_MULTI_POI_KEY = "showTodoTableInMultiPoi";

    /**
     * 歪马场景，在多门店+今日待办场景时，该待办是否展示，true展示，false不展示，默认显示
     */
    public static final String SHOW_TODO_TASK_IN_MULTI_POI_KEY = "showTodoTaskInMultiPoi";

    public static final String DEFAULT_COLOR = "['#F2FAFF', '#ECF7FF']";


    /**
     * 转换为VO对象.
     *
     * @param taskSummaryTo 任务汇总Dto对象
     * @return vo对象
     */
    public static TaskSummaryVo convertToVo(TaskSummaryTo taskSummaryTo) {
        TaskSummaryVo taskSummaryVo = new TaskSummaryVo();

        taskSummaryVo.setTaskType(taskSummaryTo.getTaskType());
        taskSummaryVo.setTaskTypeName(taskSummaryTo.getTaskTypeName());
        taskSummaryVo.setStatus(taskSummaryTo.getStatus());
        taskSummaryVo.setTaskCount(taskSummaryTo.getTaskCount());
        taskSummaryVo.setDescription(taskSummaryTo.getDescription());
        taskSummaryVo.setTips(taskSummaryTo.getTips());
        taskSummaryVo.setImportant(ConverterUtils.nonNullConvert(
                EnumBase.ofCode(TaskTypeEnum.class, taskSummaryTo.getTaskType()), TaskTypeEnum::isImportant));
        taskSummaryVo.setJumpUrl(generateJumpUrl(taskSummaryTo));
        return taskSummaryVo;
    }

    public static MenuInfo convertSacMenu2AssistantMenu(SacMenuNodeDto sacMenuNodeDto) {
        MenuInfo menuInfo = new MenuInfo();
        menuInfo.setMenuCode(sacMenuNodeDto.getSacMenuCode());
        menuInfo.setMenuName(sacMenuNodeDto.getSacMenuName());
        menuInfo.setHasAuth(sacMenuNodeDto.isHasAuth());
        menuInfo.setUrl(sacMenuNodeDto.getRouteUrl());
        menuInfo.setRank(sacMenuNodeDto.getRank());
        menuInfo.setExtraInfoMap(StringUtils.isBlank(sacMenuNodeDto.getMetaData()) ? new HashMap<>() :
                JacksonUtils.parseMap(sacMenuNodeDto.getMetaData(), String.class, Object.class));
        menuInfo.setTaskQueryType(Integer.valueOf(menuInfo.getExtraInfoMap().getOrDefault(AssistantTaskConverter.TASK_QUERY_TYPE,"1").toString()));
        MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofAuthCode(sacMenuNodeDto.getSacMenuCode());
        if (Objects.nonNull(menuCodeEnum) && Objects.nonNull(menuCodeEnum.getGroupEnum())) {
            menuInfo.getExtraInfoMap().put(COLOR_KEY, menuCodeEnum.getGroupEnum().getColor());
        } else {
            menuInfo.getExtraInfoMap().put(COLOR_KEY, DEFAULT_COLOR);
        }
        return menuInfo;
    }

    public static MenuInfo convertTaskCenter2AssistantMenu(TaskSummaryTo taskSummaryTo) {
        MenuInfo menuInfo = new MenuInfo();
        menuInfo.setMenuCode(taskSummaryTo.getTaskTypeName());
        menuInfo.setMenuName(taskSummaryTo.getTaskTypeName());
        menuInfo.setHasAuth(true);
        menuInfo.setUrl(taskSummaryTo.getExtraInfoMap().get(ExtraMapKey.APP_URL));
        menuInfo.setRank(10000);

        menuInfo.setExtraInfoMap(new HashMap<>(taskSummaryTo.getExtraInfoMap()));
        Map<String, Object> extraInfoMap = menuInfo.getExtraInfoMap();

        extraInfoMap.put("unit", "个");
        if (taskSummaryTo.getTaskCount() > 0) {
            extraInfoMap.put(COLOR_KEY, taskSummaryTo.getExtraInfoMap().get(ExtraMapKey.COLOR));
        } else {
            extraInfoMap.put(COLOR_KEY, DEFAULT_COLOR);
        }
        extraInfoMap.put(AssistantTaskConverter.TASK_COUNT_KEY, String.valueOf(taskSummaryTo.getTaskCount()));

        return menuInfo;
    }

    private static String generateJumpUrl(TaskSummaryTo taskSummaryTo) {
        if (!EnumBase.ofCode(TaskTypeEnum.class, taskSummaryTo.getTaskType()).isExternal()) {
            return INNER_TASK_JUMP_URL_PREFIX + taskSummaryTo.getTaskType();
        }
        else {
            return MccConfigUtil.getAssistantTaskJumpUrl(taskSummaryTo.getTaskType());
        }
    }


    /**
     * 转换为VO对象.
     *
     * @param spuTaskTo 任务汇总Dto对象
     * @return vo对象
     */
    public static SpuTaskVo convertToSpuTaskVo(SpuTaskTo spuTaskTo) {
        SpuTaskVo spuTaskVo = new SpuTaskVo();

        spuTaskVo.setTaskId(spuTaskTo.getTaskId());
        spuTaskVo.setSpuId(spuTaskTo.getSpuId());
        spuTaskVo.setSpuName(spuTaskTo.getSpuName());
        spuTaskVo.setSpec(spuTaskTo.getSpec());
        spuTaskVo.setSales(spuTaskTo.getSales());
        spuTaskVo.setPrice(spuTaskTo.getPrice());
        spuTaskVo.setPicUrl(spuTaskTo.getPicUrl());
        spuTaskVo.setBadCaseNums(spuTaskTo.getBadCaseNums());
        spuTaskVo.setCreateDate(DateFormatUtils.format(spuTaskTo.getCreateTime(), "yyyy.MM.dd"));
        spuTaskVo.setSpuOffSaleTime(spuTaskTo.getOffSaleTime());
        spuTaskVo.setSpuProperties(buildSpuProperties(spuTaskTo));
        return spuTaskVo;
    }

    private static List<SpuPropertyVo> buildSpuProperties(SpuTaskTo spuTaskTo) {
        List<SpuPropertyVo> spuPropertyVos = new ArrayList<>();

        addSpecProperties(spuPropertyVos, "spec", "规格", spuTaskTo.getSpec());
        if (Objects.equals(spuTaskTo.getTaskType(), TaskTypeEnum.SKU_OFF_SALE_IN_STOCK.code())) {
            addSpecProperties(spuPropertyVos, "stock", "库存", spuTaskTo.getStock());
        }
        else {
            addSpecProperties(spuPropertyVos, "sales", "销量", spuTaskTo.getSales());
        }

        addSpecProperties(spuPropertyVos, "spuOffSaleTime", "下架时间", spuTaskTo.getOffSaleTime());
        addSpecProperties(spuPropertyVos, "badCaseNums", "近半月差评数", spuTaskTo.getBadCaseNums());

        return spuPropertyVos;
    }

    private static void addSpecProperties(List<SpuPropertyVo> spuPropertyVos, String key, String label, Object value) {
        if (value == null || StringUtils.isBlank(String.valueOf(value))) {
            return;
        }

        spuPropertyVos.add(SpuPropertyVo.of(key, label, String.valueOf(value)));

    }

    public static PageInfoVO convertPageInfoVo(PageInfo pageInfo) {
        return PageInfoVO.builder().page(pageInfo.getPage()).size(pageInfo.getSize())
                .totalPage(pageInfo.getTotalPage()).totalSize(pageInfo.getTotalSize())
                .build();
    }

}
