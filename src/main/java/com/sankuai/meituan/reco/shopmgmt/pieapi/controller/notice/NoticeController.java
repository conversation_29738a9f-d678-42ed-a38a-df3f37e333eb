package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.notice;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.shangou.saas.tenant.thrift.NoticeThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.notice.NoticeConfirmRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.notice.NoticeDetailResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.notice.NoticeDto;
import com.meituan.shangou.saas.tenant.thrift.dto.notice.NoticeQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020-12-09
 */
@InterfaceDoc(
        displayName = "公告服务",
        type = "restful",
        scenarios = "公告服务相关接口",
        description = "公告服务相关接口",
        authors = {
                "wangjian"
        },
        version = "V1.0"
)
@Api(value = "公告服务")
@Slf4j
@RestController
@RequestMapping("/pieapi/notice")
public class NoticeController {

    @Autowired
    private NoticeThriftService noticeThriftService;

    @MethodDoc(
            displayName = "弹出公告",
            description = "弹出公告",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "",
            restExamplePostData = "",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{}}"
    )
    @ApiOperation(value = "查询公告")
    @GetMapping(value = "/detail")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @Auth
    public CommonResponse<NoticeVo> queryDetail() {
        NoticeQueryRequest noticeQueryRequest = new NoticeQueryRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        noticeQueryRequest.setTenantId(user.getTenantId());
        noticeQueryRequest.setAccountId(user.getAccountId());
        noticeQueryRequest.setFrom(2);
        NoticeDetailResponse detail = noticeThriftService.getDetail(noticeQueryRequest);
        NoticeDto noticeDto = detail.getNoticeDto();
        if(noticeDto != null) {
            NoticeVo noticeVo = new NoticeVo();
            noticeVo.setId(noticeDto.getId());
            noticeVo.setContent(noticeDto.getContent());
            noticeVo.setButton(noticeDto.getButton());
            noticeVo.setUrl(noticeDto.getUrl());
            return CommonResponse.success(noticeVo);
        }
        return CommonResponse.success(null);

    }


    @MethodDoc(
            displayName = "确认公告",
            description = "确认公告",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "",
            restExamplePostData = "",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{}}"
    )
    @ApiOperation(value = "确认公告")
    @PostMapping(value = "/confirm")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @Auth
    public CommonResponse confirm(@RequestBody NoticeConfirmVoRequest request) {
        NoticeConfirmRequest noticeQueryRequest = new NoticeConfirmRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        noticeQueryRequest.setTenantId(user.getTenantId());
        noticeQueryRequest.setAccountId(user.getAccountId());
        noticeQueryRequest.setNoticeId(request.getId());
        noticeThriftService.confirm(noticeQueryRequest);
        return CommonResponse.success(null);
    }

}
