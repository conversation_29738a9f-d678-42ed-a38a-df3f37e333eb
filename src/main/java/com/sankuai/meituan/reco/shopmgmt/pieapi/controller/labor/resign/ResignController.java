package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.labor.resign;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryApprovalResignApplyListResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryResignApplyDetailResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryResignApplyListResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryWaitToResignApplyResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.ResignServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-12-09
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "员工下车服务",
        type = "restful",
        scenarios = "用于员工下车",
        description = "用于员工下车"
)
@RestController
@RequestMapping(value = "/pieapi/labor/management/resign")
@Slf4j
public class ResignController {

    @Resource
    private ResignServiceWrapper resignServiceWrapper;

    @MethodDoc(
            displayName = "获取待申请列表",
            description = "获取待申请列表",
            parameters = {
                    @ParamDoc(
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/queryWaitToApplyList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取待申请列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitToApplyList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryWaitToResignApplyResp> queryWaitToApplyList(@RequestBody QueryWaitToResignApplyListRequest request) {

        return resignServiceWrapper.queryWaitToResignApplyList(request);
    }

    @MethodDoc(
            displayName = "提交申请",
            description = "提交申请",
            parameters = {
                    @ParamDoc(
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/submitApply",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "提交申请")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/submitApply", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> submitApply(@RequestBody SubmitApplyRequest request) {

        return resignServiceWrapper.submitApply(request);
    }

    @MethodDoc(
            displayName = "查询申请列表",
            description = "查询申请列表",
            parameters = {
                    @ParamDoc(
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/queryApplyList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询申请列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryApplyList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryResignApplyListResp> queryApplyList(@RequestBody QueryResignApplyListRequest request) {

        return resignServiceWrapper.queryApplyList(request);
    }

    @MethodDoc(
            displayName = "查询待审批/已审批列表",
            description = "查询待审批/已审批列表",
            parameters = {
                    @ParamDoc(
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/queryApprovalList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询待审批/已审批列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryApprovalList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryApprovalResignApplyListResp> queryApprovalList(@RequestBody QueryResignApprovalListRequest request) {

        return resignServiceWrapper.queryApprovalList(request);
    }

    @MethodDoc(
            displayName = "查询待审批/已审批详情",
            description = "查询待审批/已审批详情",
            parameters = {
                    @ParamDoc(
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/queryWaitToApprovalDetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询待审批/已审批详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitToApprovalDetail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryResignApplyDetailResp> queryResignApprovalDetail(@RequestBody QueryResignApprovalDetailRequest request) {

        return resignServiceWrapper.queryResignApprovalDetail(request);
    }
}
