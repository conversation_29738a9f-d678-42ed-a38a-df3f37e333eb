package com.sankuai.meituan.reco.shopmgmt.pieapi.exception;


import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/3/8
 */
public class CommonLogicException extends RuntimeException {

    private ResultCode resultCode;
    private transient Object[] text;

    private boolean setMessage;

    public CommonLogicException() {
    }

    public CommonLogicException(String message) {
        super(message);
        setMessage = true;
    }

    public CommonLogicException(String message, Throwable cause) {
        super(message, cause);
        setMessage = true;
    }

    public CommonLogicException(Throwable cause) {
        super(cause);
    }

    public CommonLogicException(String message, ResultCode code) {
        super(message);
        setMessage = true;
        this.resultCode = code;
    }

    public CommonLogicException(String message, Throwable cause, ResultCode code) {
        super(message, cause);
        setMessage = true;
        this.resultCode = code;
    }

    public CommonLogicException(Throwable cause, ResultCode codeEnum) {
        super(cause);
        this.resultCode = codeEnum;
    }

    public CommonLogicException(ResultCode codeEnum) {
        this.resultCode = codeEnum;
    }

    public CommonLogicException(ResultCode codeEnum, Object... objects) {
        this.resultCode = codeEnum;
        this.text = objects;
    }

    public ResultCode getResultCode() {
        return resultCode;
    }

    public Object[] getText() {
        return text;
    }

    public boolean isSetMessage() {
        return setMessage;
    }
}
