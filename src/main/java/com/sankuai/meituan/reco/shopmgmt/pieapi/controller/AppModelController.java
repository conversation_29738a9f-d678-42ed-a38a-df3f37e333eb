package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.kafka.javaclient.common.errors.ApiException;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.munich.assistant.client.constant.ExtraMapKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuGroupEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.AbstractAppModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.WorkbenchTaskModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AssistantTaskWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StockAuthWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * app模块相关接口
 *
 * <AUTHOR>
 * @since 2021/7/2
 */
@InterfaceDoc(
        type = "restful",
        displayName = "app模块相关接口",
        description = "app模块相关接口",
        scenarios = "app模块相关接口",
        authors = {"liyu44"}
)
@Slf4j
@RestController
@RequestMapping("/pieapi/appmodel")
public class AppModelController {

    @Autowired
    private AssistantTaskWrapper taskWrapper;

    @Autowired
    private StockAuthWrapper stockAuthWrapper;

    @MethodDoc(
            description = "根据菜单code查询app模块信息",
            displayName = "根据菜单code查询app模块信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询app模块菜单信息请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/appmodel/queryMenuInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据菜单code查询app模块信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryMenuInfo", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Deprecated
    public CommonResponse<AppModelMenuInfo> queryMenuInfo(@Valid @RequestBody QueryMenuInfoRequest request) {
        MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofAuthCode(request.getMenuCode());
        if (menuCodeEnum == null) {
            return CommonResponse.fail2(ResultCode.MENU_CODE_NOT_EXIST);
        }
        try {
            AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(menuCodeEnum, request, null);
            // 保证兼容性，待灰度完成后删除
            if (MenuCodeEnum.WORKBENCH_PENDING_TASK.equals(menuCodeEnum)) {
                appModelMenuInfo.setSubMenus(appModelMenuInfo.getSubMenus().stream()
                        .filter(WorkbenchTaskModelMenuService::testShowMenuInfo).collect(Collectors.toList()));
                appModelMenuInfo.getSubMenus().sort(Comparator.comparingInt(MenuInfo::getRank));
            }
            if (appModelMenuInfo == null) {
                log.error("根据菜单code查询模块数据失败,request:{}", request);
                return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
            }
            return CommonResponse.success(appModelMenuInfo);
        }
        catch (Exception e) {
            log.error("根据菜单code查询模块数据异常,request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    @Auth
    @ApiOperation(value = "根据菜单code查询app模块信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryMenuInfoNew", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Deprecated
    public CommonResponse<AppModelMenuInfo> queryMenuInfoNew(@Valid @RequestBody QueryMenuInfoRequest request) {
        MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofAuthCode(request.getMenuCode());
        if (menuCodeEnum == null) {
            return CommonResponse.fail2(ResultCode.MENU_CODE_NOT_EXIST);
        }
        try {
            AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(menuCodeEnum, request, null);
            // 保证兼容性，待灰度完成后删除
            if (MenuCodeEnum.WORKBENCH_PENDING_TASK.equals(menuCodeEnum)) {
                appModelMenuInfo.setSubMenus(appModelMenuInfo.getSubMenus().stream()
                        .filter(WorkbenchTaskModelMenuService::testShowMenuInfo).collect(Collectors.toList()));
                appModelMenuInfo.getSubMenus().sort(Comparator.comparingInt(MenuInfo::getRank));
            }
            if (appModelMenuInfo == null) {
                log.error("根据菜单code查询模块数据失败,request:{}", request);
                return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
            }
            return CommonResponse.success(appModelMenuInfo);
        }
        catch (Exception e) {
            log.error("根据菜单code查询模块数据异常,request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    @MethodDoc(
            description = "根据菜单code查询app模块信息",
            displayName = "根据菜单code查询app模块信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询app模块菜单信息请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/appmodel/queryMenuInfoList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据菜单code查询app模块信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryMenuInfoList", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<List<AppModelMenuInfo>> queryMenuInfoList(@Valid @RequestBody QueryMenuInfoRequest request) {
        MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofAuthCode(request.getMenuCode());
        if (!MenuCodeEnum.WORKBENCH_PENDING_TASK.equals(menuCodeEnum)) {
            return CommonResponse.fail2(ResultCode.MENU_CODE_NOT_EXIST);
        }
        try {
            AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(menuCodeEnum, request, true);
            if (appModelMenuInfo == null) {
                log.error("根据菜单code查询模块数据失败,request:{}", request);
                return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
            }

            // 单门店需解析过滤库存权限
            List<AppModelMenuInfo> appModelMenuInfoList = buildAppModelMenuInfoList(appModelMenuInfo);
            Long poiId;
            try {
                poiId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
            } catch (Exception e2) {
                log.info("非单门店不过滤权限,request:{}, e2:{}", request, e2.getMessage());
                poiId = null;
            }
            if (poiId != null) {
                // 根据库存对接配置过滤相关库存待办模块权限
                stockAuthWrapper.filterAppMenuResultByStockConfig(
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), poiId,
                        appModelMenuInfoList);
            }
            return CommonResponse.success(appModelMenuInfoList);
        }
        catch (Exception e) {
            log.error("根据菜单code查询模块数据异常,request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    @MethodDoc(
            description = "根据菜单code查询app模块信息",
            displayName = "根据菜单code查询app模块信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询app模块菜单信息请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/appmodel/queryMenuInfoDetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据菜单code查询app模块信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryMenuInfoDetail", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<List<AppModelMenuInfo>> queryMenuInfoDetail(@Valid @RequestBody QueryMenuInfoRequest request) {
        MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofAuthCode(request.getMenuCode());
        if (!MenuCodeEnum.WM_PROCESSING_TASK.equals(menuCodeEnum)) {
            return CommonResponse.fail2(ResultCode.MENU_CODE_NOT_EXIST);
        }
        try {
            AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(menuCodeEnum, request, null);
            if (appModelMenuInfo == null) {
                log.error("根据菜单code查询模块数据失败,request:{}", request);
                return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
            }
            List<AppModelMenuInfo> appModelMenuInfos = buildWaiMaSupervisionMenuTab(appModelMenuInfo);
            waiMaTodoTabPostProcessor(appModelMenuInfo);
            return CommonResponse.success(appModelMenuInfos);
        }
        catch (Exception e) {
            log.error("根据菜单code查询模块数据异常,request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }


    private List<AppModelMenuInfo> buildWaiMaSupervisionMenuTab(AppModelMenuInfo appModelMenuInfo) {
        List<AppModelMenuInfo> result = new ArrayList<>();
        AppModelMenuInfo supervisionMenuInfo = new AppModelMenuInfo();
        MenuInfo parentMenuInfo = taskWrapper.getWaiMaSupervisorMenuInfo();
        if (Objects.isNull(parentMenuInfo)) {
            log.warn("歪马工作台没有配置总部监管任务 identity:{}",
                    JacksonUtils.toJson(ApiMethodParamThreadLocal.getIdentityInfo()));
            result.add(appModelMenuInfo);
            return result;
        }
        supervisionMenuInfo.setParentMenu(parentMenuInfo);
        supervisionMenuInfo.setSubMenus(Collections.emptyList());
        result.add(supervisionMenuInfo);
        result.add(appModelMenuInfo);

        if (!parentMenuInfo.isHasAuth()) {
            supervisionMenuInfo.setHideParentMenu(Boolean.TRUE);
            log.info("没有总部监管权限，不展示总部监管信息, identity:{}",
                    JacksonUtils.toJson(ApiMethodParamThreadLocal.getIdentityInfo()));
            return result;
        }

        if (ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().size() <= 1) {
            supervisionMenuInfo.setHideParentMenu(Boolean.TRUE);
            log.info("门店数量 <= 1，不展示总部监管信息,  identity:{}",
                    JacksonUtils.toJson(ApiMethodParamThreadLocal.getIdentityInfo()));
            return result;
        }

        // poiId + taskName 的table
        HashBasedTable<String, MenuInfo, MenuInfo> taskPoiMenuInfo = HashBasedTable.create();
        appModelMenuInfo.getSubMenus().forEach(menuInfo ->
                menuInfo.getChildren().forEach(child ->
                        taskPoiMenuInfo.put(child.getMenuName(), menuInfo, child)));

        // 所有有延时任务的门店数
        List<MenuInfo> supervisionSubMenu = buildPoiDimMenuInfo(taskPoiMenuInfo);
        parentMenuInfo.setTaskCount((int) supervisionSubMenu.stream().filter(menu -> menu.getDelayTaskCount() > 0).count());
        supervisionMenuInfo.setSubMenus(supervisionSubMenu);
        supervisionMenuInfo.setHideParentMenu(Boolean.FALSE);
        return result;
    }


    private List<MenuInfo> buildPoiDimMenuInfo(HashBasedTable<String, MenuInfo, MenuInfo> taskPoiMenuInfo) {
        List<MenuInfo> subMenuInfo = taskPoiMenuInfo.rowKeySet().stream().map(row -> { // 门店id
            int poiTaskCount = taskPoiMenuInfo.row(row).values().stream().mapToInt(MenuInfo::getTaskCount).sum();
            int poiDelayTaskCount = taskPoiMenuInfo.row(row).values().stream().mapToInt(MenuInfo::getDelayTaskCount).sum();
            MenuInfo poiMenuInfo = new MenuInfo();
            poiMenuInfo.setTaskCount(poiTaskCount);
            poiMenuInfo.setDelayTaskCount(poiDelayTaskCount);
            poiMenuInfo.setHasAuth(true);
            List<MenuInfo> menuInfos = new ArrayList<>();
            taskPoiMenuInfo.columnKeySet().forEach(column -> { //任务信息
                MenuInfo menuInfo = taskPoiMenuInfo.get(row, column);
                if (Objects.nonNull(menuInfo)) { // 为null，则表示没有该门店，没有该待办的权限
                    poiMenuInfo.setMenuName(menuInfo.getMenuName());
                    poiMenuInfo.setMenuCode(menuInfo.getMenuCode());
                    MenuInfo taskMenuInfo = new MenuInfo();
                    taskMenuInfo.setMenuCode(column.getMenuCode());
                    taskMenuInfo.setMenuName(column.getMenuName());
                    taskMenuInfo.setTaskCount(menuInfo.getTaskCount());
                    taskMenuInfo.setDelayTaskCount(menuInfo.getDelayTaskCount());
                    taskMenuInfo.setHasAuth(true);
                    taskMenuInfo.setExtraInfoMap(column.getExtraInfoMap());
                    menuInfos.add(taskMenuInfo);
                }
            });

            // 排序，首先按照延时数量排序，再按照待办数量排序
            List<MenuInfo> sortedMenuInfo = menuInfos.stream().sorted(
                            Comparator.comparing(MenuInfo::getDelayTaskCount).reversed()
                                    .thenComparing(Comparator.comparing(MenuInfo::getTaskCount).reversed()))
                    .collect(Collectors.toList());
            poiMenuInfo.setChildren(sortedMenuInfo);
            return poiMenuInfo;
        }).collect(Collectors.toList());

        // 排序，首先按照延时数量排序，再按照待办数量排序
        return subMenuInfo.stream().sorted(
                        Comparator.comparing(MenuInfo::getDelayTaskCount).reversed()
                                .thenComparing(Comparator.comparing(MenuInfo::getTaskCount).reversed()))
                .collect(Collectors.toList());
    }


    private void waiMaTodoTabPostProcessor(AppModelMenuInfo todoMenuTab) {
        if (ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().size() <= 1) {
            log.info("门店数量 <= 1，不处理今日待办列表 identity:{}",
                    JacksonUtils.toJson(ApiMethodParamThreadLocal.getIdentityInfo()));
            return;
        }

        Iterator<MenuInfo> iterator = todoMenuTab.getSubMenus().iterator();
        while (iterator.hasNext()) {
            MenuInfo next = iterator.next();
            Map<String, Object> extraInfoMap = next.getExtraInfoMap();
            // 在多门店场景时，不展示该待办
            if (Boolean.FALSE.equals(extraInfoMap.getOrDefault(AssistantTaskConverter.SHOW_TODO_TASK_IN_MULTI_POI_KEY, true))) {
                iterator.remove();
                continue;
            }
            // 在多门店场景时，不显示对应的门店列表
            if (Boolean.FALSE.equals(extraInfoMap.getOrDefault(AssistantTaskConverter.SHOW_TODO_TABLE_IN_MULTI_POI_KEY, true))) {
                next.setChildren(Collections.emptyList());
            }
        }
    }

    private List<AppModelMenuInfo> buildAppModelMenuInfoList(AppModelMenuInfo appModelMenuInfo) {
        ArrayList<AppModelMenuInfo> result = Lists.newArrayList(appModelMenuInfo);
        HashMap<MenuGroupEnum, AppModelMenuInfo> groupMenuInfoMap = Maps.newHashMap();
        appModelMenuInfo.getSubMenus().forEach(menu -> {
            MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofAuthCode(menu.getMenuCode());
            if (Objects.nonNull(menuCodeEnum)) {
                MenuGroupEnum groupEnum = menuCodeEnum.getGroupEnum();
                AppModelMenuInfo groupAppMenuInfo = groupMenuInfoMap.computeIfAbsent(groupEnum, groupMenu ->
                        initAppModeMenuInfo(appModelMenuInfo, groupMenu));
                groupAppMenuInfo.getSubMenus().add(menu);
            } else if (MapUtils.isNotEmpty(menu.getExtraInfoMap())
                    && menu.getExtraInfoMap().containsKey(ExtraMapKey.TASK_CENTER_TYPE)
                    && Boolean.parseBoolean(menu.getExtraInfoMap().get(ExtraMapKey.TASK_CENTER_TYPE).toString())) {
                MenuGroupEnum menuGroupEnum = MenuGroupEnum.ofGroupDesc(menu.getExtraInfoMap().get(ExtraMapKey.FIRST_TASK_TYPE_NAME).toString());
                if (Objects.isNull(menuGroupEnum)) { // 没找到menu
                    log.error("任务中心分组未接入待办, menuInfo:{}", JacksonUtils.toJson(menu));
                    return;
                }
                AppModelMenuInfo taskCenterGroupMenuInfo = groupMenuInfoMap.computeIfAbsent(menuGroupEnum, groupMenu ->
                        initAppModeMenuInfo(appModelMenuInfo, groupMenu));
                taskCenterGroupMenuInfo.getSubMenus().add(menu);
            } else {
                log.error("待办未接入, menuInfo:{}", JacksonUtils.toJson(menu));
            }
        });
        ArrayList<AppModelMenuInfo> appModelMenuInfos = new ArrayList<>(groupMenuInfoMap.values());
        appModelMenuInfos.sort(Comparator.comparingInt(k -> k.getParentMenu().getRank()));
        result.addAll(appModelMenuInfos);
        // 过滤掉不显示的情况
        appModelMenuInfo.setSubMenus(appModelMenuInfo.getSubMenus().stream()
                .filter(WorkbenchTaskModelMenuService::testDataValueNotZeroNotPick)
                .collect(Collectors.toList()));
        // 为保证兼容性，这里硬编码进去
        appModelMenuInfo.getParentMenu().setMenuName(MenuGroupEnum.ALL.getDesc());
        return result;
    }

    private AppModelMenuInfo initAppModeMenuInfo(AppModelMenuInfo appModelMenuInfo, MenuGroupEnum menu) {
        AppModelMenuInfo info = new AppModelMenuInfo();
        MenuInfo menuInfo = new MenuInfo();
        menuInfo.setMenuCode(menu.name());
        menuInfo.setMenuName(menu.getDesc());
        menuInfo.setRank(menu.ordinal());
        menuInfo.setHasAuth(appModelMenuInfo.getParentMenu().isHasAuth());
        info.setParentMenu(menuInfo);
        info.setHideParentMenu(false);
        info.setSubMenus(Lists.newArrayList());
        return info;
    }

    private AppModelMenuInfo initTaskCenterMenuInfo(MenuGroupEnum menu) {
        AppModelMenuInfo info = new AppModelMenuInfo();
        MenuInfo menuInfo = new MenuInfo();
        menuInfo.setMenuCode(menu.name());
        menuInfo.setMenuName(menu.getDesc());
        menuInfo.setRank(menu.ordinal());
        menuInfo.setHasAuth(true);
        info.setParentMenu(menuInfo);
        info.setHideParentMenu(false);
        info.setSubMenus(Lists.newArrayList());
        return info;
    }

}