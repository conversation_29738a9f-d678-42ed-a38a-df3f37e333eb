package com.sankuai.meituan.reco.shopmgmt.pieapi.converters;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.ScmChannelGoodsVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplierGoodsInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase.SupplierGoodsVO;
import com.sankuai.meituan.reco.supplychain.purchase.client.common.SupplierGoodsTypeEnum;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.dto.goods.SupplierGoodsDTO;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
public class SupplierGoodsConverter {
    public static SupplierGoodsVO buildSupplierGoods(SupplierGoodsDTO supplierGoods) {
        return Optional.ofNullable(supplierGoods)
                .map(sg -> SupplierGoodsVO.builder()
                        .goodsId(sg.getGoodsId())
                        .channelGoods(Optional.of(sg).map(SupplierGoodsDTO::getChannelGoods).map(cg -> ScmChannelGoodsVO.builder()
                                .channelId(cg.getChannelId())
                                .goodsId(cg.getGoodsId())
                                .channelSpecId(cg.getChannelSpecId())
                                .channelGoodsId(cg.getChannelGoodsId())
                                .channelGoodsName(cg.getChannelGoodsName())
                                .channelSupplierId(cg.getChannelSupplierId())
                                .channelSupplierName(cg.getChannelSupplierName())
                                .picUrlList(cg.getPicUrlList())
                                .detailUrl(cg.getDetailPcUrl())
                                .channelSpecName(cg.getChannelSpecName())
                                .saleQuantity(cg.getSaleQuantity())
                                .goodsMinPrice(Optional.ofNullable(cg.getGoodsMinPrice()).map(BigDecimal::valueOf).map(PriceConverter::calcDivide1000PriceValue).orElse(BigDecimal.ZERO.toPlainString()))
                                .specPrice(Optional.ofNullable(cg.getSpecPrice()).map(BigDecimal::valueOf).map(PriceConverter::calcDivide1000PriceValue).orElse(BigDecimal.ZERO.toPlainString()))
                                .stock(cg.getStock())
                                .unit(cg.getUnit())
                                .status(cg.getStatus())
                                .minOrderQuantity(cg.getMinOrderQuantity())
                                .openLadderPrice(cg.getOpenLadderPrice())
                                .batchNumber(cg.getBatchNumber())
                                .serviceChargeType(cg.getServiceChargeType())
                                .firstUnit(cg.getFirstUnit())
                                .firstUnitFee(cg.getFirstUnitFee())
                                .nextUnit(cg.getNextUnit())
                                .nextUnitFee(cg.getNextUnitFee())
                                .firstUnitPrice(cg.getFirstUnitPrice())
                                .nextUnitPrice(cg.getNextUnitPrice())
                                .build()).orElse(null))
                        .type(sg.getType())
                        .supplierGoodsInfo(buildSupplierGoodsInfoVO(sg.getType(), sg.getGoodsId(), sg.getGoodsName(), sg.getChannelGoodsId(), sg.getExtend()))
                        .build())
                .orElse(null);
    }

    /**
     * 构建供应商商品信息对象
     *
     * @param supplierGoodsType 供应商商品类型
     * @param goodsId           商品ID
     * @param name              商品名称
     * @param code              商品编码
     * @param extend            供应商商品扩展信息
     * @return 构建的供应商商品信息对象
     */
    private static SupplierGoodsInfoVO buildSupplierGoodsInfoVO(Integer supplierGoodsType, String goodsId, String name, String code, SupplierGoodsDTO.ScmSupplierGoodsExtend extend) {
        // 如果供应商商品类型为渠道货品，则返回null
        if (Objects.equals(supplierGoodsType, SupplierGoodsTypeEnum.DEFAULT.getCode())) {
            return null;
        }
        // 创建供应商商品信息对象
        SupplierGoodsInfoVO target = new SupplierGoodsInfoVO();
        // 设置商品ID
        target.setGoodsId(goodsId);
        // 设置商品名称
        target.setName(name);
        // 设置商品编码
        target.setCode(code);
        // 如果供应商商品扩展信息不为空，则设置商品规格、商品链接和商品评论
        if (Objects.nonNull(extend)) {
            // 设置商品规格
            target.setSpec(extend.getProductSpec());
            // 设置商品链接
            target.setUrl(extend.getProductUrl());
            // 设置商品评论
            target.setComment(extend.getComment());
        }
        return target.isAllFieldsBlank() ? null : target;
    }


}
