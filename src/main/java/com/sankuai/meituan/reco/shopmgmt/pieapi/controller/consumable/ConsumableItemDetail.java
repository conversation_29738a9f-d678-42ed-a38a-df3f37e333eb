package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.quartz.SimpleTrigger;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsumableItemDetail {

    @FieldDoc(description = "skuName")
    private String skuName;

    @FieldDoc(description = "skuId")
    private String skuId;

    @NotBlank
    @FieldDoc(description = "单次出库上限")
    private String maxQuantity;

    @FieldDoc(description = "0-普通耗材 1-封签 2-封签容具")
    private Integer type;

    @FieldDoc(description = "预警库存")
    private String warnStockQuantity;

    @FieldDoc(description = "是否需要校验库存")
    private Boolean needCheckStockQuantity;

    @FieldDoc(description = "库存量")
    private String currentStockQuantity;

}
