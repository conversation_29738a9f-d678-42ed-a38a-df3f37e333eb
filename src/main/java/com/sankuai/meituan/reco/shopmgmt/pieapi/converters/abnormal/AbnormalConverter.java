package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.abnormal;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryStoreSpuAbnormalRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalRuleNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.product.ChannelNameService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SpringAppContext;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalProductInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalRuleNodeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.AbnormalJumpPageType;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchQueryPoiSpuAbnormalRequest;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.enums.MergedAuditStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/5/30 19:18
 */
public class AbnormalConverter {

    private static ChannelNameService channelNameService = SpringAppContext.AppContext.getBean(ChannelNameService.class);

    public static final String UNKNOWN_CHANNEL = "未知";

    public static final String GOODS_DIFFER = "商品不一致";
    public static final String GOODS_DIFFER_MERGE_ABNORMAL_CODE = "99991";
    public static final String GOODS_EXISTS = "商品存在";

    public static String getAbnormalMarkMsg(List<AbnormalProductInfoDTO> abnormalProductInfos) {
        if (CollectionUtils.isEmpty(abnormalProductInfos)) {
            return null;
        }

        AbnormalProductInfoDTO firstAbnormalProduct = abnormalProductInfos.get(0);
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.findByChannelId(firstAbnormalProduct.getChannelId());
        String abnormalDesc = firstAbnormalProduct.getAbnormalDesc();
        String abnormalHandleMsg = firstAbnormalProduct.getAbnormalHandleMsg();

        String abnormalMarkMsg = "【" + getChannelEnumDesc(channelTypeEnum) + abnormalDesc + "】";
        if (abnormalProductInfos.size() == 1) {
            return abnormalMarkMsg + abnormalHandleMsg;
        } else {
            return "存在" + abnormalMarkMsg + "等" + abnormalProductInfos.size() + "项问题";
        }
    }

    public static String getMergedAuditComment(Integer channelId, Integer mergedAuditStatus) {
        String channelDesc = AbnormalConverter.getChannelEnumDesc(channelId);
        if (MergedAuditStatusEnum.AUDIT_REJECT.getCode().equals(mergedAuditStatus)) {
            return channelDesc + "审核驳回，请根据驳回原因修正商品信息后重新同步";
        } else if (MergedAuditStatusEnum.AUDIT_ING.getCode().equals(mergedAuditStatus)) {
            return channelDesc + "审核中，审核通过后自动上架售卖";
        } else {
            return channelDesc + "审核通过";
        }
    }

    public static String getChannelEnumDesc(Integer channelId) {
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.findByChannelId(channelId);
        return getChannelEnumDesc(channelTypeEnum);
    }

    public static String getChannelEnumDesc(ChannelTypeEnum channelTypeEnum) {
        if (channelTypeEnum == null) {
            return UNKNOWN_CHANNEL;
        }

        switch (channelTypeEnum) {
            case MEITUAN:
                return "美团";
            case ELEM:
                return "饿了么";
            case JD2HOME:
                return "京东";
            case QNH:
                return "";
            default:
                return channelNameService.getChannelName(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), channelTypeEnum.getChannelId());
        }
    }

    public static BatchQueryPoiSpuAbnormalRequest convertToPoiSpuAbnormalQueryRequest(QueryStoreSpuAbnormalRequest request) {
        BatchQueryPoiSpuAbnormalRequest batchQueryPoiSpuAbnormalRequest = new BatchQueryPoiSpuAbnormalRequest();
        batchQueryPoiSpuAbnormalRequest.setMerchantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        batchQueryPoiSpuAbnormalRequest.setChannelId(request.getChannelId());
        batchQueryPoiSpuAbnormalRequest.setPoiId(request.getStoreId());
        batchQueryPoiSpuAbnormalRequest.setSpuIds(Arrays.asList(request.getSpuId()));
        batchQueryPoiSpuAbnormalRequest.setFilterByAbnormalCode(true);
        batchQueryPoiSpuAbnormalRequest.setNeedDiffInfo(request.getNeedDiffInfo());
        batchQueryPoiSpuAbnormalRequest.setMtOriginQualityProblem(request.getOriginQualityProblem());
        return batchQueryPoiSpuAbnormalRequest;
    }

    public static AbnormalRuleNodeVO convert(AbnormalRuleNodeDTO abnormalRuleDto, Map<String, List<AbnormalRuleNodeDTO>> parentCodeMap) {
        AbnormalRuleNodeVO abnormalRuleNode = new AbnormalRuleNodeVO();
        abnormalRuleNode.setChannelId(abnormalRuleDto.getChannelId());
        abnormalRuleNode.setAbnormalCode(abnormalRuleDto.getAbnormalCode());
        abnormalRuleNode.setAbnormalDesc(abnormalRuleDto.getAbnormalDesc());
        abnormalRuleNode.setLevel(abnormalRuleDto.getLevel());
        abnormalRuleNode.setParentCode(abnormalRuleDto.getParentCode());
        // 子异常类型
        List<AbnormalRuleNodeDTO> childAbnormalRules = parentCodeMap.getOrDefault(abnormalRuleDto.getAbnormalCode(), Collections.emptyList());
        List<AbnormalRuleNodeVO> children = Fun.map(childAbnormalRules, rule -> convert(rule, parentCodeMap));
        List<AbnormalRuleNodeVO> otherRule = Fun.filter(children, abnormalRuleNodeVO -> abnormalRuleNodeVO.getAbnormalDesc().contains("其他"));
        children = Fun.filter(children, abnormalRuleNodeVO -> !abnormalRuleNodeVO.getAbnormalDesc().contains("其他"));
        children.addAll(otherRule);
        abnormalRuleNode.setChildren(children);
        return abnormalRuleNode;
    }

    /**
     * 合并不一致异常
     * @param abnormalProductInfos
     * @return
     */
    public  static Pair<List<Integer>, List<AbnormalProductInfoDTO>> mergeDiffCompareAbnormals(List<AbnormalProductInfoDTO> abnormalProductInfos) {
        if (CollectionUtils.isEmpty(abnormalProductInfos)){
            return Pair.of(Collections.emptyList(), abnormalProductInfos);
        }

        List<AbnormalProductInfoDTO> diffCompareAbnormalList = Fun.filter(abnormalProductInfos, t -> Objects.equals(t.getJumpPageType(), AbnormalJumpPageType.STORE_SPU_DIFF_COMPARE_LIST.getType()));
        if (CollectionUtils.isEmpty(diffCompareAbnormalList)) {
            return Pair.of(Collections.emptyList(), abnormalProductInfos);
        }

        StringBuilder abnormalHandleMsg = new StringBuilder(GOODS_EXISTS);
        for (int i = 0; i < diffCompareAbnormalList.size(); i++) {
            abnormalHandleMsg.append("（").append(diffCompareAbnormalList.get(i).getAbnormalDesc()).append("）");
            if (i != diffCompareAbnormalList.size() - 1){
                abnormalHandleMsg.append("、");
            }
        }
        AbnormalProductInfoDTO abnormalProductInfoDTO = new AbnormalProductInfoDTO();
        abnormalProductInfoDTO.setChannelId(ChannelTypeEnum.QNH.getChannelId());
        abnormalProductInfoDTO.setAbnormalCode(GOODS_DIFFER_MERGE_ABNORMAL_CODE);
        abnormalProductInfoDTO.setAbnormalDesc(GOODS_DIFFER);
        abnormalProductInfoDTO.setAbnormalHandleMsg(abnormalHandleMsg.toString());
        abnormalProductInfoDTO.setJumpPageType(AbnormalJumpPageType.STORE_SPU_DIFF_COMPARE_LIST.getType());
        List<AbnormalProductInfoDTO> abnormalList = Fun.filter(abnormalProductInfos, t -> !Objects.equals(t.getJumpPageType(), AbnormalJumpPageType.STORE_SPU_DIFF_COMPARE_LIST.getType()));
        List<AbnormalProductInfoDTO> mergedAbnormalProductInfos = new ArrayList<>(abnormalList);
        mergedAbnormalProductInfos.add(abnormalProductInfoDTO);

        List<Integer> diffCompareTypeList = Fun.map(diffCompareAbnormalList, AbnormalProductInfoDTO::getDiffCompareType);
        return Pair.of(diffCompareTypeList, mergedAbnormalProductInfos);
    }
}
