package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/06/13 11:21:35
 * @email <EMAIL>
 */
@TypeDoc(description = "获取门店类型的请求")
@AllArgsConstructor
@Data
public class GetPoiOperationModeRequest {

    @FieldDoc(description = "门店id")
    @NotNull
    private Long poiId;

}
