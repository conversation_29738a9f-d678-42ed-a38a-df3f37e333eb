package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.sku;

import com.dianping.zebra.util.StringUtils;
import com.sankuai.meituan.reco.pickselect.thrift.Tag;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.dto.WaitPickOrderInfoDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.dto.WaitPickTaskInfoDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.OrderForPickAndOutwardSourcingVO;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuForPickAndOutwardSourcingVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.TagInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


/**
 * @Author: <EMAIL>
 * @Date: 2020/11/12 16:40
 * @Description:
 */

@Mapper(componentModel = "spring", imports = {ChannelTypeConvertUtils.class})
public interface SkuForPickAndOutwardSourcingConverter {

    /**
     * 展示需要用的属性分隔符
     */
    String SHOW_ATTR_SPILT = "/";

    /**
     * 下发生成Task用的属性分隔符
     */
    String DISPATCH_ATTR_SPILT = "+";

    /**
     * 是赠品
     */
    int GIFT_TYPE = 1;

    /**
     * 赠品描述
     */
    String giftPrefix = "[赠品]";

    @Mapping(target = "fulFillWorkOrderId", source = "fulfillWOId")
    @Mapping(target = "daySeq", source = "daySeq")
    @Mapping(target = "daySeqNum", source = "daySeqNum")
    @Mapping(target = "channelName", source = "sourceName")
    @Mapping(target = "caution", source = "caution")
    @Mapping(target = "skuCount", source = "skuCount")
    @Mapping(target = "pushTime", source = "pushTime")
    @Mapping(target = "warningDuration", source = "warningDuration")
    @Mapping(target = "isReserved", source = "isReserved")
    @Mapping(target = "deliverTime", source = "deliverTime")
    @Mapping(target = "orderId", source = "unifyOrderId")
    @Mapping(target = "channelId", expression = "java(ChannelTypeConvertUtils.convert(waitPickOrderInfoDTO.getSourceId()))")
    OrderForPickAndOutwardSourcingVO convertOrderForPickAndOutwardSourcingVO(WaitPickOrderInfoDTO waitPickOrderInfoDTO);


    @Mapping(target = "fulFillWorkOrderId", source = "fulfillWOId")
    @Mapping(target = "daySeq", source = "daySeq")
    @Mapping(target = "daySeqNum", source = "daySeqNum")
    @Mapping(target = "channelName", source = "sourceName")
    @Mapping(target = "caution", source = "caution")
    @Mapping(target = "skuCount", source = "skuCount")
    @Mapping(target = "pushTime", source = "pushTime")
    @Mapping(target = "warningDuration", source = "warningDuration")
    @Mapping(target = "isReserved", source = "isReserved")
    @Mapping(target = "deliverTime", source = "deliverTime")
    @Mapping(target = "orderId", source = "unifyOrderId")
    @Mapping(target = "channelId", expression = "java(ChannelTypeConvertUtils.convert(waitPickOrderInfoDTO.getSourceId()))")
    OrderForPickAndOutwardSourcingVO convertPickOrderForPickAndOutwardSourcingVO(com.sankuai.meituan.reco.pickselect.thrift.picking.dto.WaitPickOrderInfoDTO waitPickOrderInfoDTO);


    @Mapping(target = "fulFillWorkOrderId", source = "fulfillWOId")
    @Mapping(target = "skuName", source = "itemName")
    @Mapping(target = "itemName", expression = "java(buildItemName(waitPickTaskInfoDTO))")
    @Mapping(target = "itemNum", source = "itemNum")
    @Mapping(target = "picUrl", source = "picUrl")
    @Mapping(target = "pickingtaskId", source = "pickingtaskId")
    @Mapping(target = "pickingWorkOrderId", source = "pickingWorkOrderId")
    @Mapping(target = "spec", source = "spec")
    @Mapping(target = "attribute", source = "attribute")
    @Mapping(target = "skuId", source = "storeSku")
    @Mapping(target = "giftType", source = "giftType")
    @Mapping(target = "tagInfoList", source = "tagList")
    SkuForPickAndOutwardSourcingVO convertSkuForPickAndOutwardSourcingVO(WaitPickTaskInfoDTO waitPickTaskInfoDTO);

    @Mapping(target = "fulFillWorkOrderId", source = "fulfillWOId")
    @Mapping(target = "skuName", source = "itemName")
    @Mapping(target = "itemName", expression = "java(buildPickItemName(waitPickTaskInfoDTO))")
    @Mapping(target = "itemNum", source = "itemNum")
    @Mapping(target = "picUrl", source = "picUrl")
    @Mapping(target = "pickingtaskId", source = "pickingtaskId")
    @Mapping(target = "pickingWorkOrderId", source = "pickingWorkOrderId")
    @Mapping(target = "spec", source = "spec")
    @Mapping(target = "attribute", source = "attribute")
    @Mapping(target = "skuId", source = "storeSku")
    @Mapping(target = "giftType", source = "giftType")
    @Mapping(target = "tagInfoList", source = "tagList")
    SkuForPickAndOutwardSourcingVO convertPickSkuForPickAndOutwardSourcingVO(com.sankuai.meituan.reco.pickselect.thrift.picking.dto.WaitPickTaskInfoDTO waitPickTaskInfoDTO);

    @Mapping(target = "type", source = "type")
    @Mapping(target = "name", source = "name")
    TagInfoVO convertTagInfoVO(Tag tag);


    default String buildItemName(WaitPickTaskInfoDTO waitPickTaskInfoDTO) {
        StringBuilder itemNameStringBuilder = new StringBuilder();
        int giftType = waitPickTaskInfoDTO.getGiftType();
        if (GIFT_TYPE == giftType) {
            itemNameStringBuilder.append(giftPrefix);
        }
        itemNameStringBuilder.append(waitPickTaskInfoDTO.getItemName());

        String attr = waitPickTaskInfoDTO.getAttribute();
        if (StringUtils.isNotBlank(attr)) {
            itemNameStringBuilder.append("(");
            itemNameStringBuilder.append(attr.replace(DISPATCH_ATTR_SPILT, SHOW_ATTR_SPILT));
            itemNameStringBuilder.append(")");
        }
        return itemNameStringBuilder.toString();
    }

    default String buildPickItemName(com.sankuai.meituan.reco.pickselect.thrift.picking.dto.WaitPickTaskInfoDTO waitPickTaskInfoDTO) {
        StringBuilder itemNameStringBuilder = new StringBuilder();
        int giftType = waitPickTaskInfoDTO.getGiftType();
        if (GIFT_TYPE == giftType) {
            itemNameStringBuilder.append(giftPrefix);
        }
        itemNameStringBuilder.append(waitPickTaskInfoDTO.getItemName());

        String attr = waitPickTaskInfoDTO.getAttribute();
        if (StringUtils.isNotBlank(attr)) {
            itemNameStringBuilder.append("(");
            itemNameStringBuilder.append(attr.replace(DISPATCH_ATTR_SPILT, SHOW_ATTR_SPILT));
            itemNameStringBuilder.append(")");
        }
        return itemNameStringBuilder.toString();
    }

}
