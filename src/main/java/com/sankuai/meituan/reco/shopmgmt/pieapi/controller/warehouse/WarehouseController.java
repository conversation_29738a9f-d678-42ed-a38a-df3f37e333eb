package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.warehouse;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.pickselect.thrift.wave.response.PickWarehouseTasksResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StoreFulfillConfigWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.WarehousePickOperateWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.WarehouseWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.Valid;


@InterfaceDoc(
        displayName = "调拨拣货服务",
        type = "restful",
        scenarios = "调拨拣货服务",
        description = "调拨拣货服务",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "调拨拣货服务")
@RestController
@RequestMapping("/pieapi/warehouse/pick")
public class WarehouseController {

    @Autowired
    private WarehouseWrapper warehouseWrapper;

    @Autowired
    private WarehousePickOperateWrapper warehousePickOperateWrapper;
    @Autowired
    private TenantWrapper tenantWrapper;
    @Resource
    private StoreFulfillConfigWrapper storeFulfillConfigWrapper;

    @MethodDoc(
            displayName = "查询待集单数据",
            description = "查询待集单数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待集单数据",
                            type = WarehousePickCollectionQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/collection/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待集单数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/collection/query", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickCollectionQueryResponse> queryWarehouseCollection(@Valid @RequestBody WarehousePickCollectionQueryRequest request) {
        request.validate();
        return warehouseWrapper.queryWarehouseCollection(request);
    }

    @MethodDoc(
            displayName = "查询出库单商品数量和类型",
            description = "查询出库单商品数量和类型",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询出库单商品数量和类型",
                            type = WarehousePickCollectionQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/count/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询出库单商品数量和类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/count/query", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickQueryCountResponse> queryWarehouseCount(@Valid @RequestBody WarehousePickQueryCountRequest request) {
        return warehouseWrapper.queryWarehouseCount(request);
    }

    @MethodDoc(
            displayName = "查询tab数据数量",
            description = "查询tab数据数量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询tab数据数量",
                            type = WarehousePickTabNumRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/tab/num",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询tab数据数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/tab/num", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickTabNumResponse> queryWarehousePickTabNum(@Valid @RequestBody WarehousePickTabNumRequest request) {
        return warehouseWrapper.queryWarehousePickTabNum(request);
    }

    @MethodDoc(
            displayName = "集单操作",
            description = "集单操作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "集单操作",
                            type = WarehousePickCollectionOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/collection/operate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "集单操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/collection/operate", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> operateWarehouseCollection(@Valid @RequestBody WarehousePickCollectionOperateRequest request) {
        boolean isAllotOutOrder = request.getOrderType() == null || request.getOrderType() == 1;
        if (isAllotOutOrder && MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = warehousePickOperateWrapper.getOfflineStoreIdFromContext(identityInfo);

        if (MccConfigUtil.getJudgeTrusteeshipSwitch() && tenantWrapper.isTrusteeshipRepoByRepoId(offlineStoreId)){
            return CommonResponse.fail(1,"托管仓不允许集单!");
        }

        try {
            warehousePickOperateWrapper.operateWarehouseCollection(request);
        }catch (CommonRuntimeException e){
            log.error("operateWarehouseCollection error request:{}",request,e);
            return CommonResponse.fail(1,e.getMessage());
        } catch (Exception e) {
            log.error("operateWarehouseCollection error request:{}",request,e);
            return CommonResponse.fail(1,"集单失败");
        }
        return CommonResponse.success(null);
    }

    @MethodDoc(
            displayName = "查询待领取",
            description = "查询待领取",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待领取",
                            type = WarehousePickWaitReceiveRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/waitreceive",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待领取")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/waitreceive", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickWaitReceiveResponse> queryWarehouseWaitReceive(@Valid @RequestBody WarehousePickWaitReceiveRequest request) {
        return warehouseWrapper.queryWarehouseWaitReceive(request);

    }

    @MethodDoc(
            displayName = "领取操作",
            description = "领取操作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "领取操作",
                            type = WarehousePickReceiveRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/receive",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "集单操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/receive", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> operateWarehousePickReceive(@Valid @RequestBody WarehousePickReceiveRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        try {
            warehousePickOperateWrapper.operateWarehousePickReceive(request);
        }catch (CommonRuntimeException e){
            log.error("operateWarehousePickReceive error request:{}",request,e);
            return CommonResponse.fail(1,e.getMessage());
        } catch (TException e) {
            log.error("operateWarehousePickReceive error request:{}",request,e);
            return CommonResponse.fail(1,"领取失败");
        }
        return CommonResponse.success(null);
    }


    @MethodDoc(
            displayName = "查询待拣货",
            description = "查询待拣货",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待拣货",
                            type = WarehouseWaitPickQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/waitpick",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待拣货")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/waitpick", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehouseWaitPickQueryResponse> queryWarehouseWaitPick(@Valid @RequestBody WarehouseWaitPickQueryRequest request) {
        return warehouseWrapper.queryWarehouseWaitPick(request);
    }

    @MethodDoc(
            displayName = "查询拣货/分拣任务信息",
            description = "查询拣货/分拣任务信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询拣货/分拣任务信息",
                            type = WarehouseWaveTaskDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/wavetaskdetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询拣货/分拣任务信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/wavetaskdetail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehouseWaveTaskDetailResponse> queryWarehouseWaveTaskDetail(@Valid @RequestBody WarehouseWaveTaskDetailRequest request) {
        return warehouseWrapper.queryWarehouseWaveTaskDetail(request);
    }

    @MethodDoc(
            displayName = "查询拣货详情里的商品信息",
            description = "查询拣货详情里的商品信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询拣货详情里的商品信息",
                            type = WarehousePickSkuDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/sku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询拣货详情里的商品信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sku", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickSkuDetailResponse> queryWarehousePickSkuDetail(@Valid @RequestBody WarehousePickSkuDetailRequest request) {
        return warehouseWrapper.queryWarehousePickSkuDetail(request);
    }

    @MethodDoc(
            displayName = "更新容器信息",
            description = "更新容器信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "更新容器信息",
                            type = WarehouseContainerBoxUpdateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/box/update",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "更新容器信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/box/update", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<String> updateWarehouseContainerBox(@Valid @RequestBody WarehouseContainerBoxUpdateRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        return warehouseWrapper.updateWarehouseContainerBox(request);
    }

    @MethodDoc(
            displayName = "拣货完成操作",
            description = "拣货完成操作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "拣货完成操作",
                            type = WarehousePickCompleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/complete/operate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "拣货完成操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/complete/operate", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickCompleteResponse> operateWarehousePickComplete(@Valid @RequestBody WarehousePickCompleteRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        WarehousePickCompleteResponse response=new WarehousePickCompleteResponse();
        try {
            PickWarehouseTasksResponse tasksResponse = warehousePickOperateWrapper.operateWarehousePickComplete(request);
            response.setFinish(tasksResponse.isFinish());
            response.setNeedSeed(tasksResponse.isNeedSeed());
        } catch (CommonRuntimeException e){
            log.error("operateWarehousePickComplete error request:{}",request,e);
            return CommonResponse.fail(1,e.getMessage());
        }catch (TException e) {
            log.error("operateWarehousePickComplete error request:{}",request,e);
            return CommonResponse.fail(1,"拣货失败");
        }
        return CommonResponse.success(response);
    }

    @MethodDoc(
            displayName = "修改拣货数量操作",
            description = "修改拣货数量操作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "修改拣货数量操作",
                            type = WarehousePickCompleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/complete/update",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "修改拣货数量操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/complete/update", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> updateWarehousePickCompleteNum(@Valid @RequestBody WarehousePickCompleteRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        try {
            warehousePickOperateWrapper.updateWarehousePickCompleteNum(request);
        }catch (CommonRuntimeException e){
            log.error("updateWarehousePickCompleteNum error request:{}",request,e);
            return CommonResponse.fail(1,e.getMessage());
        } catch (TException e) {
            log.error("updateWarehousePickCompleteNum error request:{}",request,e);
            return CommonResponse.fail(1,"修改拣货失败");
        }
        return CommonResponse.success(null);
    }

    @MethodDoc(
            displayName = "查询已拣货列表",
            description = "查询已拣货列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询已拣货列表",
                            type = WarehousePickCompleteQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/complete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询已拣货列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/complete", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickCompleteQueryResponse> queryWarehousePickComplete(@Valid @RequestBody WarehousePickCompleteQueryRequest request) {
        return warehouseWrapper.queryWarehousePickComplete(request);
    }

    @MethodDoc(
            displayName = "查询待分拣列表",
            description = "查询待分拣列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待分拣列表",
                            type = WarehouseSeedWaitQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/seed/wait",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待分拣列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/seed/wait", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehouseSeedWaitQueryResponse> queryWarehouseWaitSeedList(@Valid @RequestBody WarehouseSeedWaitQueryRequest request) {
        return warehouseWrapper.queryWarehouseWaitSeedList(request);
    }

    @MethodDoc(
            displayName = "查询分拣详情里的商品信息",
            description = "查询分拣详情里的商品信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询分拣详情里的商品信息",
                            type = WarehouseSeedSkuDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/seed/sku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询分拣详情里的商品信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/seed/sku", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehouseSeedSkuDetailResponse> queryWarehouseSeedSkuDetail(@Valid @RequestBody WarehouseSeedSkuDetailRequest request) {
        return warehouseWrapper.queryWarehouseSeedSkuDetail(request);
    }

    @MethodDoc(
            displayName = "模糊查询分拣任务详情里商品信息",
            description = "模糊查询分拣任务详情里商品信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "模糊查询分拣任务详情里商品信息",
                            type = WarehouseSeedFuzzySkuRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/seed/fuzzy",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "模糊查询分拣任务详情里商品信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/seed/fuzzy", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehouseSeedFuzzySkuResponse> queryWarehouseSeedFuzzySku(@Valid @RequestBody WarehouseSeedFuzzySkuRequest request) {
        return warehouseWrapper.queryWarehouseSeedFuzzySku(request);
    }

    @MethodDoc(
            displayName = "模糊查询分拣任务详情里商品信息（装箱分拣-已分拣）",
            description = "模糊查询分拣任务详情里商品信息（装箱分拣-已分拣）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "模糊查询分拣任务详情里商品信息（装箱分拣-已分拣tab）",
                            type = WarehouseSeedFuzzySkuRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/seed/fuzzy",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "模糊查询分拣任务详情里商品信息（装箱分拣-已分拣）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/seed/fuzzy", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickPackSeedCompleteDetailResponse> queryWarehouseSeedFuzzySkuForPackComplete(@Valid @RequestBody WarehouseSeedFuzzySkuRequest request) {
        return warehouseWrapper.queryWarehouseSeedFuzzySkuForPackComplete(request);
    }



    @MethodDoc(
            displayName = "模糊查询拣货任务详情里商品信息",
            description = "模糊查询拣货任务详情里商品信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "模糊查询拣货任务详情里商品信息",
                            type = WarehousePickDetailFuzzyRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/detail/fuzzy",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "模糊查询拣货任务详情里商品信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detail/fuzzy", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickDetailFuzzyResponse> queryWarehousePickDetailFuzzy(@Valid @RequestBody WarehousePickDetailFuzzyRequest request) {
        return warehouseWrapper.queryWarehousePickDetailFuzzy(request);
    }

    @MethodDoc(
            displayName = "分拣操作",
            description = "分拣操作",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分拣操作",
                            type = WarehouseSortingPickOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/sortingpick/operate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "分拣操作")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sortingpick/operate", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> operateWarehouseSortingPick(@Valid @RequestBody WarehouseSortingPickOperateRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        return warehousePickOperateWrapper.operateWarehouseSortingPick(request);
    }

    @MethodDoc(
            displayName = "查询已分拣列表",
            description = "查询已分拣列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询已分拣列表",
                            type = WarehouseSeedCompleteQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/seed/complete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询已分拣列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/seed/complete", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehouseSeedCompleteQueryResponse> queryWarehouseSeedComplete(@Valid @RequestBody WarehouseSeedCompleteQueryRequest request) {
        return warehouseWrapper.queryWarehouseSeedComplete(request);
    }

    @MethodDoc(
            displayName = "查询待集单门店列表",
            description = "查询待集单门店列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待集单门店列表",
                            type = Valid.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/poi/info",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待集单门店列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/poi/info", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickPoiInfoResponse> queryWarehousePoiInfo() {
        try {
            return CommonResponse.success(warehouseWrapper.getPoiInfo());
        } catch (TException e) {
            log.error("queryWarehousePoiInfo error",e);
        }
        return CommonResponse.fail(1,"获取列表失败");
    }

    @MethodDoc(
            displayName = "查询待集单收货方门店列表（支持跨租户）",
            description = "查询待集单收货方门店列表（支持跨租户）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待集单收货方门店列表（支持跨租户）",
                            type = Valid.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/poi/info-for-sale",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待集单收货方门店列表（支持跨租户）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/poi/info-for-sale", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickPoiInfoResponse> queryWarehousePoiInfoCrossTenant(@RequestParam String orderTag,
                                                                                         @Nullable @RequestParam(required = false) Integer outboundOrderStatus) {
        try {
            return CommonResponse.success(warehouseWrapper.getPoiInfoCrossTenant(orderTag, outboundOrderStatus));
        } catch (TException e) {
            log.error("queryWarehousePoiInfoCrossTenant error",e);
        }
        return CommonResponse.fail(1,"获取列表失败");
    }

    @MethodDoc(
            displayName = "Hu解绑定",
            description = "Hu解绑定",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "Hu解绑定",
                            type = WarehousePickHuCodeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/update",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "Hu解绑定")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/update", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<String> updateWarehousePickHuCode(@Valid @RequestBody WarehousePickHuCodeRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        return warehouseWrapper.updateWarehousePickHuCode(request);
    }

    @MethodDoc(
            displayName = "Hu换绑",
            description = "Hu换绑",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "Hu换绑",
                            type = WarehousePickHuCodeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/replace",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "Hu换绑")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/replace", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<String> replaceWarehouseSeedHuCode(@Valid @RequestBody WarehouseSeedHuReplaceRequest request) {
//        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
//            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
//        }

        return warehouseWrapper.replaceWarehouseSeedHuCode(request);
    }

    @MethodDoc(
            displayName = "装箱完成",
            description = "装箱完成",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "装箱完成",
                            type = WarehousePickPackCompleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/complete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "装箱完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/complete", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<String> updateWarehousePickPackComplete(@Valid @RequestBody WarehousePickPackCompleteRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        return warehouseWrapper.updateWarehousePickPackComplete(request);
    }

    @MethodDoc(
            displayName = "取消装箱",
            description = "取消装箱",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "取消装箱",
                            type = WarehousePickPackCancelRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/cancel",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "取消装箱")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/cancel", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<String> updateWarehousePickPackCancel(@Valid @RequestBody WarehousePickPackCancelRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        return warehouseWrapper.updateWarehousePickPackCancel(request);
    }

    @MethodDoc(
            displayName = "装箱满箱",
            description = "装箱满箱",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "装箱满箱",
                            type = WarehousePickPackFullRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/full",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "装箱满箱")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/full", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<String> updateWarehousePickPackFull(@Valid @RequestBody WarehousePickPackFullRequest request) {
        if (MccConfigUtil.needBlockAllotOutOperation(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
            return CommonResponse.fail(1, "调拨出库单迁移中，请稍后再试");
        }

        return warehouseWrapper.updateWarehousePickPackFull(request);
    }

    @MethodDoc(
            displayName = "查询待分拣详情",
            description = "查询待分拣详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待分拣详情",
                            type = WarehousePickPackSeedWaitDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/seedWait/detail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询待分拣详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/seedWait/detail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickPackSeedWaitDetailResponse> queryWarehousePickPackSeedWaitDetail(@Valid @RequestBody WarehousePickPackSeedWaitDetailRequest request) {
        return warehouseWrapper.queryWarehousePickPackSeedWaitDetail(request);
    }

    @MethodDoc(
            displayName = "查询已分拣详情",
            description = "查询已分拣详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询已分拣详情",
                            type = WarehousePickPackSeedCompleteDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/pack/seedComplete/detail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询已分拣详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pack/seedComplete/detail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickPackSeedCompleteDetailResponse> queryWarehousePickPackSeedCompleteDetail(@Valid @RequestBody WarehousePickPackSeedCompleteDetailRequest request) {
        return warehouseWrapper.queryWarehousePickPackSeedCompleteDetail(request);
    }

    @MethodDoc(
            displayName = "查询已拣货列表V2",
            description = "查询已拣货列表V2",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询已拣货列表V2",
                            type = WarehousePickCompleteQueryRequestV2.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/warehouse/pick/complete/v2",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询已拣货列表V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/complete/v2", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<WarehousePickCompleteQueryResponseV2> queryWarehousePickCompleteV2(@Valid @RequestBody WarehousePickCompleteQueryRequestV2 request) {
        return warehouseWrapper.queryWarehousePickCompleteV2(request);
    }
}
