package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 7:56 下午
 * Description
 */
@Data
@ToString
@EqualsAndHashCode
@TypeDoc(description = "返回的数据为城市-门店列表, 门店列表为所在城市下的门店")
public class CityWithPoiListVO {

    /**
     * 城市编码
     */
    @FieldDoc(description = "城市编码")
    private String cityCode;

    /**
     * 城市名称
     */
    @FieldDoc(description = "城市名称")
    private String cityName;
    /**
     * 门店列表
     */
    @FieldDoc(description = "城市下的门店列表")
    private List<PoiInfoVO> poiList;
}
