package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:18
 * @Description:配送类型操作类型
 */
public enum DeliveryExceptionOperateType {

    //1取消2转自配送3已送出4联系骑手
    CANCEL(1, "取消"),
    SELF_DELIVERY(2, "转自配送"),
    CONTACT_RIDER(4, "联系骑手"),
    RETRY_CREATE_DELIVERY(6, "重发配送");

    public final Integer type;
    public final String desc;

    DeliveryExceptionOperateType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
