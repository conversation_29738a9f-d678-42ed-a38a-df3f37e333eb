package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SimpleUser;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.PriceIndexStoreCategoryQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.SkuCanModifyPriceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.SpuCanModifyPriceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChangeChannelSkuFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuStockInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuStockInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.AdjustSkuRequestInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.BatchOperateSpuResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.CreateStockAdjustRequestVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.CreateStockAdjustResponseVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuCanModifyPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SpuCanModifyPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QueryProductByUpcResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion.StoreSpuActivityAndForbidFieldsQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.StockAdjustTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.SupplyRelationService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SsrfCheckUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.RequestConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.*;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.SkuStockInfo;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.StockInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuBasicDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuBasicDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.SpuActivityInfoQueryTypeEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.FranchiseeControlProductThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QueryMerchantSkuListBySkuIds;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasure;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuPvExtend;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.MerchantSkuInfoListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMasterCheckService;
import com.sankuai.meituan.shangou.saas.common.data.BaseResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ErrorCodeEnum.SPU_PUSH_CHANNEL_FAILED;

/**
 * @Title: StoreSpuController
 * @Description: OCMS 商品SPU相关接口
 * @Author: zhaolei12
 * @Date: 2020/4/17 12:02 下午
 */
@InterfaceDoc(
        displayName = "OCMS 商品SPU相关接口",
        type = "restful",
        scenarios = "OCMS 商品SPU相关接口",
        description = "OCMS 商品SPU相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "OCMS 商品SPU相关接口")
@RestController
@RequestMapping("/pieapi/ocms/store/spu")
public class StoreSpuController {

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    @Resource
    private OCMSProductServiceWrapper ocmsProductServiceWrapper;

    @Resource
    private OcmsTaggedSpuCoverageWrapper ocmsTaggedSpuCoverageWrapper;

    @Resource
    private StoreProductWrapper storeProductWrapper;

    @Resource
    private TenantProductWrapper tenantProductWrapper;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private StockWrapper stockWrapper;

    @Resource
    private ProductBizServiceWrapper productBizServiceWrapper;

    @Resource
    private PurchaseBizServiceWrapper purchaseBizServiceWrapper;

    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;
    @Resource
    private ChannelActivityWrapper channelActivityWrapper;
    @Resource
    private EmpowerMasterCheckService.Iface empowerMasterCheckService;

    @Resource
    private FranchiseeControlProductThriftService franchiseeControlProductThriftService;

    @Autowired
    private SupplyRelationService supplyRelationService;

    @MethodDoc(
            displayName = "门店商品分页查询接口",
            description = "查询商品信息列表，分页查询商品信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品分页查询请求参数",
                            type = StoreSpuPageQueryApiRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/ocms/store/spu/pageQuery",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pageQuery", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSpuPageQueryResponseVO> pageQuery(@Valid @RequestBody StoreSpuPageQueryApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<String> boothIdList = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, String::valueOf);
        String boothId = null;
        if (user.getAccountType() == AccountTypeEnum.BOOTH.getValue() && !CollectionUtils.isEmpty(boothIdList)) {
            boothId = boothIdList.get(0);
        }
        return ocmsProductServiceWrapper.pageQueryStoreSpu(request, user, boothId);
    }

    @MethodDoc(
            displayName = "获取门店商品异常详情",
            description = "获取门店商品异常详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取门店商品异常详情请求参数",
                            type = QueryStoreSpuAbnormalRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/ocms/store/spu/getSpuAbnormalDetail",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getSpuAbnormalDetail", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProductAbnormalVO> getSpuAbnormalDetail(@RequestBody QueryStoreSpuAbnormalRequest request) {
        return ocmsProductServiceWrapper.getSpuAbnormalDetail(request);
    }

    @MethodDoc(
            description = "获取门店商品详情",
            displayName = "获取门店商品详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品详情请求参数",
                            type = StoreSpuDetailApiRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/detail",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSpuVO> detail(@Valid @RequestBody StoreSpuDetailApiRequest request) {
        // APP门店商品编辑增补类目，必须返回类目信息，供用户修改类目，将channelCategoryExtend置为true
        request.setChannelCategoryExtend(true);
        return ocmsProductServiceWrapper.detailStoreSpu(request);
    }


    @MethodDoc(
            displayName = "松鼠-门店商品字段配置",
            description = "查询门店商品的字段属性配置",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询门店商品的字段属性配置"
                    ),
            },
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            responseParams = {
                    @ParamDoc(name = "msg", description = "请求成功", type = String.class),
                    @ParamDoc(name = "code", description = "接口返回code,0为成功", type = int.class),
                    @ParamDoc(name = "data", description = "查询门店商品的字段属性配置", type = StoreProductFieldsVO.class),
            }
    )
    @ApiOperation(value = "松鼠-门店受控商品受控字段")
    @com.sankuai.meituan.shangou.saas.common.method.MethodLog(logResponse = true, logger = "http")
    @RequestMapping(value = "/fieldSetting", method = RequestMethod.POST)
    public CommonResponse<StoreProductFieldsVO> controlFields() {
        try {
            // 查询所有受控字段
            List<String> controlFields = franchiseeControlProductThriftService.queryControlFields();
            StoreProductFieldsVO storeProductFieldsVO = buildStoreProductFieldsVO(controlFields);
            return CommonResponse.success(storeProductFieldsVO);
        } catch (Exception e) {
            log.error("查询受控字段异常", e);
            return new  CommonResponse(ResultCode.FAIL.getCode(), "查询受控字段异常", null);
        }
    }

    private StoreProductFieldsVO buildStoreProductFieldsVO(List<String> controlFields) {
        StoreProductFieldsVO storeProductFieldsVO = new StoreProductFieldsVO();
        storeProductFieldsVO.setControlFieldsList(controlFields);
        return storeProductFieldsVO;
    }


    @MethodDoc(
            displayName = "新增/编辑门店商品",
            description = "新增/编辑门店商品",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "新增/编辑门店商品请求参数",
                            type = SaveStoreSpuApiRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/ocms/store/spu/saveOrUpdate",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SaveStoreSpuPartSuccessResponseVO> saveStoreOnlineSku(@Valid @RequestBody SaveStoreSpuApiRequest request) {
        @NotNull StoreSpuCreateVO storeSpu = request.getStoreSpu();
        if (CollectionUtils.isNotEmpty(storeSpu.getCustomizedImageUrlList())) {
            storeSpu.getCustomizedImageUrlList()
                    .forEach(SsrfCheckUtils::checkUrl);
        }
        if (CollectionUtils.isNotEmpty(storeSpu.getPictureContents())) {
            storeSpu.getPictureContents()
                    .forEach(SsrfCheckUtils::checkUrl);
        }
        if (Objects.nonNull(storeSpu.getVideo())) {
            SsrfCheckUtils.checkUrl(storeSpu.getVideo().getVideoUrl());
            SsrfCheckUtils.checkUrl(storeSpu.getVideo().getCoverImageUrl());
        }
        CommonResponse<SaveStoreSpuPartSuccessResponseVO> response = ocmsProductServiceWrapper.saveStoreOnlineSku(request);

        StringBuilder errorMessageBuilder = new StringBuilder();
        //创建供货关系(11004忽略渠道推送失败的情况)
        List<SupplyRelationAndPurchaseVO> purchaseInfo = request.getStoreSpu().getPurchaseInfo();
        if (response != null && (response.isSuccess() || response.getCode() == SPU_PUSH_CHANNEL_FAILED.getCode()) && CollectionUtils.isNotEmpty(purchaseInfo)) {
            CommonResponse<Void> commonResponse = supplyRelationService.createSpuSupplyRelation(request.getTenantId(), request.getStoreId(),
                    request.getStoreSpu().getSpuId(), purchaseInfo,false, SimpleUser.build(ApiMethodParamThreadLocal.getIdentityInfo().getUser()));
            if (!commonResponse.isSuccess()) {
                errorMessageBuilder.append(commonResponse.getMessage()).append(";");
            }
        }

        //同步库存
        if (response != null && response.isSuccess()
                && request.isUseNewAdjustStock() && MccConfigUtil.isSaveSkuStockFromEApi(request.getTenantId())
                && request.getStockAdjustRequestVo() != null) {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            User user = identityInfo.getUser();
            CommonResponse<Void> commonResponse = saveSkuStock(user, storeSpu.getSpuId(), request.getStockAdjustRequestVo(), false);

            if (!commonResponse.isSuccess()) {
                errorMessageBuilder.append(commonResponse.getMessage()).append(";");
            }
        }

        if (response != null && errorMessageBuilder.length() > 0) {
            response.setMessage("商品编辑成功," + errorMessageBuilder);
        }


        return response;
    }

    private CommonResponse<Void> saveSkuStock(User user, String spuId, CreateStockAdjustRequestVo stockAdjustRequestVo, Boolean manualCreate) {
        try {
            //erp不做调整
            if (ocmsServiceWrapper.isErpTenant(user.getTenantId())) {
                return CommonResponse.success(null);
            }

            if (CollectionUtils.isEmpty(stockAdjustRequestVo.getSkuList())) {
                return CommonResponse.success(null);
            }

            Asserts.notBlank(spuId, "spuId");
            //如果是手动创建，需要匹配skuId信息
            if (Boolean.TRUE.equals(manualCreate)) {
                fillSkuIdWhenCreateByManual(stockAdjustRequestVo.getSkuList(), user.getTenantId(), stockAdjustRequestVo.getEntityId(), spuId);
            }

            CreateStockAdjustResponseVo responseVo = stockWrapper.saveSkuStock(stockAdjustRequestVo);
            if (responseVo.getCode() != 0){
                return new CommonResponse<>(BaseResult.FAIL.getCode(), responseVo.getMsg(), null);
            }
            return CommonResponse.success(null);
        } catch (Exception e) {
            log.error("saveSkuStock#createStockAdjust error, req:{}", stockAdjustRequestVo, e);
            return new CommonResponse<>(BaseResult.FAIL.getCode(), "保存库存失败", null);
        }
    }

    private void fillSkuIdWhenCreateByManual(List<AdjustSkuRequestInfo> adjustSkuRequestInfos, Long tenantId, Long storeId, String spuId) {
        //商品查询需要走主库
        StoreSpuBasicDTO storeSpuBasicDTO = productBizServiceWrapper.queryStoreSpuBasicInfo(tenantId, storeId, spuId, true);
        if (storeSpuBasicDTO == null ||CollectionUtils.isEmpty(storeSpuBasicDTO.getStoreSkuList())) {
            throw new BizException("门店商品不存在");
        }

        Map<String, String> skuBasicDTOMap = Fun.toMapQuietly(storeSpuBasicDTO.getStoreSkuList(),
                k -> skuMatchKey(k.getSpec()),
                StoreSkuBasicDTO::getSkuId);

        //匹配库存skuId
        for (AdjustSkuRequestInfo adjustSkuRequestInfo :adjustSkuRequestInfos) {
            String skuId = skuBasicDTOMap.get(skuMatchKey(adjustSkuRequestInfo.getSpecName()));
            if ( skuId != null) {
                adjustSkuRequestInfo.setSkuId(skuId);
            } else {
                log.error("没有匹配到对应skuId, sku:{}",  adjustSkuRequestInfo);
            }
        }
    }

    private String skuMatchKey(String spec) {
        return spec != null ? spec : "";
    }


    @MethodDoc(
            displayName = "门店商品更新规格",
            description = "门店商品更新规格",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品更新规格请求参数",
                            type = StoreSpuSpecListUpdateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/ocms/store/spu/updateSpecList",
            returnValueDescription = "门店商品更新规格返回",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/updateSpecList")
    @ResponseBody
    public CommonResponse<SaveStoreSpuPartSuccessResponseVO> updateStoreSpuSpecList(@Valid @RequestBody StoreSpuSpecListUpdateRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsProductServiceWrapper.updateStoreSpuSpecList(request, user);
    }

    @MethodDoc(
            description = "批量上下架",
            displayName = "批量上下架",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品批量上下架请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/batchChangeStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchChangeStatus", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<BatchChangeSpuStatusResponseVO> batchChangeStatus(@Valid @RequestBody BatchChangeSpuStatusRequest
                                                                                    request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if (MccConfigUtil.isQnhRequest(user.getTenantId(), request.getStoreId())) {
            return ocmsServiceWrapper.batchChangeQnhStatus(request, user);
        } else {
            // 总部管品模式下，前端不再传渠道列表
            if (tenantWrapper.isMerchantChargeSpu(user.getTenantId())
                    || tenantWrapper.isStoreManagementTenant(user.getTenantId())
                    || tenantWrapper.isNotMerchantChargeGray(user.getTenantId())) {
                List<ChannelTypeEnumBo> channelTypeEnums = tenantWrapper.queryTenantStoreChannelIds(user.getTenantId(), request
                        .getStoreId());
                List<Integer> channelIdList = channelTypeEnums.stream().map(ChannelTypeEnumBo::getChannelId).collect(Collectors
                        .toList());
                request.setChannelIds(channelIdList);
            }
            return ocmsServiceWrapper.batchChangeStatus(request, user);
        }
    }

    @MethodDoc(
            description = "批量修改线上商品前台分类",
            displayName = "批量修改线上商品前台分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "修改线上商品前台分类",
                            type = ChangeChannelSkuFrontCategoryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/batchChangeFrontCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchChangeFrontCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<BatchChangeSpuFrontCategoryResponseVO> batchChangeFrontCategory(@Valid @RequestBody BatchChangeSpuFrontCategoryRequest request) {
        return ocmsServiceWrapper.batchChangeFrontCategory(request);
    }

    //start
    @MethodDoc(
            description = "设置库存",
            displayName = "设置库存",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "设置库存",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/updateStock",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updateStock", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse updateStock(@Valid @RequestBody UpdateStockRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setAdjustType(StockAdjustTypeEnum.VALID_QUANTITY.getValue());
        return ocmsServiceWrapper.updateStock(request, user);
    }

    @MethodDoc(
            description = "查询已上架已下架商品数",
            displayName = "查询已上架已下架商品数",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询已上架已下架商品数请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/queryOnSaleAndOffSaleCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryOnSaleAndOffSaleCount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryOnSaleAndOffSaleCountResponseVO> queryOnSaleAndOffSaleCount(@Valid @RequestBody QueryOnSaleAndOffSaleCountRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<String> boothIdList = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, String::valueOf);
        String boothId = null;
        if (user.getAccountType() == AccountTypeEnum.BOOTH.getValue() && !CollectionUtils.isEmpty(boothIdList)) {
            boothId = boothIdList.get(0);
        }
        return ocmsServiceWrapper.queryOnSaleAndOffSaleCount(request, user, boothId);
    }

    @MethodDoc(
            description = "按天查未报价/未上架商品",
            displayName = "按天查未报价/未上架商品",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "按天查未报价/未上架商品",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/pageQueryUnquoted",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pageQueryOffSaleAndUnquotedByDay", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSpuPageQueryResponseVO> pageQueryOffSaleAndUnquotedByDay(@Valid @RequestBody PageQueryOffSaleAndUnquotedRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.pageQueryUnquoted(request, user);
    }


    @MethodDoc(
            description = "查询标签商品的覆盖率",
            displayName = "查询标签商品的覆盖率",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询标签商品的覆盖率请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": \"85%\"\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/getTaggedSpuCoverage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @RequestMapping(value = "/getTaggedSpuCoverage", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<String> getTaggedSpuCoverage(@Valid @RequestBody PageTaggedSpuByQueryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsTaggedSpuCoverageWrapper.getTaggedSpuCoverage(request, user);
    }

    @MethodDoc(
            description = "分页查询某tag在门店没有上架的商品",
            displayName = "分页查询某tag在门店没有上架的商品",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询标签商品的覆盖率请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/pageQueryStoreSpuByTag",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pageQueryStoreSpuByTag", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSpuPageQueryResponseVO> pageQueryStoreSpuByTag(@Valid @RequestBody PageTaggedSpuByQueryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsTaggedSpuCoverageWrapper.pageQueryStoreSpuByTag(request, user);
    }

    @MethodDoc(
            description = "商品图片白底检测，基于外卖图片检测服务，检测图片是否为白底，根据得分判断",
            displayName = "商品图片白底检测",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品图片白底检测请求参数",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": {\n" +
                    "    \"audit\": true\n" +
                    "  }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/picAudit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/picAudit", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<PicAuditResultVO> auditPic(@Valid @RequestBody PicAuditApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsProductServiceWrapper.auditPic(request, user);
    }

    @MethodDoc(
            description = "非白底图片转白底图片",
            displayName = "非白底图片转白底图片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "非白底图片转白底图片请求参数",
                            paramType = ParamType.REQUEST_PARAM,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": {\n" +
                    "    \"imgUrl\": \"\"\n" +
                    "  }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/genWhiteBackgroundImage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/genWhiteBackgroundImage", method = RequestMethod.POST)
    public CommonResponse<WhiteBackgroundImageVO> genWhiteBackgroundImage(
            @Valid @RequestBody WhiteBackgroundImageGenRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsProductServiceWrapper.genWhiteBackgroundImage(request, user);
    }

    @MethodDoc(
            description = "商品增加易差评标签",
            displayName = "商品增加易差评标签",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品增加易差评标签请求参数",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": {\n" +
                    "    \"audit\": true\n" +
                    "  }\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/addBadCaseTag",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/addBadCaseTag", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse addBadCaseTag(@RequestBody AddBadCaseTagRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsProductServiceWrapper.addBadCaseTag(user.getTenantId(), ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                request.getSpuIds(), user.getAccountId(), user.getAccountName());
    }

    @MethodDoc(
            description = "设置或取消门店商品力荐",
            displayName = "设置或取消门店商品力荐",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "更新门店商品力荐请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\"\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/updateSpecialty",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updateSpecialty", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse updateSpecialty(@RequestBody StoreSpuSpecialtyUpdateApiRequest request) {

        // 校验参数
        request.validate();

        // 更新门店商品力荐
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsProductServiceWrapper.updateStoreSpuSpecialty(request, user);
    }

    @MethodDoc(
            description = "upc码查询租户商品、门店商品、标品库商品",
            displayName = "upc码查询租户商品、门店商品、标品库商品",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品查询请求参数",
                            type = QueryProductByUpcRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/queryByUpc",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryByUpc", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryProductByUpcResponse> queryByUpc(@Valid @RequestBody QueryProductByUpcRequest request) {
        return tenantProductWrapper.queryByUpc(request);
    }

    @MethodDoc(
            displayName = "新增门店商品(新）",
            description = "新增门店商品(新）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "新增门店商品(新）请求参数",
                            type = CreateStoreSpuApiRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/ocms/store/spu/createProduct",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/createProduct", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<FastCreateSpuVO> newCreateProduct(@Valid @RequestBody CreateStoreSpuApiRequest request) {
        CommonResponse<FastCreateSpuVO> response = storeProductWrapper.fastCreateStoreSpu(request);

        StringBuilder errorMessageBuilder = new StringBuilder();

        //创建供货关系(11004忽略渠道推送失败的情况)
        List<SupplyRelationAndPurchaseVO> purchaseInfo = request.getStoreSpu().getPurchaseInfo();
        if (response != null && (response.isSuccess() || response.getCode() == SPU_PUSH_CHANNEL_FAILED.getCode()) && CollectionUtils.isNotEmpty(purchaseInfo)) {
            CommonResponse<Void> commonResponse = supplyRelationService.createSpuSupplyRelation(request.getTenantId(), request.getStoreId(), response.getData().getSpuId(),
                    purchaseInfo, request.getManualCreate(), SimpleUser.build(ApiMethodParamThreadLocal.getIdentityInfo().getUser()));
            if (!commonResponse.isSuccess()) {
                errorMessageBuilder.append(commonResponse.getMessage()).append(";");
            }
        }

        //同步库存
        if (response != null && response.isSuccess()
                && request.isUseNewAdjustStock() && MccConfigUtil.isSaveSkuStockFromEApi(request.getTenantId())
                && request.getStockAdjustRequestVo() != null) {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            User user = identityInfo.getUser();
            CommonResponse<Void> commonResponse = saveSkuStock(user, response.getData().getSpuId(), request.getStockAdjustRequestVo(), true);

            if (!commonResponse.isSuccess()) {
                errorMessageBuilder.append(commonResponse.getMessage()).append(";");
            }
        }
        if (response != null && errorMessageBuilder.length() > 0) {
            response.setMessage("商品创建成功," + errorMessageBuilder);
        }

        return response;
    }

    @MethodDoc(
            displayName = "不一致商品列表",
            description = "不一致商品列表",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = ProblemSpuQueryRequest.class)
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryProblemSpuList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProblemSpuQueryResponseVO> queryProblemSpuList(@RequestBody ProblemSpuQueryRequest request) {
        request.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsProductServiceWrapper.queryProblemSpuList(user, request);
    }

    @MethodDoc(
            description = "获取门店商品库存信息",
            displayName = "获取门店商品库存信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品库存信息请求参数",
                            type = StoreSkuStockInfoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/store/spu/skuStockInfo",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "entityId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/skuStockInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSkuStockInfoVO> querySkuStockInfo(@Valid @RequestBody StoreSkuStockInfoRequest request) {
        try {
            // 参数校验
            request.validate();

            StockInfoRequest stockInfoRequest = new StockInfoRequest(request.getTenantId(), request.getEntityId(), request.getEntityType(), request.getSkuIds());
            List<SkuStockInfo> skuStockInfoList = stockWrapper.queryStockInfo(stockInfoRequest);
            StoreSkuStockInfoVO storeSkuStockInfoVO = new StoreSkuStockInfoVO();
            skuStockInfoList.forEach(item -> {
                StoreSkuStockInfoVO.SkuStockInfo skuStockInfo = StoreSkuStockInfoVO.SkuStockInfo.builder().skuId(item.getSkuId())
                        .quantity(item.getQuantity())
                        .validQuantity(item.getValidQuantity())
                        .lockedQuantity(item.getLockedQuantity())
                        .frozenQuantity(item.getFrozenQuantity())
                        .offShelveQuantity(item.getOffShelveQuantity())
                        .build();
                storeSkuStockInfoVO.getSkuStockInfoList().add(skuStockInfo);
            });

            return CommonResponse.success(storeSkuStockInfoVO);
        } catch (ParamException e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getErrMsg());
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "商品库存信息查询异常，请稍后重试");
        }
    }


    @MethodDoc(
            displayName = "根据商品名称查询渠道推荐类目",
            description = "根据商品名称查询渠道推荐类目",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/queryRecommendTag",
            restExamplePostData = "{\"storeId\":123 , \"channelId\": 100, \"name\":\"土豆500g\"}",
            restExampleResponseData = "ChannelCategoryDTO"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRecommendTag", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ChannelCategoryDTO> queryRecommendTag(@RequestBody QuerySpuRecommendTag request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        Long storeId = getStoreId();
        return ocmsProductServiceWrapper.queryRecommendTag(user, request, storeId);
    }

    public Long getStoreId() {
        Long storeId = null;
        try {
            storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        } catch (Exception e) {
            log.error("app端无法获取商品池门店", e);
        }
        return storeId;
    }

    @MethodDoc(
            displayName = "批量删除商品",
            description = "批量删除商品",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/batchDelete",
            restExamplePostData = "",
            restExampleResponseData = ""
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchDelete", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<BatchOperateSpuResultVO> batchDelete(@RequestBody BatchDeleteStoreSpuApiRequest request) {
        return ocmsProductServiceWrapper.batchDelete(request);
    }

    @MethodDoc(
            description = "门店商品是否允许改价",
            displayName = "门店商品是否允许改价",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品是否允许改价请求",
                            type = PriceIndexStoreCategoryQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951, \"spuId\":\"SPU1234\"}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/canModifyPrice",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/canModifyPrice", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<SpuCanModifyPriceVO>> canModifyPrice(@RequestBody SpuCanModifyPriceRequest request) {
        // 校验参数
        request.validate();

        // 查询SPU改价信息
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        try {
            return CommonResponse.success(channelActivityWrapper.canModifyPriceByCustomSpuId(user.getTenantId(), request.getStoreSpuInfos()));
        } catch (BizException e) {
            log.error("活动查询信息异常，request [{}].", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("活动查询信息异常，request [{}].", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "商品促销活动查询异常", null);
        }
    }

    @MethodDoc(
            description = "门店商品是否允许改价",
            displayName = "门店商品是否允许改价",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店商品是否允许改价请求",
                            type = PriceIndexStoreCategoryQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/canModifyPrice/v2",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/canModifyPrice/v2", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<SkuCanModifyPriceVO>> canModifyPriceBySku(@RequestBody SkuCanModifyPriceRequest request) {
        // 校验参数
        request.validate();

        // 查询SKU改价信息
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        try {
            return CommonResponse.success(channelActivityWrapper.canModifyPriceByCustomSkuId(user.getTenantId(), request.getStoreSkuInfos()));
        } catch (BizException e) {
            log.error("活动查询信息异常，request [{}].", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("活动查询信息异常，request [{}].", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "商品促销活动查询异常", null);
        }
    }


    @MethodDoc(
            displayName = "查询渠道推荐动态信息",
            description = "查询渠道推荐动态信息",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/queryRecommendDynamicInfo ",
            restExamplePostData = "{\"spuName\":\"橘子\", \"channelId\": 100, \"channelCategoryId\":\"200002706\", \"storeId\": 409827}",
            restExampleResponseData = "ChannelCategoryDTO"
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRecommendDynamicInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RecommendDynamicInfoVo> queryRecommendDynamicInfo(@RequestBody QuerySpuRecommendDynamicInfo request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = getStoreId();
        request.selfCheck();
        return ocmsProductServiceWrapper.queryRecommendDynamicInfo(tenantId, storeId, request);
    }

    @MethodDoc(
            displayName = "查询渠道推荐动态信息和重量",
            description = "查询渠道推荐动态信息和重量",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/queryRecommendDynamicInfoAndWeight ",
            restExamplePostData = "{\"spuName\":\"橘子\", \"channelId\": 100, \"channelCategoryId\":\"200002706\", \"storeId\": 409827, \"upc\": 123456789123, \"isMerchantSpu\": false}",
            restExampleResponseData = "RecommendDynamicInfoAndWeightVo"
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRecommendDynamicInfoAndWeight", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RecommendDynamicInfoAndWeightVo> queryRecommendDynamicInfoAndWeight(@RequestBody QuerySpuRecommendDynamicInfoAndWeight request) {
        request.selfCheck();
        return ocmsProductServiceWrapper.queryRecommendDynamicInfoAndWeight(request);
    }

    @MethodDoc(
            displayName = "查询门店在美团渠道一级经营品类",
            description = "查询门店在美团渠道一级经营品类",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/queryStoreFirstLevelBusinessCategory ",
            restExamplePostData = "{\"businessCategory\": 2}",
            restExampleResponseData = "ChannelCategoryDTO"
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStoreFirstLevelBusinessCategory", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreBusinessCategoryQueryVO> queryStoreFirstLevelBusinessCategory(@RequestBody StoreBusinessCategoryQueryRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = getStoreId();
        request.selfCheck();
        return ocmsProductServiceWrapper.queryStoreFirstLevelBusinessCategory(tenantId, storeId, request.getChannelId());
    }

    @MethodDoc(
            displayName = "查询门店商品商家端活动信息",
            description = "查询门店商品商家端活动信息",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/ocms/store/spu/queryStoreSpuActivityInfo ",
            restExamplePostData = "{\"businessCategory\": 2}",
            restExampleResponseData = "ChannelCategoryDTO"
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStoreSpuActivityInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSpuActivityAndForbidFieldsQueryVO> queryStoreSpuActivityInfo(@RequestBody StoreSpuActivityQueryRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = getStoreId();
        request.selfCheck();
        // 未命中灰度则不用查活动信息
        if(!MccConfigUtil.isQueryStoreChannelSpuActivity(tenantId)){
            return CommonResponse.success(new StoreSpuActivityAndForbidFieldsQueryVO());
        }
        StoreSpuActivityAndForbidFieldsQueryVO resultVO = ocmsProductServiceWrapper.queryStoreSpuActivityInfo(tenantId, storeId, request.getChannelId(),
                request.getSpuId(), SpuActivityInfoQueryTypeEnum.QUERY_ALL_ACTIVITY_AND_FIELD_ACTIVITY_IMPACT.getCode());
        return CommonResponse.success(resultVO);
    }

    @MethodDoc(
            displayName = "查询商品门店配置",
            description = "查询商品门店配置",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询商品门店配置请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见StoreSpuConfigDetailVO",
            restExampleUrl = "/pieapi/ocms/store/spu/queryStoreConfig",
            restExamplePostData = "",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":\"\"}"
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStoreConfig", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<StoreSpuConfigDetailVO>> queryStoreSpuConfig(@RequestBody QueryStoreSpuConfigRequest request) {
        request.selfCheck();
        Long tenantId = ApiMethodParamThreadLocal.getInstance().get().getUser().getTenantId();
        Long storeId = getStoreId();
        try {
            return CommonResponse.success(storeProductWrapper.queryStoreSpuConfig(tenantId, storeId, request.getConfigPageType()));
        } catch (BizException e) {
            log.error("查询信息门店配置异常，request [{}].", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage(), null);
        } catch (Exception e) {
            log.error("查询门店配置异常，request [{}].", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询门店配置异常", null);
        }
    }

}
