package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryManagementModifyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryManagementQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryManagementUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryStoreManagementQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryStoreManagementQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryManagementConfigResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.ShopAuth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.StoreDeliveryChannelLaunchPointVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.BatchStoreDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.StoreSubDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.ConfigCommonResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/04/08
 */
@InterfaceDoc(
        displayName = "我的配送管理相关接口",
        type = "restful",
        scenarios = "包含配送相关的查询和操作接口",
        description = "包含配送相关的查询和操作接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "配送服务")
@RestController
@RequestMapping("/pieapi/my/deliverymanagement/")
public class MyDeliveryConfigController {

    @Resource
    private TmsServiceWrapper tmsServiceWrapper;

    private static final int DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES = 0;
    private static final int DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES = 60;

    @MethodDoc(
            displayName = "我的配送管理",
            description = "查询可配送渠道+询价",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "预发配送请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/my/deliverymanagement/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取配送管理-我的配送配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ResponseBody
    @PostMapping("/query")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<DeliveryManagementConfigResponse> deliveryManagementQuery(@Valid @RequestBody DeliveryManagementQueryRequest request) {
        DeliveryManagementConfigResponse configResponse = tmsServiceWrapper.deliveryStoreConfigSearch(request.getStoreId());
        return configResponse == null ? CommonResponse.fail(1, "配送配置信息不存在") : CommonResponse.success(configResponse);
    }

    @MethodDoc(
            displayName = "我的配送管理",
            description = "获取配送管理-保存/更新我的配送配置信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "预发配送请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/my/deliverymanagement/update",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取配送管理-保存/更新我的配送配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ResponseBody
    @PostMapping("/update")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<DeliveryManagementConfigResponse> deliveryManagementUpdate(@Valid @RequestBody DeliveryManagementUpdateRequest request) {
        Integer deliveryLaunchPoint = null;
        //check逻辑：所有 "platformCode != 0" 渠道（除了京东之外，必须一致），即过滤掉京东后，只应该有一种launchPoint
        try {
            if (MccConfigUtil.getLaunchPointForceSyncConfig(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
                if (Objects.equals(request.getAggDeliveryPlatformInfo().getStatus(), 1)) {
                    List<StoreSubDeliveryConfigDto> storeSubDeliveryConfigDtos = tmsServiceWrapper.batchQueryStoreDeliveryConfig(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
                    Set<Integer> deliveryLaunchPointSet = Optional.ofNullable(storeSubDeliveryConfigDtos)
                            .orElse(Lists.newArrayList())
                            .stream()
                            //京东渠道只能是“拣货完成发配”，过滤掉
                            .filter(dto -> !Objects.equals(dto.getChannelType(), DynamicChannelType.JD2HOME.getChannelId()))
                            //没开配送的也过滤掉
                            .filter(dto -> Objects.equals(dto.getOpenFlag(), 1))
                            .map(StoreSubDeliveryConfigDto::getDeliveryLaunchPoint)
                            .collect(Collectors.toSet());
                    if (deliveryLaunchPointSet.size() > 1) {
                        return CommonResponse.fail(ResultCode.FAIL.getCode(), "打开失败，非京东渠道的“发单节点”必须保持一致");
                    }
                    if (CollectionUtils.isNotEmpty(deliveryLaunchPointSet)) {
                        deliveryLaunchPoint =  deliveryLaunchPointSet.iterator().next();
                    }
                }
            }
        } catch (Exception e) {
            log.error("process deliveryLaunchPoint error", e);
            Cat.logEvent("DELIVERY_CONFIG_UPDATE", "LaunchPointForceSyncConfigError");
        }

        ConfigCommonResponse deliveryConfigResponse = tmsServiceWrapper.deliveryStoreConfigUpdate(request);
        if (deliveryConfigResponse == null) {
            return CommonResponse.fail(1, "门店配置信息不存在");
        }
        if (deliveryConfigResponse.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            return CommonResponse.fail(deliveryConfigResponse.getStatus().getCode(), deliveryConfigResponse.getStatus().getMsg());
        }
        ShopAuth shopAuth = new ShopAuth();
        if (deliveryConfigResponse.getShopAuthConfig() != null) {
            shopAuth.setCode(deliveryConfigResponse.getShopAuthConfig().getCode());
            shopAuth.setIsAuthed(deliveryConfigResponse.getShopAuthConfig().getIsAuthed());
            shopAuth.setUrl(Optional.ofNullable(deliveryConfigResponse.getShopAuthConfig().getDapLinkUrl()).orElse(""));
        }
        DeliveryManagementConfigResponse response = new DeliveryManagementConfigResponse();
        response.setShopAuth(shopAuth);

        //补充逻辑：除京东和 抖音&&平台配送之外，且为1开通时，更新为用上面统一的发单接单
        try {
            if (MccConfigUtil.getLaunchPointForceSyncConfig(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
                boolean douYinPlatForm = Objects.equals(request.getAggDeliveryPlatformInfo().getChannelType(), ChannelType.DOU_YIN.getValue()) && Objects.equals(request.getAggDeliveryPlatformInfo().getPlatformCode(), DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode());
                if (Objects.equals(request.getAggDeliveryPlatformInfo().getStatus(), 1)
                        && !Objects.equals(request.getAggDeliveryPlatformInfo().getChannelType(), DynamicChannelType.JD2HOME.getChannelId())
                        && !douYinPlatForm
                ) {
                    if (Objects.nonNull(deliveryLaunchPoint)) {
                        DeliveryManagementModifyRequest modifyRequest = new DeliveryManagementModifyRequest();
                        modifyRequest.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
                        StoreDeliveryChannelLaunchPointVo storeDeliveryChannelLaunchPointVo = new StoreDeliveryChannelLaunchPointVo();
                        storeDeliveryChannelLaunchPointVo.setChannelType(request.getAggDeliveryPlatformInfo().getChannelType());
                        storeDeliveryChannelLaunchPointVo.setDeliveryLaunchPoint(deliveryLaunchPoint);
                        storeDeliveryChannelLaunchPointVo.setDeliveryLaunchDelayMinutes(DEFAULT_IMMEDIATE_ORDER_DELAY_MINUTES);
                        storeDeliveryChannelLaunchPointVo.setBookingOrderDeliveryLaunchMinutes(DEFAULT_EMPOWER_BOOKING_ORDER_DELAY_MINUTES);
                        modifyRequest.setDeliveryLaunchPoints(Lists.newArrayList(storeDeliveryChannelLaunchPointVo));

                        CommonResponse modifyResponse = tmsServiceWrapper.deliveryStoreConfigModify(modifyRequest);
                        if (!modifyResponse.isSuccess()) {
                            throw new CommonRuntimeException("modifyResponse error");
                        }
                    }

                }
            }
        } catch (Exception e) {
            log.error("invoke deliveryManagementModify error", e);
            Cat.logEvent("DELIVERY_CONFIG_UPDATE", "deliveryStoreConfigModifyError");
        }
        return CommonResponse.success(response);
    }

    @MethodDoc(
            displayName = "我的配送管理",
            description = "获取配送管理-查询门店的配置信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店的配置信息的请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/my/deliverymanagement/querystoreconfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取配送管理-查询门店的配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ResponseBody
    @PostMapping("/querystoreconfig")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<DeliveryStoreManagementQueryResponse> deliveryStoreManagementQuery(@Valid @RequestBody DeliveryStoreManagementQueryRequest request) {
        DeliveryStoreManagementQueryResponse response = tmsServiceWrapper.deliveryStoreConfigQuery(request.getStoreId());
        return response == null ? CommonResponse.fail(1, "门店配置信息不存在") : CommonResponse.success(response);
    }

    @MethodDoc(
            displayName = "我的配送管理",
            description = "获取配送管理-修改配送节点配置信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "修改配送节点配置信息请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/my/deliverymanagement/modify",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "获取配送管理-修改配送节点配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ResponseBody
    @PostMapping("/modify")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<Void> deliveryManagementModify(@Valid @RequestBody DeliveryManagementModifyRequest request) {
        return tmsServiceWrapper.deliveryStoreConfigModify(request);
    }
}
