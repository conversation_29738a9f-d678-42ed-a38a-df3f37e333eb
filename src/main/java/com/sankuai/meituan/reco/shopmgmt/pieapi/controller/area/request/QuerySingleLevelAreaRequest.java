package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.area.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QuerySingleLevelAreaInfoRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/19 19:30
 **/
@Data
public class QuerySingleLevelAreaRequest {

    @FieldDoc(
            description = "父地区id"
    )
    private Long parentId;

    @FieldDoc(
            description = "地区名"
    )
    private String areaName;

    @FieldDoc(
            description = "层级"
    )
    @Min(value = 1, message = "地区层级最小为1")
    @Max(value = 4, message = "地区层级最大为4")
    private Integer level;

    @FieldDoc(
            description = "是否有效 1-有效，2-删除"
    )
    @Min(value = 1, message = "是否有效标志错误 1-有效，2-删除")
    @Min(value = 2, message = "是否有效标志错误 1-有效，2-删除")
    private Integer valid;

    @FieldDoc(
            description = "数据来源：1-标品库，2-开放平台"
    )
    private Integer dataSource;

    public static QuerySingleLevelAreaInfoRequest buildBizRequest(Long tenantId, QuerySingleLevelAreaRequest request){
        QuerySingleLevelAreaInfoRequest bizRequest = new QuerySingleLevelAreaInfoRequest();
        bizRequest.setTenantId(tenantId);
        bizRequest.setAreaName(request.getAreaName());
        bizRequest.setParentId(request.getParentId());
        bizRequest.setLevel(request.getLevel());
        bizRequest.setValid(request.getValid());
        bizRequest.setDataSource(request.getDataSource());
        return bizRequest;
    }
}
