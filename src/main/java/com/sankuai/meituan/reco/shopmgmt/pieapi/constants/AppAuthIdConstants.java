package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;
// Copyright (C) 2019 Meituan
// All rights reserved

/**
 * @author: <EMAIL>
 * @class: AppAuthIdConstants
 * @date: 2019-09-03 17:13:32
 * @desc:
 */
public enum AppAuthIdConstants {

    /**
     * 主客户端
     */
    SHU_GUO_PAI(5, "牵牛花APP"),
    /**
     * 8、9、11、12、13 为 5 的权限子应用
     */
    AREA_CENTRAL_WAREHOUSE(8, "区域中心仓"),
    SHARED_WAREHOUSE(9, "共享前置仓"),
    STORE(11, "门店"),
    SHARED_WAREHOUSE_STORE(12, "共享前置仓门店"),
    FULL_STORE(13, "全部门店/总部"),
    ;

    private int val;

    private String msg;

    AppAuthIdConstants(int val, String msg) {
        this.val = val;
        this.msg = msg;
    }

    public int val() {
        return val;
    }

    public String getMsg() {
        return msg;
    }
}
