package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

/**
 * <AUTHOR>
 * @Description 渠道特殊属性枚举
 * @Date 2022/7/1 17:25
 **/
public enum ChannelSpecialAttrEnum {

    BRAND(1200000088, "品牌"),
    PRODUCING_AREA(1200000094, "产地"),
    DOUYIN_BRAND(1687, "抖音品牌"),
    ELM_BRAND(20000, "饿了么品牌")
    ;

    private final long code;
    private final String msg;

    ChannelSpecialAttrEnum(long code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public long getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isSpecialAttrEnum(Long code){
        if(code == null){
            return false;
        }
        for(ChannelSpecialAttrEnum item : ChannelSpecialAttrEnum.values()){
            if(item.code == code){
                return true;
            }
        }
        return false;
    }

    public static String getAttrName(Long code){
        if(null == code){
            return "";
        }
        for(ChannelSpecialAttrEnum item : ChannelSpecialAttrEnum.values()){
            if(item.code == code){
                return item.msg;
            }
        }
        return "";
    }
}
