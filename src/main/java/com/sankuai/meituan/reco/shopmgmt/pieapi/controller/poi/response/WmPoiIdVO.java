package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "外卖PoiIdVO"
)
@Data
public class WmPoiIdVO {
    @FieldDoc(
            description = "外卖门店编码",
            example = {}
    )
    private String wmPoiId;
    @FieldDoc(
            description = "混淆后的门店编码",
            example = {}
    )
    private String encWmPoiId;
    @FieldDoc(
            description = "商品详情页地址",
            example = {}
    )
    private String goodsDetailUrlPrifix;
}
