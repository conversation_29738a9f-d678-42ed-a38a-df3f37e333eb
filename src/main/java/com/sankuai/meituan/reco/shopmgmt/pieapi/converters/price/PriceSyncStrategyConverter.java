package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.price;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.PriceStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuHitSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.strategy.PriceCentModeEnum;
import com.sankuai.meituan.shangou.empower.price.client.enums.strategy.PriceStrategyTypeEnum;
import com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.type.TypeReference;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 定价方式转换器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class PriceSyncStrategyConverter {

    /**
     * 所有门店
     */
    private static final int ALL_STORE_FLAG = 0;

    /**
     * 价格范围区间长度
     */
    private static final int PRICE_RANGE_ARRAY_LENGTH = 2;

    private PriceSyncStrategyConverter() {

    }

    public static String calculateRetailPrice(SkuHitSyncStrategyDTO skuHitSyncStrategyDTO, Long storePrice) {
        if (Objects.isNull(storePrice)) {
            return "";
        }

        if (skuHitSyncStrategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.FIX_PRICE) {
            return smaller100Times(skuHitSyncStrategyDTO.getFixedPrice());
        } else {
            PriceStrategyDTO priceStrategyDTO = skuHitSyncStrategyDTO.getPriceStrategyDTO();
            BigDecimal retailPrice = BigDecimal.valueOf(storePrice).multiply(BigDecimal.valueOf(10000L + priceStrategyDTO.getAdjustPercent()))
                    .divide(BigDecimal.valueOf(10000), 0, RoundingMode.DOWN)
                    .add(BigDecimal.valueOf(priceStrategyDTO.getAdjustMoney()));
            return smaller100Times(retailPrice.longValue());
        }
    }

    /**
     * 计算价格时添加分位规则
     */
    public static String calculateRetailPriceWithCentMode(Long tenantId,
                                                          SkuHitSyncStrategyDTO skuHitSyncStrategyDTO,
                                                          Long storePrice,
                                                          Integer priceCentMode) {
        if (Objects.isNull(storePrice)) {
            return "";
        }

        if (skuHitSyncStrategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.FIX_PRICE) {
            return smaller100Times(skuHitSyncStrategyDTO.getFixedPrice());
        } else {
            PriceStrategyDTO priceStrategyDTO = skuHitSyncStrategyDTO.getPriceStrategyDTO();
            BigDecimal retailPrice = BigDecimal.valueOf(storePrice).multiply(BigDecimal.valueOf(10000L + priceStrategyDTO.getAdjustPercent()))
                    .divide(BigDecimal.valueOf(10000), 0, RoundingMode.HALF_UP)
                    .add(BigDecimal.valueOf(priceStrategyDTO.getAdjustMoney()));
            Long result = retailPrice.longValue();
            // 当存在分位规则时，应该根据分位规则再进行一次分位处理
            if (MccConfigUtil.isCalPriceWithCentMode(tenantId) && priceCentMode != null) {
                PriceCentModeEnum priceCentModeEnum = PriceCentModeEnum.convertMode(priceCentMode);
                if (priceCentModeEnum != null) {
                    result = PriceCentModeEnum.calc(result, priceCentModeEnum);
                }
            }
            return smaller100Times(result);
        }
    }


    public static String genDescription(SkuHitSyncStrategyDTO syncStrategyDTO, String storeName) {

        if (syncStrategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.SINGLE_SKU_FIX_STRATEGY_PRICE) {
            return "该商品的零售价将在进货价的基础上，按照设置的提价规则自动更新";
        }

        PriceStrategyDTO priceStrategyDTO = syncStrategyDTO.getPriceStrategyDTO();
        StringBuilder sb = new StringBuilder();
        if (priceStrategyDTO.getStrategyType() == PriceStrategyTypeEnum.CHANNEL_ADJUST_PRICE
                && priceStrategyDTO.getStoreIdList().get(0).intValue() == ALL_STORE_FLAG) {
            sb.append(ChannelTypeEnum.findChannelNameByChannelId(priceStrategyDTO.getChannelId())).append("渠道");
        } else if (priceStrategyDTO.getStrategyType() == PriceStrategyTypeEnum.CHANNEL_ADJUST_PRICE
                && priceStrategyDTO.getStoreIdList().get(0).intValue() != ALL_STORE_FLAG) {
            sb.append(storeName);
        } else if (priceStrategyDTO.getStrategyType() == PriceStrategyTypeEnum.PRICE_RANGE_ADJUST_PRICE) {
            sb.append(storeName);
            sb.append(getPriceRange(PriceStrategyTypeEnum.PRICE_RANGE_ADJUST_PRICE, priceStrategyDTO.getStrategyContent()));
        } else if (priceStrategyDTO.getStrategyType() == PriceStrategyTypeEnum.CATEGORY_ADJUST_PRICE) {
            sb.append(storeName);
            sb.append(getCategoryNames(PriceStrategyTypeEnum.CATEGORY_ADJUST_PRICE, priceStrategyDTO.getExt()));
        } else {
            sb.append(storeName);
            sb.append(getCategoryNames(PriceStrategyTypeEnum.CATEGORY_PRICE_RANGE_ADJUST_PRICE, priceStrategyDTO.getExt()));
            sb.append(getPriceRange(PriceStrategyTypeEnum.CATEGORY_PRICE_RANGE_ADJUST_PRICE, priceStrategyDTO.getExt()));
        }

        sb.append("所有商品提价").append(smaller100Times(priceStrategyDTO.getAdjustPercent())).append("%");
        if (Objects.nonNull(priceStrategyDTO.getAdjustMoney())) {
            sb.append("再加").append(smaller100Times(priceStrategyDTO.getAdjustMoney())).append("元");
        }
        return sb.toString();
    }

    public static String getPriceRange(PriceStrategyTypeEnum strategyType, String ext) {

        if (strategyType == null || StringUtils.isEmpty(ext)) {
            return "";
        }

        try {
            if (strategyType.getValue() == PriceStrategyTypeEnum.PRICE_RANGE_ADJUST_PRICE.getValue()
                    || strategyType.getValue() == PriceStrategyTypeEnum.CATEGORY_PRICE_RANGE_ADJUST_PRICE.getValue()) {
                Map<String, Object> map = JacksonUtils.fromJson(ext, new TypeReference<Map<String, Object>>() {
                });
                String priceRange = (String) map.get("priceRange");
                String[] priceArray = priceRange.split("-");
                if (priceArray.length == PRICE_RANGE_ARRAY_LENGTH && StringUtils.isNotBlank(priceArray[0]) && StringUtils.isNotBlank(priceArray[1])) {
                    return smaller100Times(Integer.parseInt(priceArray[0])) + "元至" + smaller100Times(Integer.parseInt(priceArray[1])) + "元";
                }
            }
        } catch (Exception e) {
            log.warn("分类区间解析错误， ext [{}]", ext);
        }
        return "";
    }

    public static String getCategoryNames(PriceStrategyTypeEnum strategyType, String ext) {

        if (strategyType == null || StringUtils.isEmpty(ext)) {
            return "";
        }

        try {
            if (strategyType.getValue() == PriceStrategyTypeEnum.CATEGORY_ADJUST_PRICE.getValue()
                    || strategyType.getValue() == PriceStrategyTypeEnum.CATEGORY_PRICE_RANGE_ADJUST_PRICE.getValue()) {
                Map<String, Object> map = JacksonUtils.fromJson(ext, new TypeReference<Map<String, Object>>() {
                });
                return ((List<String>) map.get("categoryList")).get(0);
            }
        } catch (Exception e) {
            log.warn("分类区间解析错误， ext [{}]", ext);
        }

        return "";
    }

    public static String smaller100Times(Object value) {
        if (value == null) {
            return "";
        }
        if (value instanceof Long) {
            return BigDecimal.valueOf((Long) value).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).toString();
        } else {
            return BigDecimal.valueOf((Integer) value).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).toString();
        }
    }
}
