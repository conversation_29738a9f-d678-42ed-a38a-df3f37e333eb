package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.image.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 图片上传结果
 *
 * <AUTHOR>
 * @date 2021-11-02 17:12
 */
@AllArgsConstructor
@Data
@TypeDoc(
        description = "图片上传结果"
)
@ApiModel("图片上传结果")
public class ImageUploadResultVO {

    /**
     * 备注：此处返回字段名称fileUrl，是因为前端组件限制，组件解析的是fileUrl
     */
    @FieldDoc(
            description = "图片url连接", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "图片url连接")
    private String fileUrl;
}
