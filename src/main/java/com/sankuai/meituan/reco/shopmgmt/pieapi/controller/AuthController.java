package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonV2Response;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.BatchAuthCodeCheckRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.DataAuthResourceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.EquipmentCheckRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.BatchAuthCodeCheckResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.DataAuthPermissionVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.DataAuthResourceResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.DataResourceInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.EquipmentCheckResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.SacAccountPermissionVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.SacMenuDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacAccountClient;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataPermissionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Created by shihuifeng on 19/11/18.
 */
@InterfaceDoc(
        displayName = "权限controller",
        type = "restful",
        scenarios = "负责封装权限信息(例如按钮权限)给前端使用,用于控制按钮是否展示",
        description = "负责封装权限信息(例如按钮权限)给前端使用,根本当前用户登录获取的token解析出账户,并获取账户的权限信息提供给前端控制显示或隐藏",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "负责封闭权限信息(例如按钮权限)给前端使用,用于控制按钮是否展示")
@RestController
@RequestMapping("/pieapi/auth")
public class AuthController {

    @Autowired
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private SacAccountClient sacAccountClient;


    @MethodDoc(
            description = "提供前端根据当前账号查询权限列表",
            displayName = "查询权限列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "提供前端根据当前账号查询权限列表",
                            type = DataAuthResourceRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "CommonResponse", description = "通用返回结果", type = CommonResponse.class)
            },
            returnValueDescription = "提供前端根据当前账号查询权限列表",
            restExamplePostData = "{\n" +
                    "  \"code\": \"10\",\n" +
                    "  \"storeId\": 1000006,\n" +
                    "  \"type\": 3\n" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": {\n" +
                    "    \"dataPermissions\": [\n" +
                    "      {\n" +
                    "        \"dataResourceInfoVo\": {\n" +
                    "          \"appId\": 1,\n" +
                    "          \"code\": \"10\",\n" +
                    "          \"field\": \"\",\n" +
                    "          \"id\": 1,\n" +
                    "          \"method\": 0,\n" +
                    "          \"name\": \"审核\",\n" +
                    "          \"parentCode\": \"\",\n" +
                    "          \"type\": 3,\n" +
                    "          \"uri\": \"\",\n" +
                    "          \"valid\": 1\n" +
                    "        },\n" +
                    "        \"isAuth\": 0\n" +
                    "      }\n" +
                    "    ]\n" +
                    "  },\n" +
                    "  \"message\": \"\"\n" +
                    "}\n" +
                    "\n",
            restExampleUrl = "/pieapi/auth/dataauthresource",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
            @ApiOperation(value = "查询权限列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/dataauthresource", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<DataAuthResourceResult> dataAuthResource(@RequestBody @Valid DataAuthResourceRequest request){
        User user = ApiMethodParamThreadLocal.getIdentityInfo(request.getStoreId()).getUser();
        return CommonResponse.success(convertAuthResource(authThriftWrapper.dataAuthResource(request, user)));
    }


    @MethodDoc(
            description = "批量查询权限码是否有权限",
            displayName = "批量查询权限码是否有权限",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量查询权限码是否有权限",
                            type = DataAuthResourceRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "CommonResponse", description = "通用返回结果", type = CommonResponse.class)
            },
            returnValueDescription = "批量查询权限码是否有权限",
            restExamplePostData = "{\n" +
                    "  \"authCode\": [\"10\",\"20\"]" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": {\n" +
                    "          \"10\": 1,\n" +
                    "          \"20\": \"0\",\n" +
                    "        }\n" +
                    "}\n" +
                    "\n",
            restExampleUrl = "/pieapi/auth/batchauthcodecheck",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "批量查询权限码是否有权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/batchauthcodecheck", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<BatchAuthCodeCheckResult> batchAuthCodeCheck(@RequestBody @Valid BatchAuthCodeCheckRequest request){
        return authThriftWrapper.batchAuthCodeCheck(request);
    }


    @MethodDoc(
            description = "查询用户权限列表",
            displayName = "查询用户权限列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询用户权限列表",
                            type = DataAuthResourceRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "CommonResponse", description = "通用返回结果", type = CommonResponse.class)
            },
            returnValueDescription = "查询用户权限列表",
            restExamplePostData = "{\n" +
                    "  \"authCode\": [\"10\",\"20\"]" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": {\n" +
                    "          \"10\": 1,\n" +
                    "          \"20\": \"0\",\n" +
                    "        }\n" +
                    "}\n" +
                    "\n",
            restExampleUrl = "/pieapi/auth/accountPermission/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询用户权限列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/accountPermission/list", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonV2Response<SacAccountPermissionVO> accountPermissionList(){
        SacAccountPermissionVO sacAccountPermissionVO = sacAccountClient.querySacAccountPermission();
        return CommonV2Response.success(sacAccountPermissionVO);
    }



    @MethodDoc(
            description = "查询某个应用菜单列表",
            displayName = "查询某个应用菜单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询某个应用菜单列表",
                            type = DataAuthResourceRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "CommonV2Response", description = "查询某个应用菜单列表", type = CommonResponse.class)
            },
            returnValueDescription = "查询某个应用菜单列表",
            restExamplePostData = "{\n" +
                    "  \"authCode\": [\"10\",\"20\"]" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": {\n" +
                    "          \"10\": 1,\n" +
                    "          \"20\": \"0\",\n" +
                    "        }\n" +
                    "}\n" +
                    "\n",
            restExampleUrl = "/pieapi/auth/appMenu/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 用户token获取数据"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询某个应用菜单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/appMenu/list", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonV2Response<List<SacMenuDetailVo>> appMenuList(){
        List<SacMenuDetailVo> sacMenuDetailVos = sacAccountClient.querySacMenuListByAppId();
        return CommonV2Response.success(sacMenuDetailVos);
    }

    private DataAuthResourceResult convertAuthResource(List<DataPermissionVo> resourceResponseList){
        DataAuthResourceResult resourceResult = new DataAuthResourceResult();
        List<DataAuthPermissionVo> dataAuthPermissionVoList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(resourceResponseList)){
            for(DataPermissionVo dataPermissionVo : resourceResponseList){
                DataAuthPermissionVo dataAuthPermissionVo = new DataAuthPermissionVo();
                dataAuthPermissionVo.setIsAuth(dataPermissionVo.getIsAuth());
                if(dataPermissionVo.getResourceInfoVo() != null) {
                    DataResourceInfoVo vo = new DataResourceInfoVo();
                    vo.setId(dataPermissionVo.getResourceInfoVo().getId());
                    vo.setAppId(dataPermissionVo.getResourceInfoVo().getAppId());
                    vo.setCode(dataPermissionVo.getResourceInfoVo().getCode());
                    vo.setParentCode(dataPermissionVo.getResourceInfoVo().getParentCode());
                    vo.setName(dataPermissionVo.getResourceInfoVo().getName());
                    vo.setField(dataPermissionVo.getResourceInfoVo().getField());
                    vo.setUri(dataPermissionVo.getResourceInfoVo().getUrl());
                    vo.setMethod(dataPermissionVo.getResourceInfoVo().getMethod());
                    vo.setType(dataPermissionVo.getResourceInfoVo().getType());
                    vo.setValid(dataPermissionVo.getResourceInfoVo().getValid());
                    dataAuthPermissionVo.setDataResourceInfoVo(vo);
                }
                dataAuthPermissionVoList.add(dataAuthPermissionVo);
            }
        }
        resourceResult.setDataPermissions(dataAuthPermissionVoList);

        return resourceResult;
    }

}
