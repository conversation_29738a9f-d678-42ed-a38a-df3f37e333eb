package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/06/13 11:22:05
 * @email <EMAIL>
 */
@TypeDoc(
        description = "获取门店类型的响应"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetPoiOperationModeResponse {

    @FieldDoc(description = "1-直营 2-加盟 为空等于直营")
    private Integer operationMode;

}
