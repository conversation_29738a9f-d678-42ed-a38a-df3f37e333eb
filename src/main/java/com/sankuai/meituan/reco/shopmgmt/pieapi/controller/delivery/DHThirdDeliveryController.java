package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.delivery;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third.AcceptThirdDeliveryPickOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third.DrunkHorseTurnToMerchantSelfDeliveryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third.PageQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third.QueryWaitToDeliveryOrderBySubTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third.ThirdDeliveryOrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third.ThirdWaitToDeliverySubTypeCountResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.delivery.third.DHThirdDeliveryService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @since 2023/8/25 14:29
 * @desc 歪马业务三方配送相关页面
 **/

@Slf4j
@Auth
@RestController
//todo linxiaorui 记得加鉴权
@RequestMapping("/pieapi/delivery/dh/third")
public class DHThirdDeliveryController {

    @Resource
    private DHThirdDeliveryService dhThirdDeliveryService;

    @MethodDoc(
            displayName = "分页查询待领取的拣货任务列表",
            description = "分页查询待领取的拣货任务列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待领取的拣货任务列表",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/queryWaitToTake",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "分页查询待领取的拣货任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitToTake", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ThirdDeliveryOrderListResponse> queryWaitToTake(@Valid @RequestBody PageQueryRequest request) {
        return CommonResponse.success(dhThirdDeliveryService.queryWaitToTake(request.getPage(), request.getPageSize()));
    }

    @MethodDoc(
            displayName = "分页查询待拣货的拣货任务列表",
            description = "分页查询待拣货的拣货任务列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待领取的拣货任务列表",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/queryWaitToPick",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "分页查询待领取的拣货任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWaitToPick", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ThirdDeliveryOrderListResponse> queryWaitToPick(@Valid @RequestBody PageQueryRequest request) {
        return CommonResponse.success(dhThirdDeliveryService.queryWaitToPick(request.getPage(), request.getPageSize()));
    }

    @MethodDoc(
            displayName = "分页查询待配送子tab运单列表",
            description = "分页查询待配送子tab运单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询待配送子tab运单列表",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/querywaitdeliverybysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "分页查询待配送子tab运单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querywaitdeliverybysubtype", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ThirdDeliveryOrderListResponse> queryWaitDeliveryBySubType(@Valid @RequestBody QueryWaitToDeliveryOrderBySubTypeRequest request) {
        return CommonResponse.success(dhThirdDeliveryService.queryWaitDeliveryBySubType(request));
    }

    @MethodDoc(
            displayName = "分页查询配送异常tab运单列表",
            description = "分页查询配送异常tab运单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询配送异常tab运单列表请求",
                            type = PageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/queryDeliveryException",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "分页查询配送异常tab运单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryDeliveryException", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ThirdDeliveryOrderListResponse> queryDeliveryException(@Valid @RequestBody PageQueryRequest request) {
        return CommonResponse.success(dhThirdDeliveryService.queryThirdOnExceptionOrders(request));
    }

    @MethodDoc(
            displayName = "待配送订单子类型数量查询",
            description = "待配送订单子类型数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "待配送订单子类型数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/querywaitdeliverysubtypecount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "待配送订单子类型数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitdeliverysubtypecount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ThirdWaitToDeliverySubTypeCountResponse> queryWaitDeliverySubTypeCount() {
        return CommonResponse.success(dhThirdDeliveryService.queryWaitDeliverySubTypeCount());
    }

    @MethodDoc(
            displayName = "门店待领取拣货单数量查询",
            description = "门店待领取拣货单数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店待领取拣货单数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/queryStoreUnAcceptedPickOrderCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "门店待领取拣货单数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/queryStoreUnAcceptedPickOrderCount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Integer> queryStoreUnAcceptedPickOrderCount() {
        return CommonResponse.success(dhThirdDeliveryService.queryStoreUnAcceptedPickOrderCount());
    }

    @MethodDoc(
            displayName = "骑手待拣货的拣货单数量查询",
            description = "骑手待拣货的拣货单数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手待拣货的拣货单数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/queryWaitPickOrderCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "骑手待拣货的拣货单数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/queryWaitPickOrderCount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Integer> queryWaitPickOrderCount() {
        return CommonResponse.success(dhThirdDeliveryService.queryWaitPickOrderCount());
    }

    @MethodDoc(
            displayName = "三方配送转商家自配送",
            description = "三方配送转商家自配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "三方配送转商家自配送",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/turnToSelfDelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "三方配送转商家自配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ResponseBody
    @PostMapping("/turnToSelfDelivery")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<Void> turnToMerchantSelfDelivery(@Valid @RequestBody DrunkHorseTurnToMerchantSelfDeliveryRequest request) throws TException {
        dhThirdDeliveryService.turnToMerchantSelfDelivery(request.getOrderId(), request.getRiderAccountId());
        return CommonResponse.success(null);
    }

    @MethodDoc(
            displayName = "自营骑手领取三方配送拣货单",
            description = "自营骑手领取三方配送拣货单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自营骑手领取三方配送拣货单",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/delivery/dh/third/acceptThirdDeliveryPickOrder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "三方配送转商家自配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ResponseBody
    @PostMapping("/acceptThirdDeliveryPickOrder")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<Void> acceptThirdDeliveryPickOrder(@Valid @RequestBody AcceptThirdDeliveryPickOrderRequest request) {
        dhThirdDeliveryService.acceptThirdDeliveryPickOrder(request.getViewOrderId(), request.getChannelId());
        return CommonResponse.success(null);
    }

}
