package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

import javax.validation.Valid;

import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfirmLetterStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.SecurityDepositStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.PoiManageModeDto;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.GenerateConfirmLetterDto;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.GenerateConfirmLetterRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.securitydeposit.PayResultQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.securitydeposit.QueryStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.securitydeposit.StoreIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuTotalCountOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.PayResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.SecurityDepositInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.SecurityDepositPaySuccessVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.SecurityDepositStatusVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.ConfirmLetterFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.PieApiMccUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SecurityDepositWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StoreWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 12:59
 * @Description:
 */
@InterfaceDoc(
        displayName = "保证金服务",
        type = "restful",
        scenarios = "保证金服务",
        description = "保证金服务",
        host = "https://pieapi.empower.shangou.meituan.com/"
)
@Slf4j
@Api(value = "保证金服务")
@RestController
@RequestMapping("/pieapi/securitydeposit")
public class SecurityDepositController {


    @Autowired
    private SecurityDepositWrapper securityDepositWrapper;

    @Autowired
    private StoreWrapper storeWrapper;

    @Autowired
    private ConfirmLetterFacade confirmLetterFacade;

    @Autowired
    private TenantWrapper tenantWrapper;


    @MethodDoc(
            description = "查询门店保证金信息接口",
            displayName = "查询门店保证金信息接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店保证金列表接口",
                            type = SkuTotalCountOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/securitydeposit/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，并判定当前用户是否有门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SecurityDepositInfoVO> info(@Valid @RequestBody StoreIdRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        SecurityDepositInfoVO securityDeposit = securityDepositWrapper.getSecurityDeposit(user, request.getStoreId());
        if (securityDeposit != null && securityDeposit.getStatus() != SecurityDepositStatusEnum.UNINITIALIZED.getType()) {
            securityDeposit.setChangeRecords(securityDepositWrapper.queryDepositChangeRecord(user.getTenantId(), securityDeposit.getBudgetId()));
        }

        //如果保证金状态为有扣款，判断保证金余额是否低于一定比例，如果是，则禁止登录
        if (securityDeposit != null && securityDeposit.getStatus() == SecurityDepositStatusEnum.DEDUCTED.getType()) {
            String tenantLowestRatio = MccConfigUtil.getLowestSecDepRemainAmountRatioByTargetKey(String.valueOf(user.getTenantId()));
            String lowestRatio = StringUtils.isBlank(tenantLowestRatio)
                    ? MccConfigUtil.getLowestSecDepRemainAmountRatioByTargetKey(String.valueOf(PieApiMccUtils.getSupermarketNewSupplyBizType()))
                    : tenantLowestRatio;
            if (StringUtils.isNotBlank(lowestRatio)) {
                securityDeposit.setLowestRatio(Double.valueOf(lowestRatio));
                BigDecimal lowestSecDepAmount = new BigDecimal(lowestRatio).divide(new BigDecimal(100)).multiply(BigDecimal.valueOf(securityDeposit.getAmount()));
                if (lowestSecDepAmount.compareTo(BigDecimal.valueOf(securityDeposit.getRemainAmount())) > 0) {
                    securityDeposit.setIsFundSufficient(YesNoEnum.NO.getKey());
                } else {
                    securityDeposit.setIsFundSufficient(YesNoEnum.YES.getKey());
                }
            }
        }
        return securityDeposit == null ? CommonResponse.success(new SecurityDepositInfoVO()) :
                CommonResponse.success(securityDeposit);
    }


    @MethodDoc(
            description = "查询门店保证金状态接口",
            displayName = "查询门店保证金状态接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店保证金状态接口",
                            type = StoreIdRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/securitydeposit/queryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，并判定当前用户是否有门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStatus", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SecurityDepositStatusVO> queryStatus(@Valid @RequestBody QueryStatusRequest request) {

        if (StringUtils.isEmpty(request.getStoreId()) || !StringUtils.isNumeric(request.getStoreId())) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "非法的门店ID");
        }

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        long storeId = Long.parseLong(request.getStoreId());
        // 门店、共享前置仓类型，租户管理模式下，可以查询保证金
        if (isNotStoreOrShareableWarehouse(user.getTenantId(), storeId)) {
            return CommonResponse.success(null);
        }

        SecurityDepositInfoVO securityDeposit = securityDepositWrapper.getSecurityDeposit(user, storeId);

        SecurityDepositStatusVO securityDepositStatusVO = new SecurityDepositStatusVO();

        if (securityDeposit != null) {
            securityDepositStatusVO.setId(securityDeposit.getId());
            securityDepositStatusVO.setStatus(securityDeposit.getStatus());
            securityDepositStatusVO.setContractStatus(securityDeposit.getContractStatus());
            securityDepositStatusVO.setUnsignedContractUrl(securityDeposit.getUnsignedContractUrl());
            if (StringUtils.isNotBlank(securityDeposit.getAppendConfirmLetterContractUrl())) {
                securityDepositStatusVO.setContractUrl(securityDeposit.getAppendConfirmLetterContractUrl());
            } else {
                securityDepositStatusVO.setContractUrl(securityDeposit.getContractUrl());
            }
            securityDepositStatusVO.setContractExpireTime(securityDeposit.getContractExpireTime());
            securityDepositStatusVO.setIsStoreManager(securityDeposit.getIsStoreManager());
            securityDepositStatusVO.setStoreManagerList(securityDeposit.getStoreManagerList());
            securityDepositStatusVO.setContractId(securityDeposit.getContractId());
            securityDepositStatusVO.setIsMainContract(securityDeposit.getIsMainContract());
            securityDepositStatusVO.setSecurityDepositMode(securityDeposit.getSecurityDepositMode());

            if (Objects.equals(request.getNeedGenerateConfirmLetter(), Boolean.TRUE)) {
                fillConfirmLetterIfNecessary(user, storeId, securityDepositStatusVO);
            }

            //如果保证金状态为有扣款，判断保证金余额是否低于一定比例，如果是，则禁止登录
            if (securityDeposit.getStatus() == SecurityDepositStatusEnum.DEDUCTED.getType()) {
                String tenantLowestRatio = MccConfigUtil.getLowestSecDepRemainAmountRatioByTargetKey(String.valueOf(user.getTenantId()));
                String lowestRatio = StringUtils.isBlank(tenantLowestRatio)
                        ? MccConfigUtil.getLowestSecDepRemainAmountRatioByTargetKey(String.valueOf(PieApiMccUtils.getSupermarketNewSupplyBizType()))
                        : tenantLowestRatio;
                if (StringUtils.isNotBlank(lowestRatio)) {
                    securityDepositStatusVO.setLowestRatio(Double.valueOf(lowestRatio));
                    BigDecimal lowestSecDepAmount = new BigDecimal(lowestRatio).divide(new BigDecimal(100)).multiply(BigDecimal.valueOf(securityDeposit.getAmount()));
                    if (lowestSecDepAmount.compareTo(BigDecimal.valueOf(securityDeposit.getRemainAmount())) > 0) {
                        securityDepositStatusVO.setIsFundSufficient(YesNoEnum.NO.getKey());
                    } else {
                        securityDepositStatusVO.setIsFundSufficient(YesNoEnum.YES.getKey());
                    }
                }
            }
        }

        return CommonResponse.success(securityDepositStatusVO);
    }

    private boolean isNotStoreOrShareableWarehouse(Long tenantId, long storeId) {
        Map<Long, PoiInfoDto> poiInfoDtoMap = storeWrapper.queryPoiInfoById(tenantId, Arrays.asList(storeId));
        PoiInfoDto poiInfoDto = poiInfoDtoMap.get(storeId);
        Integer entityType = (poiInfoDto == null ? null : poiInfoDto.getEntityType());

        return !(Objects.equals(PoiEntityTypeEnum.STORE.code(), entityType) || Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType));
    }

    private void fillConfirmLetterIfNecessary(User user, long storeId, SecurityDepositStatusVO vo) {
        PoiManageModeDto poiManageMode = tenantWrapper.queryPoiManageMode(user.getTenantId());
        // 默认置为无需签署
        vo.setConfirmLetterStatus(ConfirmLetterStatusEnum.NOT_NEED_SIGN.code());

        PoiInfoDto poiInfoDto = getPoiInfoById(user.getTenantId(), storeId);
        vo.setPoiCreateGteOneMonth(isGteOneMonth(poiInfoDto.getCreateTime()));

        if (notNeedSignConfirmLetter(poiManageMode, vo.getContractId())) {
            return;
        }

        GenerateConfirmLetterDto dto;
        try {
            dto = generateConfirmLetter(user.getTenantId(), vo.getContractId(), storeId);
        }
        catch (CommonRuntimeException e) {
            // 非管理员账号登录，则需要设置未签署状态
            if (user.getAccountType() != AccountTypeEnum.ADMIN.getValue()) {
                vo.setConfirmLetterStatus(ConfirmLetterStatusEnum.UN_SIGN.code());
                return;
            }
            throw e;
        }
        if (dto == null || Objects.equals(dto.getStatus(), ConfirmLetterStatusEnum.NOT_NEED_SIGN.code())) {
            return;
        }


        vo.setConfirmLetterStatus(dto.getStatus());
        if (Objects.equals(dto.getStatus(), ConfirmLetterStatusEnum.MERGE_CONTRACT.code())) {
            // 如果确认函的状态为与协议一并签署，则协议url用追加了确认函的url
            vo.setUnsignedContractUrl(dto.getAppendConfirmLetterContractUrl());
        }

        vo.setConfirmLetterId(dto.getId());
        vo.setConfirmLetterUrl(dto.getConfirmLetterUrl());
        // 此处仅需返回10条即可
        vo.setNewlyAddedPoiNameList(dto.getPoiNameList().size() > 10 ? dto.getPoiNameList().subList(0, 10) : dto.getPoiNameList());
    }

    private boolean notNeedSignConfirmLetter(PoiManageModeDto poiManageMode, Long contractId) {
        if (contractId == null) {
            return true;
        }

        return !poiManageMode.supportSecurityDepositTenantMode();
    }

    private PoiInfoDto getPoiInfoById(long tenantId, long storeId) {
        Map<Long, PoiInfoDto> poiInfoDtoMap = storeWrapper.queryPoiInfoById(tenantId, Collections.singletonList(storeId));
        if (poiInfoDtoMap == null || !poiInfoDtoMap.containsKey(storeId)) {
            log.warn("未获取到中台门店信息，poiId: {}", storeId);
            throw new CommonRuntimeException("未获取到门店信息");
        }
        return poiInfoDtoMap.get(storeId);
    }

    private GenerateConfirmLetterDto generateConfirmLetter(long tenantId, long contractId, long storeId) {
        try {
            GenerateConfirmLetterRequest request = new GenerateConfirmLetterRequest();
            request.setTenantId(tenantId);
            request.setContractId(contractId);
            request.setPoiId(storeId);
            return confirmLetterFacade.generateConfirmLetter(request);
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            // 此处仅记录异常，不影响协议的生成
            log.error("生成确认函异常", e);
            return null;
        }
    }

    private boolean isGteOneMonth(long createTime) {
        Date poiCreateDate = new Date(createTime);
        Date confirmLetterOnlineDate = MccConfigUtil.getConfirmLetterOnlineDate();
        // 此处为兼容逻辑，为了避免影响老店的使用，门店创建时间 和 确认函上线时间 取最大的时间
        Date maxDate = poiCreateDate.after(confirmLetterOnlineDate) ? poiCreateDate : confirmLetterOnlineDate;

        LocalDateTime maxLocalDateTime = maxDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return maxLocalDateTime.isBefore(LocalDateTime.now().minusDays(30));
    }


    @MethodDoc(
            description = "门店保证金支付接口",
            displayName = "门店保证金支付接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店保证金支付接口",
                            type = StoreIdRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/securitydeposit/pay",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，并判定当前用户是否有门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pay", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SecurityDepositPaySuccessVO> pay(@Valid @RequestBody StoreIdRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return CommonResponse.success(securityDepositWrapper.securityDepositPay(user, request.getStoreId(), request.getAmount()));
    }




    @MethodDoc(
            description = "门店保证金支付结果查询接口",
            displayName = "门店保证金支付结果查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店保证金支付结果查询接口",
                            type = SkuTotalCountOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"errorRecordList\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/securitydeposit/payResult",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/payResult", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<PayResultVO> payResult(@Valid @RequestBody PayResultQueryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return CommonResponse.success(securityDepositWrapper.queryPayResult(user.getTenantId(), request.getTradeNo()));

    }





}
