package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsumableItem {

    @FieldDoc(description = "skuId")
    private String skuId;

    @NotBlank
    @FieldDoc(description = "耗材数量")
    private String quantity;


    public BigDecimal quantity() {
        return new BigDecimal(quantity);
    }

    public void valid(ConsumableItemDetail configItem) {
        BigDecimal qty = quantity();
        if (configItem == null || qty.compareTo(new BigDecimal(configItem.getMaxQuantity())) > 0) {
            throw new IllegalArgumentException();
        }
    }
}
