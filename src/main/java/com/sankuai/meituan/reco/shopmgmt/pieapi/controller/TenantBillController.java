package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.cat.util.StringUtils;
import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.PageResultV2;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.PayResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.TenantBillPrePayInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.TenantDailyBillVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantBillWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.pay.constant.BillSourceEnum;
import com.sankuai.meituan.shangou.empower.settlement.dto.model.PageInfo;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.ServicePurchaseDetailDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.ServiceRenewalDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.TenantBillServiceBillDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.TenantDailyBillDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.request.CloseServiceBillPayRequest;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.request.PageServicePurchaseDetailRequest;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.request.QueryServiceBillDetailRequest;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.request.TenantBillPageRequest;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.response.PageServicePurchaseDetailResponse;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.response.TenantBillPageResponse;
import com.sankuai.meituan.shangou.empower.tenantbill.enums.BasicServiceBillStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Api(value = "账单相关接口")
@RestController
@RequestMapping("/pieapi/tenantbill")
@Slf4j
public class TenantBillController {


    @Resource
    private TenantBillWrapper tenantBillWrapper;


    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/service/pay", method = RequestMethod.POST)
    @Auth
    public CommonResponse<TenantBillPrePayInfo> pay(@RequestBody TenantBillServicePayRequest request) {
        Long calcAmount = request.parseCalcAmount();
        Long billId = request.getBillId();

        // 如果billId为空，说明是创建账单支付， calcAmount 不能为null
        if (billId == null && calcAmount == null) {
            return CommonResponse.fail2(ResultCode.TENANT_BILL_SERVICE_PAY_AMOUNT_IS_NULL);
        }
        int billSource = request.getBillSource();
        // 新签无服务id,不需要校验
        if(billSource == BillSourceEnum.newSign.getCode() || billSource == BillSourceEnum.buyMore.getCode()){
            NewSignPayInfoVo newSignPayInfo = request.getNewSignPayInfo();
            if(newSignPayInfo == null || StringUtils.isBlank(newSignPayInfo.getCrmOrderId()) || CollectionUtils.isEmpty(newSignPayInfo.getPurchaseDetails())){
                return CommonResponse.fail2(ResultCode.TENANT_BILL_SERVICE_PAY_PARAM_INVALID);
            }
            return CommonResponse.success(tenantBillWrapper.createNewSignPrePay(request));
        }

        List<Long> serviceIdList = request.parseServiceIdList();
        List<ServiceRenewalDto> orderKingRenewalList = request.parseServiceRenewalList();

        if (CollectionUtils.isEmpty(serviceIdList) && CollectionUtils.isEmpty(orderKingRenewalList) && billId == null) {
            return CommonResponse.fail2(ResultCode.TENANT_BILL_SERVICE_PAY_PARAM_INVALID);
        }
        if ((CollectionUtils.isNotEmpty(serviceIdList) || CollectionUtils.isNotEmpty(orderKingRenewalList)) && billId != null) {
            return CommonResponse.fail2(ResultCode.TENANT_BILL_SERVICE_PAY_PARAM_INVALID);
        }

        return CommonResponse.success(tenantBillWrapper.createPrePayForService(billId, calcAmount, serviceIdList, orderKingRenewalList, request.getBillSource(), request.getTradeNo()));
    }


    @MethodLog(logResponse = true, logRequest = true)
    @Auth
    @RequestMapping(value = "/queryDailyBill", method = RequestMethod.POST)
    public CommonResponse<PageResultV2<TenantDailyBillVo>> queryDailyBill(@RequestBody DailyBillRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        //todo 枚举值
        List<String> createUserAccount= new ArrayList<>();
        if(request.getBillType().equals(30) && AccountTypeEnum.ORDINARY_USER.equals(user.getAccountType())){
            // 基础服务费普通账号只能看到自己的订单
           createUserAccount.add(user.getAccountName());
        }
        TenantBillPageRequest tenantBillPageRequest = getTenantDailyBillRequest(request, user.getTenantId(),createUserAccount);
        TenantBillPageResponse response = tenantBillWrapper.queryTenantDailyBillPage(tenantBillPageRequest);

        List<TenantDailyBillVo> tenantDailyBillVos = getTenantDailyBillVos(response.getTenantDailyBillDtos());
        PageInfo pageInfo = response.getPageInfo();
        PageResultV2<TenantDailyBillVo> pageResultV2 = new PageResultV2<>(tenantDailyBillVos, request.getPage(), request.getPageSize(),
                pageInfo.getTotalRecords(), pageInfo.getTotalPage());

        setSegmentBillDetailInfoForBasicServiceBill(pageResultV2.getList());

        return CommonResponse.success(pageResultV2);
    }


    @ApiOperation(value = "查询服务账单详情")
    @RequestMapping(value = "/service/queryBillDetail", method = RequestMethod.POST)
    @Auth
    public CommonResponse<TenantBillServiceBillDto> queryServiceBillDetail(@RequestBody QueryBillDetailRequest request) {
        Long billId = request.getBillId();
        if (billId == null) {
            return CommonResponse.fail2(ResultCode.PARAM_ERR);
        }

        QueryServiceBillDetailRequest req = new QueryServiceBillDetailRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        req.setTenantId(user.getTenantId());
        req.setBillId(billId);
        return CommonResponse.success(tenantBillWrapper.queryServiceBillDetail(req));
    }


    @ApiOperation(value = "查询基础服务费账单明细列表")
    @RequestMapping(value = "/service/queryBillDetailList", method = RequestMethod.POST)
    @Auth
    public CommonResponse<PageResultV2<ServicePurchaseDetailDto>> queryBillDetailList(@RequestBody QueryBillDetailListRequest request) {
        Long billId = request.getBillId();
        if (billId == null) {
            return CommonResponse.fail2(ResultCode.PARAM_ERR);
        }

        PageServicePurchaseDetailRequest req = new PageServicePurchaseDetailRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        req.setTenantId(user.getTenantId());
        req.setBillId(billId);
        req.setSubjectIdList(request.getSubjectIdList());
        req.setPage(request.getPage());
        req.setPageSize(request.getPageSize());
        req.setNeedSumServiceType(true);

        PageServicePurchaseDetailResponse response = tenantBillWrapper.pageServicePurchaseDetail(req);

        PageInfo pageInfo = response.getPageInfo();
        List<ServicePurchaseDetailDto> purchaseDetails = response.getPurchaseDetails();
        PageResultV2<ServicePurchaseDetailDto> pageResultV2 = new PageResultV2<>(purchaseDetails, request.getPage(), request.getPageSize(),
                pageInfo.getTotalRecords(), pageInfo.getTotalPage());

        return CommonResponse.success(pageResultV2);
    }


    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/service/closePay", method = RequestMethod.POST)
    @Auth
    public CommonResponse<Void> closeBasicServicePay(@RequestBody ClosePayRequest request) {
        Long billId = request.getBillId();
        if (billId == null) {
            return CommonResponse.fail2(ResultCode.PARAM_ERR);
        }

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        CloseServiceBillPayRequest closeServiceBillPayRequest = new CloseServiceBillPayRequest();
        closeServiceBillPayRequest.setBillId(request.getBillId());
        closeServiceBillPayRequest.setTenantId(user.getTenantId());
        closeServiceBillPayRequest.setOperatorId(user.getAccountId());

        AccountInfoVo accountInfoVo = tenantBillWrapper.getCurAccountInfoVo();
        closeServiceBillPayRequest.setOperatorName(accountInfoVo.getAccountName());

        tenantBillWrapper.closeServiceBillPay(closeServiceBillPayRequest);

        return CommonResponse.success(null);
    }


    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/service/payResult", method = RequestMethod.POST)
    @Auth
    public CommonResponse<PayResultVO> payResult(@Valid @RequestBody QueryPayResultRequest request) {
        Long billId = request.getBillId();
        if (billId == null) {
            return CommonResponse.fail2(ResultCode.PARAM_ERR);
        }

        QueryServiceBillDetailRequest req = new QueryServiceBillDetailRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        req.setTenantId(user.getTenantId());
        req.setBillId(billId);
        TenantBillServiceBillDto billServiceBillDto = tenantBillWrapper.queryServiceBillDetail(req);

        String tradeNo = billServiceBillDto.getTradeNo();
        if(StringUtils.isEmpty(tradeNo)) {
            return CommonResponse.fail2(ResultCode.PARAM_ERR);
        }

        return CommonResponse.success(tenantBillWrapper.queryPayResult(user.getTenantId(), tradeNo));
    }


    private TenantBillPageRequest getTenantDailyBillRequest(DailyBillRequest request, Long tenantId, List<String> createUserAccount) {
        TenantBillPageRequest tenantBillPageRequest = new TenantBillPageRequest();
        tenantBillPageRequest.setTenantId(tenantId);
        tenantBillPageRequest.setBillIdList(request.getBillIdList());
        tenantBillPageRequest.setBillDateRange(request.getBillDateRange());
        tenantBillPageRequest.setBillStatusList(request.getBillStatusList());
        tenantBillPageRequest.setPage(request.getPage());
        tenantBillPageRequest.setPageSize(request.getPageSize());
        tenantBillPageRequest.setBillType(request.getBillType());
        tenantBillPageRequest.setCreateUserAccount(createUserAccount);
        return tenantBillPageRequest;
    }


    private List<TenantDailyBillVo> getTenantDailyBillVos(List<TenantDailyBillDto> tenantDailyBillDtos) {
        List<TenantDailyBillVo> tenantDailyBillVos = Fun.map(tenantDailyBillDtos, tenantDailyBillDto -> {
            TenantDailyBillVo tenantDailyBillVo = new TenantDailyBillVo();
            tenantDailyBillVo.setBillId(tenantDailyBillDto.getBillId());
            tenantDailyBillVo.setBillStartDate(tenantDailyBillDto.getBillStartDate());
            tenantDailyBillVo.setBillEndDate(tenantDailyBillDto.getBillEndDate());
            tenantDailyBillVo.setBillType(tenantDailyBillDto.getBillType());
            tenantDailyBillVo.setBillTypeDesc(tenantDailyBillDto.getBillTypeDesc());
            tenantDailyBillVo.setBillAmount(tenantDailyBillDto.getBillAmount());
            tenantDailyBillVo.setBillStatus(tenantDailyBillDto.getBillStatus());
            tenantDailyBillVo.setIsCap(tenantDailyBillDto.getIsCap());
            tenantDailyBillVo.setTotalSales(tenantDailyBillDto.getTotalSales());
            tenantDailyBillVo.setTotalOrderCnt(tenantDailyBillDto.getTotalOrderCnt());
            tenantDailyBillVo.setRuleVersion(tenantDailyBillDto.getRuleVersion());
            return tenantDailyBillVo;
        });
        return tenantDailyBillVos;
    }

    // 为tenantDailyBillVos账单列表 设置部分账单详情
    public void setSegmentBillDetailInfoForBasicServiceBill(List<TenantDailyBillVo> tenantDailyBillVos) {
        if (CollectionUtils.isEmpty(tenantDailyBillVos)) {
            return;
        }
        tenantDailyBillVos.forEach(tenantDailyBillVo -> {
            if (Integer.valueOf(30).equals(tenantDailyBillVo.getBillType()) &&
                    BasicServiceBillStatusEnum.WAITING_CHARGE.getCode().equals(tenantDailyBillVo.getBillStatus())) {

                QueryServiceBillDetailRequest req = new QueryServiceBillDetailRequest();
                User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
                req.setTenantId(user.getTenantId());
                req.setBillId(tenantDailyBillVo.getBillId());
                TenantBillServiceBillDto tenantBillServiceBillDto = tenantBillWrapper.queryServiceBillDetail(req);

                tenantDailyBillVo.setCreateTime(tenantBillServiceBillDto.getCreateTime());
                tenantDailyBillVo.setPayEndTime(tenantBillServiceBillDto.getPayEndTime());
                tenantDailyBillVo.setAdjustBillAmount(tenantBillServiceBillDto.getAdjustBillAmount());
                tenantDailyBillVo.setActualBillAmount(tenantBillServiceBillDto.getActualBillAmount());
                tenantDailyBillVo.setCalcAmount(tenantBillServiceBillDto.getCalcAmount());
            }
        });
    }

}
