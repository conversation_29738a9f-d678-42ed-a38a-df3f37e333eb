package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.*;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfoResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request.EncodeWmPoiIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request.GetChannelPoiCodeByPoiIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request.GetPoiOperationModeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request.QueryByPoiIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.CityWithPoiListVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.EncodeWmPoiIdVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.GetPoiOperationModeResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.IMGoodsConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiGroupVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiSelectVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.RelationPoiInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.WmPoiIdVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonV2Response;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelInfoBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.PoiService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo.PoiInfoBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.Skip32Wrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.poi.PoiResponseConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelStoreDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryChannelStoreInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryChannelStoreInfoResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * 门店组件
 *
 * @Author：<EMAIL>
 * @Date: 2018/9/17 下午7:50
 */
@InterfaceDoc(
        displayName = "门店相关接口",
        type = "restful",
        scenarios = "管理后台通用门店组件, 查询商家门店分类, 根据门店分类查询该类别的门店列表, 根据门店名称查询门店列表",
        description = "管理后台通用门店组件, 查询商家门店分类, 根据门店分类查询该类别的门店列表, 根据门店名称查询门店列表"
)
@Api(value = "门店相关接口")
@RestController
@RequestMapping("/pieapi/poi")
@Slf4j
public class PoiController {

    @Resource
    private ChannelThriftService.Iface channelThriftService;

    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    @Value("${urls.goodsDetailPrifix}")
    private String goodsDetailPrifix;

    @Resource
    private PoiService poiService;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private SacWrapper sacWrapper;

    @MethodDoc(
            displayName = "根据租户门店查询线上门店关联接口(查询门店开通渠道信息)",
            description = "根据租户门店查询线上门店关联接口(查询门店开通渠道信息)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据租户门店查询线上门店关联请求",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见PoiResp",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/poi/queryRelationByPoiId",
            restExamplePostData = "{\"poiId\": \"1000017\"}",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"channelId\":\"string\",\"channelName\":\"string\",\"channelPoiId\":\"string\",\"channelPoiName\":\"string\",\"channelPoiStatus\":\"string\", ,\"syncDataStatus\":1}]},\"msg\":\"string\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @RequestMapping(value = "/queryRelationByPoiId", method = RequestMethod.POST)
    public CommonResponse<List<RelationPoiInfoVO>> queryRelationByPoiId(@RequestBody QueryByPoiIdRequest request) {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            String poiId = request.getStoreId();

            if (StringUtils.isBlank(poiId)) {
                return CommonResponse.fail(300, "输入参数不合法", null);
            }

            QueryChannelStoreInfoRequest queryChannelStoreInfoRequest = new QueryChannelStoreInfoRequest();
            queryChannelStoreInfoRequest.setTenantId(user.getTenantId());
            Long storeId = Long.valueOf(poiId);
            queryChannelStoreInfoRequest.setStoreIds(Arrays.asList(storeId));

            QueryChannelStoreInfoResponse response = channelThriftService.queryChannelStoreInfo(queryChannelStoreInfoRequest);

            if (response != null && response.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(500, "服务端RPC异常", null);
            }

            for (Map.Entry<Long, List<ChannelStoreDTO>> entry : response.getChannelStoreMap().entrySet()) {
                if (Objects.equals(entry.getKey(), storeId)) {
                    List<RelationPoiInfoVO> relationPoiInfoVOS = new ArrayList<>(entry.getValue().size());
                    entry.getValue().forEach(channelStoreDTO -> {
                        RelationPoiInfoVO relationPoiInfoVO = RelationPoiInfoVO.poiInfoConvert(channelStoreDTO);
                        relationPoiInfoVOS.add(relationPoiInfoVO);
                    });

                    List<Integer> channelIds = ConverterUtils.convertList(relationPoiInfoVOS, e -> Integer.valueOf(e.getChannelId()));
                    Map<Integer, ChannelInfoBo> channelMap = Fun.toMapQuietly(tenantWrapper.batchQueryChannelByChannelIds(channelIds), ChannelInfoBo::getChannelId);
                    for (RelationPoiInfoVO relationPoiInfoVO: relationPoiInfoVOS) {
                        ChannelInfoBo channelInfoBo = channelMap.get(Integer.valueOf(relationPoiInfoVO.getChannelId()));
                        if (channelInfoBo == null) {
                            throw new BizException("渠道信息获取失败");
                        }
                        relationPoiInfoVO.setChannelStandard(channelInfoBo.getStandard());
                        relationPoiInfoVO.setChannelName(channelInfoBo.getChannelName());
                    }

                    return CommonResponse.success(relationPoiInfoVOS);
                }
            }

            return CommonResponse.fail(400, "未找到门店信息,请检查请求门店ID正确性", null);
        } catch (Exception e) {
            log.error("查询门店渠道信息失败, request: {}", request, e);
            return CommonResponse.fail(500, "服务端未知异常", null);
        }
    }


    @MethodDoc(
            displayName = "查询租户所在城市门店列表",
            description = "查询租户所在城市门店列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询租户所在城市门店列表",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见PoiResp",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/poi/queryAllCityPoiList",
            restExamplePostData = "{}",
            restExampleResponseData = ""
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @RequestMapping(value = "/queryAllCityPoiList", method = RequestMethod.POST)
    public CommonResponse<List<CityWithPoiListVO>> queryAllCityPoiList() {

        try {

            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            List<PoiInfoBO> poiInfoBOs = poiService.queryPois(user.getTenantId(), null, (String) null);

            if (CollectionUtils.isEmpty(poiInfoBOs)) {
                return CommonResponse.success(new ArrayList<>());
            }

            List<CityWithPoiListVO> cityWithPoiLists = PoiResponseConvertUtils.constructCityPoiList(poiInfoBOs);
            return CommonResponse.success(cityWithPoiLists);
        } catch (Exception e) {
            log.error("查询城市门店失败, request: {}", e);
            return CommonResponse.fail(500, "服务端未知异常", null);
        }
    }



    @MethodDoc(
            displayName = "门店选择列表",
            description = "门店选择列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店选择列表",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见PoiResp",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/poi/select/list",
            restExamplePostData = "{}",
            restExampleResponseData = ""
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @RequestMapping(value = "/select/list", method = RequestMethod.POST)
    public CommonV2Response<List<PoiSelectVo>> poiSelect() {

        try {
            List<PoiSelectVo> poiSelectVos = sacWrapper.poiSelect();
            return CommonV2Response.success(poiSelectVos);
        } catch (Exception e) {
            log.error("查询门店列表异常, request: {}", e);
            return CommonV2Response.fail(500, "服务端未知异常", null);
        }
    }


    @MethodDoc(
            displayName = "通过PoiId查询channnelPoiCode",
            description = "通过PoiId查询channnelPoiCode",
            parameters = {
                    @ParamDoc(name = "request", description = "", example = {})
            },
            returnValueDescription = "详见WmPoiIdVO",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    )
            }
    )
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @RequestMapping(value = "/getChannelPoiCodeByPoiId", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<WmPoiIdVO> getChannelPoiCodeByPoiId(@RequestBody GetChannelPoiCodeByPoiIdRequest request) {
        PoiDetailInfoResponse poiDetailInfoResponse = channelPoiManageThriftService.queryPoiDetailInfoByPoiId(request.getTenantId(), request.getPoiId());
        if (poiDetailInfoResponse != null && StatusCodeEnum.SUCCESS.getCode() != poiDetailInfoResponse.getStatus().getCode()) {
            return CommonResponse.fail(poiDetailInfoResponse.getStatus().getCode(), poiDetailInfoResponse.getStatus().getMessage(), null);
        }
        if (poiDetailInfoResponse.getPoiDetailInfoDTO() == null || CollectionUtils.isEmpty(poiDetailInfoResponse.getPoiDetailInfoDTO().getChannelPoiInfoList())) {
            return CommonResponse.fail(400, "未找到门店信息,请检查请求门店ID正确性", null);
        }
        Optional<ChannelPoiInfoDTO> optional = poiDetailInfoResponse.getPoiDetailInfoDTO().getChannelPoiInfoList().stream().filter(e -> e.getChannelId() == request.getChannelId()).findAny();
        if (!optional.isPresent()) {
            return CommonResponse.fail(400, "未找到门店信息,请检查请求门店ID正确性", null);
        }

        String channelPoiId = optional.get().getChannelInnerPoiId();
        int channelPoiIdIntValue = 0;
        if (StringUtils.isBlank(channelPoiId)) {
            return CommonResponse.fail(500, "未查到对应的门店信息，poiId：" + request.getPoiId(), null);
        }
        if (!NumberUtils.isNumber(channelPoiId.trim())) {
            return CommonResponse.fail(500, "外卖渠道门店编码格式错误，无法处理：" + channelPoiId, null);
        }
        channelPoiIdIntValue = Integer.parseInt(channelPoiId);
        //增加混淆wmPoiId
        long encodeWmPoiId = Skip32Wrapper.encViewIdWithRange((byte) 1, (int) (System.currentTimeMillis() / 1000L) / 100000, channelPoiIdIntValue);

        WmPoiIdVO wmPoiIdVO = new WmPoiIdVO();
        wmPoiIdVO.setWmPoiId(channelPoiId);
        wmPoiIdVO.setEncWmPoiId(String.valueOf(encodeWmPoiId));
        wmPoiIdVO.setGoodsDetailUrlPrifix(this.goodsDetailPrifix);
        return CommonResponse.success(wmPoiIdVO);
    }

    @MethodDoc(
            displayName = "外卖poiId混淆",
            description = "外卖poiId混淆",
            parameters = {
                    @ParamDoc(name = "request", description = "外卖poiId混淆请求", example = {})
            },
            returnValueDescription = "详见EncodeWmPoiIdVO",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    )
            }
    )
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @RequestMapping(value = "/encodeWmPoiId", method = RequestMethod.POST)
    public CommonResponse<EncodeWmPoiIdVO> encodeWmPoiId(@RequestBody EncodeWmPoiIdRequest request) {
        try {
            EncodeWmPoiIdVO encodeWmPoiIdVO = new EncodeWmPoiIdVO();
            long wmPoiId = request.getWmPoiId();
            long encodeWmPoiId = Skip32Wrapper.encViewIdWithRange((byte) 1, (int) (System.currentTimeMillis() / 1000L) / 100000, (int) wmPoiId);
            encodeWmPoiIdVO.setEncWmPoiId(encodeWmPoiId);
            return CommonResponse.success(encodeWmPoiIdVO);
        } catch (Exception e) {
            return CommonResponse.fail(500, e.getMessage());
        }

    }

    @MethodDoc(
            displayName = "获取im商品配置",
            description = "获取im商品配置",
            parameters = {
                    @ParamDoc(name = "request", description = "获取im商品配置",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {})
            },
            returnValueDescription = "详见IMGoodsConfigVO",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    )
            }
    )
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @RequestMapping(value = "/getIMGoodsConfig", method = RequestMethod.POST)
    public CommonResponse<IMGoodsConfigVO> getIMGoodsConfig() {
        try {
            String configStr = MccConfigUtil.getImGoodsConfig();
            IMGoodsConfigVO imGoodsConfigVO = JacksonUtils.fromJson(configStr,
                    IMGoodsConfigVO.class);
            return CommonResponse.success(imGoodsConfigVO);
        } catch (Exception e) {
            log.warn("get im.goods.config error", e);
            IMGoodsConfigVO imGoodsConfigVO = new IMGoodsConfigVO();
            imGoodsConfigVO.setGoodsCntUpperLimit(20);
            return CommonResponse.success(imGoodsConfigVO);
        }

    }

    @MethodDoc(
            displayName = "获取租户下所有的门店分组信息",
            description = "获取租户下所有的门店分组信息",
            returnValueDescription = "",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：无"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/poi/groups",
            restExamplePostData = "  ",
            restExampleResponseData = ""
    )
    @Auth
    @ApiOperation(value = "获取租户下所有的门店分组信息")
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @GetMapping(value = "/groups")
    public CommonResponse<List<PoiGroupVo>> getTenantAllGroups() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<PoiGroupVo> poiGroupList = tenantWrapper.getTenantAllPoiGroups(user.getTenantId());
        return CommonResponse.success(poiGroupList);
    }

    @MethodDoc(
            displayName = "查询门店的类型",
            description = "查询门店的类型",
            returnValueDescription = "",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：无"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/poi/getPoiOperationMode",
            restExamplePostData = "  ",
            restExampleResponseData = ""
    )
    @Auth
    @ApiOperation(value = "查询门店的类型")
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @PostMapping(value = "/getPoiOperationMode")
    public CommonResponse<GetPoiOperationModeResponse> getPoiOperationMode(@RequestBody @Valid GetPoiOperationModeRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        try {
            GetPoiOperationModeResponse response = tenantWrapper.getPoiOperationMode(request, user.getTenantId());
            return CommonResponse.success(response);
        } catch (Exception e) {
            return CommonResponse.fail(500, e.getMessage());
        }
    }

}
