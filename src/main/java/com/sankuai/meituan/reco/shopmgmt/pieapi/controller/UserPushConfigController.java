package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ServiceType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PushConfigRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.UserPushConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.push.PushReceivedRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.UserPushConfigWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@InterfaceDoc(
        displayName = "推送功能",
        type = "restful",
        scenarios = "用于推送pushToken/unionId保存/更新/查询接口，让用户使用对应的推送信息",
        description = "推送功能，可以在用户离线情况下收到应用的相关推送，让用户实时了解信息",
        host = "https://pieapi-empower.meituan.com/"
)
@Api("推送pushToken/unionId保存/更新/查询接口")
@RestController
@RequestMapping("/pieapi/push/config")
public class UserPushConfigController {

    private final static String ANDROID = "android";
    private final static String IOS = "ios";


    @Resource
    private UserPushConfigWrapper userPushConfigWrapper;


    //保存/更新推送配置

    @MethodDoc(
            description = "保存/更新设备推送pushToken/unionId",
            displayName = "保存/更新设备推送pushToken/unionId",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "推送配置请求",
                            type = PushConfigRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "",
            restExamplePostData = "{\n" +
                    "  \"pushToken\": \"dsfsddfds\",\n" +
                    "  \"unionId\": \"12343\"\n" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": null\n" +
                    "}",
            restExampleUrl = "保存/更新设备推送pushToken/unionId",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @PostMapping("/confirm")
    @ApiOperation(value = "保存/更新设备推送pushToken/unionId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "authId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appChannel", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<Void> pushConfigConfirm(@RequestBody @NotNull PushConfigRequest request) {
        if (StringUtils.isBlank(request.getPushToken()) && StringUtils.isBlank(request.getUnionId())) {
            return CommonResponse.fail(-1, "userToken和unionId不能同时为空");
        }
        IdentityInfo info = ApiMethodParamThreadLocal.getIdentityInfo();
        UserPushConfigVO userPushConfigVO = new UserPushConfigVO();
        userPushConfigVO.setUuid(info.getUuid());
        userPushConfigVO.setAppVersion(info.getAppVersion());
        userPushConfigVO.setAppChannel(info.getAppChannel());
        if (StringUtils.isNotBlank(info.getOs()) && info.getOs().equalsIgnoreCase(IOS)) {
            userPushConfigVO.setPlatformType(0);
        } else if (StringUtils.isNotBlank(info.getOs()) && info.getOs().equalsIgnoreCase(ANDROID)) {
            userPushConfigVO.setPlatformType(1);
        }
        if (StringUtils.isNotBlank(request.getPushToken())) {//两者只能有一个不为空
            userPushConfigVO.setPushToken(request.getPushToken());
        } else {
            userPushConfigVO.setUnionId(request.getUnionId());
        }
        ServiceType serviceType = ServiceType.SHU_GUO_PAI;
        userPushConfigWrapper.saveMessageCenterPushToken(userPushConfigVO);
        return userPushConfigWrapper.saveOrUpdatePushParam(userPushConfigVO, serviceType);
    }


    @MethodDoc(
            description = "查询设备pushToken/unionId",
            displayName = "查询设备pushToken/unionId",
            returnValueDescription = "",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": {\n" +
                    "  \"uuid\": \"gfdgfdfs34343s\",\n" +
                    "  \"pushToken\": \"dsfsddfds\",\n" +
                    "  \"unionId\": \"12343\",\n" +
                    "  \"platformType\": 1,\n" +
                    "  \"appVersion\": \"1.0.0\"\n" +
                    "  }\n" +
                    "}",
            restExampleUrl = "/pieapi/push/config/uuid",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @GetMapping("/uuid")
    @ApiOperation(value = "查询设备pushToken/unionId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<UserPushConfigVO> queryPushConfig() {
        return userPushConfigWrapper.queryPushConfigByUuid(ApiMethodParamThreadLocal.getIdentityInfo().getUuid(), ServiceType.SHU_GUO_PAI);
    }

    @MethodDoc(
            description = "设备收到push, 回调消息中心",
            displayName = "设备收到push, 回调消息中心",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "设备收到push回调参数",
                            type = PushReceivedRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"msgId\":12093128951,\"platMsgId\":\"adsasdasd\",\"pushType\":\"NOTIFICATION\",\"serialNum\":2,\"subCode\":2000,\"uuid\":\"uhu2hi1bfsanoi1589\"}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0\n" +
                    "}",
            restExampleUrl = "/pieapi/push/config/pushReceived",
            extensions = {
            @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @PostMapping("/pushReceived")
    @ApiOperation(value = "设备收到push, 回调消息中心")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse pushReceived(@RequestBody PushReceivedRequest request) {
        return userPushConfigWrapper.pushReceived(request.getUuid(),
                request.getPlatMsgId(),
                request.getPushType(),
                request.getMsgId(),
                request.getSerialNum(),
                request.getSubCode());
    }

}
