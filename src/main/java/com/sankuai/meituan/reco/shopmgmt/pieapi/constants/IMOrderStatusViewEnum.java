package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-28 18:41
 * @Description:
 */
@AllArgsConstructor
@Getter
public enum IMOrderStatusViewEnum {
    SUBMIT(1, "已下单"),
    MERCHANT_CONFIRMED(11, "已接单"),
    DELIVERYING(13, "配送中"),
    ARRIVED(15, "已送达"),
    COMPLETED(15, "已完成"),
    CANCELED(25, "已取消");


    private int value;
    private String desc;

    public static IMOrderStatusViewEnum enumOf(int value) {
        for (IMOrderStatusViewEnum imOrderStatusViewEnum : IMOrderStatusViewEnum.values()) {
            if (imOrderStatusViewEnum.getValue() == value) {
                return imOrderStatusViewEnum;
            }
        }
        return null;
    }
}
