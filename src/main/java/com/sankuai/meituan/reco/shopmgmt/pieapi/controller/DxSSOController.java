package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.DxSSOWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

/**
 * <AUTHOR>
 */
@InterfaceDoc(
        displayName = "大象SSO登录",
        type = "restful",
        scenarios = "负责封装登录登出大象接口",
        description = "负责封装登录登出大象接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "负责封装大象SSO信息(登录登出)")
@Controller
@RequestMapping("/pieapi/dx")
public class DxSSOController {

    @Autowired
    private DxSSOWrapper dxSSOWrapper;

    @MethodDoc(
            description = "SSO登录跳转接口",
            displayName = "SSO登录跳转、重定向到Epassport SSO地址",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "SSO登录跳转、重定向到Epassport SSO地址",
                            paramType = ParamType.REQUEST_PARAM,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "SECURITY_PRIVILEGE", description = "SSO登录地址")
            },
            returnValueDescription = "Epassport SSO登录页面",
            restExampleUrl = "/pieapi/dx/sso/auth",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "根据大象回调封装跳转Epassport SSO登录地址"
                    ),
            }
    )
    @ApiOperation(value = "大象登录重定向到Epassport SSO地址")
    @GetMapping(value = "/sso/auth")
    public String ssoAuth(HttpServletRequest request, HttpServletResponse response) {
        return dxSSOWrapper.ssoAuth(request, response);
    }

    @MethodDoc(
            description = "SSO回调业务的接口",
            displayName = "SSO回调业务的接口、重定向到大象",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "sso回调业务的接口、重定向到大象",
                            paramType = ParamType.REQUEST_PARAM,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "SECURITY_PRIVILEGE", description = "SSO回调业务重定向到大象")
            },
            returnValueDescription = "重定向到大象",
            restExampleUrl = "/pieapi/dx/sso/callback",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "SSO登录过后重定向到大象"
                    ),
            }
    )
    @ApiOperation(value = "SSO回调业务的接口、重定向到大象")
    @GetMapping(value = "/sso/callback")
    public String ssoCallback(HttpServletRequest request, HttpServletResponse response, RedirectAttributes attributes) {
        return dxSSOWrapper.ssoCallback(request, response, attributes);
    }

    @MethodDoc(
            description = "登出大象SSO接口",
            displayName = "SSO回调业务的接口、同步清除Epassport SSO信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "SSO回调业务的接口、同步清除Epassport SSO信息",
                            paramType = ParamType.REQUEST_PARAM,
                            requiredness = Requiredness.REQUIRED
                    )
            },
            responseParams = {
                    @ParamDoc(name = "SECURITY_PRIVILEGE", description = "SSO回调业务的接口、同步清除Epassport SSO信息")
            },
            returnValueDescription = "重定向到大象Homepage",
            restExampleUrl = "/pieapi/dx/sso/clear",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "退出SSO、重定向到大象Homepage"
                    ),
            }
    )
    @ApiOperation(value = "退出SSO、重定向到大象Homepage")
    @GetMapping(value = "/sso/clear")
    public String ssoAuthClear(HttpServletRequest request, HttpServletResponse response) {
        return dxSSOWrapper.ssoAuthClear(request, response);
    }
}
