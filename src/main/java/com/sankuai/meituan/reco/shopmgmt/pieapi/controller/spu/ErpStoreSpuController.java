package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import javax.validation.Valid;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpQueryStoreSafeStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpUpdateSkuSafeStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuClippingPageQueryResponseVO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuBatchUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuPageQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuQueryChannelPriceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuSyncStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuBatchUpdateResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuQueryChannelPriceResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpTabCountVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.StoreSkuSafeStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ErpStoreSpuServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;

/**
 * ERP门店商品相关接口
 *
 * <AUTHOR>
 * @since 2023/05/24
 */
@InterfaceDoc(
        displayName = "ERP门店商品相关接口",
        type = "restful",
        scenarios = "ERP门店商品相关接口",
        description = "ERP门店商品相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "ERP门店商品相关接口")
@RestController
@RequestMapping("/pieapi/store/spu/erp")
public class ErpStoreSpuController {

    @Autowired
    private ErpStoreSpuServiceWrapper erpStoreSpuServiceWrapper;

    @MethodDoc(
            displayName = "ERP门店商品分页查询接口",
            description = "ERP门店商品分页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品分页查询请求参数",
                            type = ErpStoreSpuPageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/pageQuery",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pageQuery", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ErpStoreSpuPageQueryResponseVO> pageQuery(@Valid @RequestBody ErpStoreSpuPageQueryRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.erpPageQuery(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP门店商品裁剪结构分页查询接口",
            description = "ERP门店商品裁剪结构分页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品裁剪结构分页查询请求参数",
                            type = ErpStoreSpuPageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/pageQueryForClipping",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pageQueryForClipping", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ErpStoreSpuClippingPageQueryResponseVO> pageQueryForClipping(@Valid @RequestBody ErpStoreSpuPageQueryRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.erpPageQueryForClipping(request);
    }

    @MethodDoc(
            displayName = "ERP tab页数量统计接口",
            description = "ERP tab页数量统计接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品查询请求参数",
                            type = ErpStoreSpuPageQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/tabCount",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/tabCount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ErpTabCountVO> tabCount(@Valid @RequestBody ErpStoreSpuQueryRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.erpTabCount(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP门店商品批量设置接口",
            description = "ERP门店商品批量设置接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品批量设置请求",
                            type = ErpStoreSpuBatchUpdateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/batchUpdateSpu",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchUpdateSpu", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ErpStoreSpuBatchUpdateResponseVO> batchUpdateSpu(@Valid @RequestBody ErpStoreSpuBatchUpdateRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.batchUpdateSpu(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP-同步线下价格",
            description = "ERP-同步线下价格",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品相关属性同步请求参数",
                            type = ErpStoreSpuSyncStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/syncOfflinePrice",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/syncOfflinePrice", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> syncOfflinePrice(@Valid @RequestBody ErpStoreSpuSyncStatusRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.syncOfflinePrice(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP-同步线下库存",
            description = "ERP-同步线下库存",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品相关属性同步请求参数",
                            type = ErpStoreSpuSyncStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/syncOfflineStock",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/syncOfflineStock", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> syncOfflineStock(@Valid @RequestBody ErpStoreSpuSyncStatusRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.syncOfflineStock(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP-同步线上价格",
            description = "ERP-同步线上价格",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品相关属性同步请求参数",
                            type = ErpStoreSpuSyncStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/syncOnlinePrice",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/syncOnlinePrice", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> syncOnlinePrice(@Valid @RequestBody ErpStoreSpuSyncStatusRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.syncOnlinePrice(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP-同步线上库存",
            description = "ERP-同步线上库存",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品相关属性同步请求参数",
                            type = ErpStoreSpuSyncStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/syncOnlineStock",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/syncOnlineStock", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> syncOnlineStock(@Valid @RequestBody ErpStoreSpuSyncStatusRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.syncOnlineStock(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP-同步上下架状态",
            description = "ERP-同步上下架状态",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品相关属性同步请求参数",
                            type = ErpStoreSpuSyncStatusRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/syncSaleStatus",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/syncSaleStatus", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> syncSaleStatus(@Valid @RequestBody ErpStoreSpuSyncStatusRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.syncSaleStatus(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "ERP门店商品查询渠道价格",
            description = "ERP门店商品查询渠道价格",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品渠道价格查询请求参数",
                            type = ErpStoreSpuQueryChannelPriceRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/queryChannelPriceWithAdjustRate",
            returnValueDescription = "渠道商品价格列表",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryChannelPriceWithAdjustRate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ErpStoreSpuQueryChannelPriceResponseVO> queryChannelPriceWithAdjustRate(@Valid @RequestBody ErpStoreSpuQueryChannelPriceRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.queryChannelPriceWithAdjustRate(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }

    @MethodDoc(
            displayName = "查询门店商品安全库存相关值",
            description = "查询门店商品安全库存量+门店安全库存配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店商品安全库存相关值请求参数",
                            type = ErpQueryStoreSafeStockRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/queryOnlineSafeStock",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryOnlineSafeStock", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<StoreSkuSafeStockVO> queryOnlineSafeStock(@Valid @RequestBody ErpQueryStoreSafeStockRequest request) {
        request.validate();
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();

        return erpStoreSpuServiceWrapper.querySafeStock(tenantId, request);
    }


    @MethodDoc(
            displayName = "ERP门店商品修改SKU安全库存接口",
            description = "ERP门店商品修改SKU安全库存接口，若上行安全库存值为null，依然可保存成功",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "ERP门店商品修改SKU安全库存请求",
                            type = ErpUpdateSkuSafeStockRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/store/spu/erp/updateOnlineSafeStock",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updateOnlineSafeStock", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> updateOnlineSafeStock(@Valid @RequestBody ErpUpdateSkuSafeStockRequest request) {
        request.validate();
        return erpStoreSpuServiceWrapper.updateOnlineSafeStock(ApiMethodParamThreadLocal.getIdentityInfo().getUser(), request);
    }
}
