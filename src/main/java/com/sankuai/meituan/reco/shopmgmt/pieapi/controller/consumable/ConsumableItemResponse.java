package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConsumableItemResponse {

    @FieldDoc(description = "耗材明细")
    private List<ConsumableItemDetail> items;
}
