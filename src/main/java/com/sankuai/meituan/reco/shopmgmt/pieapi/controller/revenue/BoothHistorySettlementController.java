package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.revenue;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementDailyPageReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementDailySummaryReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementMonthlyPageReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementMonthlySummaryReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementDailyPageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementDailySummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementMonthlyPageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementMonthlySummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.BoothHistorySettlementServiceFromSettlementWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.BoothHistorySettlementServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 摊位历史账单服务
 *
 * <AUTHOR>
 * @since 2019/06/17
 */
@InterfaceDoc(
        displayName = "摊位历史账单服务",
        type = "restful",
        scenarios = "摊位历史账单服务",
        description = "查看中台订单摊位历史账单",
        authors = {
                "shihuifeng"
        }
)
@Api(value = "摊位历史账单服务")
@Slf4j
@RestController
@RequestMapping("/order/settlement/history/booth")
public class BoothHistorySettlementController {

    @Resource
    BoothHistorySettlementServiceWrapper boothHistorySettlementServiceWrapper;

    @Autowired
    BoothHistorySettlementServiceFromSettlementWrapper boothHistorySettlementServiceFromSettlementWrapper;

    @MethodDoc(
            description = "按日查询摊位历史账单——汇总信息",
            displayName = "按日查询摊位历史账单——汇总信息",
            restExampleResponseData = "",
            restExampleUrl = "/order/settlement/history/booth/dailySummary",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "按日查询摊位历史账单"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "按日查询摊位历史账单——汇总信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/dailySummary", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<BoothHistorySettlementDailySummaryVO> dailySummary(@RequestBody @Valid BoothHistorySettlementDailySummaryReq req) {
        if (MccConfigUtil.isBoothRevenueFromSettlement(req.getStoreId())) {
            return boothHistorySettlementServiceFromSettlementWrapper.dailySummary(req);
        }
        else {
            return boothHistorySettlementServiceWrapper.dailySummary(req);
        }
    }

    @MethodDoc(
            description = "按日查询摊位历史账单——分页查询",
            displayName = "按日查询摊位历史账单——分页查询",
            restExampleResponseData = "",
            restExampleUrl = "/order/settlement/history/booth/dailyList",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "按日查询摊位历史账单"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "按日查询摊位历史账单——分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/dailyList", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<BoothHistorySettlementDailyPageVO> dailyList(@RequestBody @Valid BoothHistorySettlementDailyPageReq req) {
        if (MccConfigUtil.isBoothRevenueFromSettlement(req.getStoreId())) {
            return boothHistorySettlementServiceFromSettlementWrapper.dailyList(req);
        }
        else {
            return boothHistorySettlementServiceWrapper.dailyList(req);
        }
    }

    @MethodDoc(
            description = "按月查询摊位历史账单——汇总信息",
            displayName = "按月查询摊位历史账单——汇总信息",
            restExampleResponseData = "",
            restExampleUrl = "/order/settlement/history/booth/monthlySummary",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "按日查询摊位历史账单"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "按日查询摊位历史账单——汇总信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/monthlySummary", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<BoothHistorySettlementMonthlySummaryVO> monthlySummary(@RequestBody @Valid BoothHistorySettlementMonthlySummaryReq req) {
        if (MccConfigUtil.isBoothRevenueFromSettlement(req.getStoreId())) {
            return boothHistorySettlementServiceFromSettlementWrapper.monthlySummary(req);
        }
        else {
            return boothHistorySettlementServiceWrapper.monthlySummary(req);
        }
    }


    @MethodDoc(
            description = "按月查询摊位历史账单——分页查询",
            displayName = "按月查询摊位历史账单——分页查询",
            restExampleResponseData = "",
            restExampleUrl = "/order/settlement/history/booth/monthlyList",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "按日查询摊位历史账单"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "按日查询摊位历史账单——分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/monthlyList", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<BoothHistorySettlementMonthlyPageVO> monthlyList(@RequestBody @Valid BoothHistorySettlementMonthlyPageReq req) {
        if (MccConfigUtil.isBoothRevenueFromSettlement(req.getStoreId())) {
            return boothHistorySettlementServiceFromSettlementWrapper.monthlyList(req);
        }
        else {
            return boothHistorySettlementServiceWrapper.monthlyList(req);
        }
    }


}
