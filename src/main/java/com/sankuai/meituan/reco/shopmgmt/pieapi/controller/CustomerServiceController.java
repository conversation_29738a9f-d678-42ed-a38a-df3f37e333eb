package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacAccountClient;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2020-12-09
 */
@InterfaceDoc(
        displayName = "客服服务",
        type = "restful",
        scenarios = "客服服务相关接口",
        description = "客服服务相关接口",
        authors = {
                "wangjian"
        },
        version = "V1.0"
)
@Slf4j
@RestController
@RequestMapping("/pieapi/customer")
public class CustomerServiceController {

    @Autowired
    private SacAccountClient sacAccountClient;

    @MethodDoc(
            displayName = "查询咨询链接",
            description = "查询咨询链接",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "",
            restExamplePostData = "",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{}}"
    )
    @ApiOperation(value = "查询咨询链接")
    @Auth
    @GetMapping(value = "/counselUrl")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    public CommonResponse<String> queryCounselUrl(String type) {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        String counselUrl = sacAccountClient.getCounselUrl(sessionInfo.getAccountId(), sessionInfo.getTenantId(), type);
        return CommonResponse.success(counselUrl);

    }
}
