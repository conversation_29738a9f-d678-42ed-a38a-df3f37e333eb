package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;

import com.sankuai.meituan.reco.pickselect.ebase.thrift.constants.PickingGroupModeEnum;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.constants.PrepareMode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ResvChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.PickSelectConfigUpdateReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QuerySkusForPickAndOutwardSourcingRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickHomePageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickSelectConfigResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QuerySkusForPickAndOutwardSourcingResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PickWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@InterfaceDoc(
        displayName = "拣货服务",
        type = "restful",
        scenarios = "拣货服务",
        description = "拣货服务",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "拣货服务")
@RestController
@RequestMapping("/pieapi/pick")
public class PickController {

    @Resource
    private PickWrapper pickWrapper;

    @MethodDoc(
            displayName = "拣货首页聚合数据",
            description = "拣货首页聚合数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "拣货首页聚合数据",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/pick/homepage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "拣货首页聚合数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/homepage", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<PickHomePageResponse> homepage() {
        return pickWrapper.homepage();
    }

    @MethodDoc(
            displayName = "缺货配置查询服务",
            description = "缺货配置查询服务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "拣货首页聚合数据",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/operate/config/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "缺货配置查询服务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pickselectconfig/query", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<PickSelectConfigResponse> queryActionAfterLackConfig() {

        return CommonResponse.success(pickWrapper.queryActionAfterLackConfig());
    }


    @MethodDoc(
            displayName = "缺货配置管理服务",
            description = "缺货配置管理服务",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "拣货首页聚合数据",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/operate/config/update",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "缺货配置管理服务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/pickselectconfig/save", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> updateActionAfterLackConfig(@RequestBody PickSelectConfigUpdateReq req) {
        checkUpdateFulfillConfigReq(req);
        if (Objects.nonNull(req.getAutoPickDoneConfig())){
            pickWrapper.updateAutoPickDoneConfig(req.getAutoPickDoneConfig());
        }
        if (Objects.nonNull(req.getActionAfterLackConfig())){
            pickWrapper.updateActionAfterLackConfig(req.getActionAfterLackConfig());
        }
        if (checkUpdateAutoPickDoneConfigInfos(req)){
            pickWrapper.updateAutoPickDoneConfigInfos(req);
        }
        return CommonResponse.success(null);
    }

    @MethodDoc(
            displayName = "查询待拣货及外采商品列表",
            description = "查询待拣货及外采商品列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询待外采商品列表请求",
                            type = QuerySkusForPickAndOutwardSourcingRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "",
            restExampleUrl = "/pieapi/pick/querySkusForPickAndOutwardSourcing",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\"}"
    )
    @ApiOperation(value = "查询待拣货及外采商品列表")
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySkusForPickAndOutwardSourcing", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QuerySkusForPickAndOutwardSourcingResponseVO> querySkusForPickAndOutwardSourcing(@Valid @RequestBody QuerySkusForPickAndOutwardSourcingRequest request) {
        return CommonResponse.success(pickWrapper.querySkusForPickAndOutwardSourcing(request));
    }

    private void checkUpdateFulfillConfigReq(PickSelectConfigUpdateReq request) throws CommonLogicException{

        if (Objects.isNull(request.getAutoPickDoneConfig()) && Objects.isNull(request.getActionAfterLackConfig())){
            throw new CommonLogicException("未传递参数", ResultCode.CHECK_PARAM_ERR);
        }
        if (Objects.nonNull(request.getAutoPickDoneConfig())){
            int autoPickDoneTime = request.getAutoPickDoneConfig().getAutoPickDoneTime();
            if (autoPickDoneTime > 360 || autoPickDoneTime < 1){
                throw new CommonLogicException("立即单门店自动拣货完成时间不正确!", ResultCode.CHECK_PARAM_ERR);
            }
        }
        List<ChannelAutoPickDoneConfig> channelAutoPickDoneConfigs = request.getChannelAutoPickDoneConfigList();
        if (CollectionUtils.isNotEmpty(channelAutoPickDoneConfigs)){
            boolean check = channelAutoPickDoneConfigs.stream().anyMatch(channelAutoPickDoneConfig -> channelAutoPickDoneConfig.getAutoPickDoneTime() > 360 || channelAutoPickDoneConfig.getAutoPickDoneTime() < 1);
            if (check){
                throw new CommonLogicException("立即单渠道自动拣货完成时间不正确!", ResultCode.CHECK_PARAM_ERR);
            }
        }
        if (Objects.nonNull(request.getResvAutoPickDoneConfig())){
            int autoPickDoneTime = request.getResvAutoPickDoneConfig().getResvAutoPickDoneTime();
            if (autoPickDoneTime > 360 || autoPickDoneTime < 1){
                throw new CommonLogicException("预约单自动拣货完成时间不正确!", ResultCode.CHECK_PARAM_ERR);
            }
        }
        List<ResvChannelAutoPickDoneConfig> resvChannelAutoPickDoneConfigList = request.getResvChannelAutoPickDoneConfigList();
        if (CollectionUtils.isNotEmpty(resvChannelAutoPickDoneConfigList)){
            boolean check = resvChannelAutoPickDoneConfigList.stream().anyMatch(resvChannelAutoPickDoneConfig -> resvChannelAutoPickDoneConfig.getResvAutoPickDoneTime() > 360 || resvChannelAutoPickDoneConfig.getResvAutoPickDoneTime() < 1);
            if (check){
                throw new CommonLogicException("预约单渠道自动拣货完成时间不正确!", ResultCode.CHECK_PARAM_ERR);
            }
        }


    }
    private boolean checkUpdateAutoPickDoneConfigInfos(PickSelectConfigUpdateReq req) throws CommonLogicException {

        if (com.dianping.lion.client.util.CollectionUtils.isNotEmpty(req.getChannelAutoPickDoneConfigList())) {
            return true;
        }
        if (Objects.nonNull(req.getResvAutoPickDoneConfig())) {
            return true;
        }
        if (com.dianping.lion.client.util.CollectionUtils.isNotEmpty((req.getResvChannelAutoPickDoneConfigList()))) {
            return true;
        }
        if (Objects.nonNull(req.getAutoPickDoneSwitch()) || Objects.nonNull(req.getResvAutoPickDoneSwitch())) {
            return true;
        }
        if (Objects.nonNull(req.getAutoPickDoneType()) || Objects.nonNull(req.getResvAutoPickDoneType())) {
            return true;
        }
        return false;
    }

}
