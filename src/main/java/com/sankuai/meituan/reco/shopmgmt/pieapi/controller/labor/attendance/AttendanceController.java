package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.labor.attendance;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.ScheduledEmployeeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceStatisticsVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryCheckinInfoResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.AttendanceServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/10/19 14:47
 **/
@InterfaceDoc(
        displayName = "考勤相关接口",
        type = "restful",
        scenarios = "考勤相关接口",
        description = "考勤相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "考勤相关接口")
@RestController
@RequestMapping("/pieapi/labor/management/attendance")
public class AttendanceController {
    @Resource
    private AttendanceServiceWrapper attendanceServiceWrapper;

    @MethodDoc(
            displayName = "进入打卡页面-获取打卡信息",
            description = "进入打卡页面-获取打卡信息",
            parameters = {


            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/preCheckin",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "进入打卡页面-获取打卡信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/preCheckin", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryCheckinInfoResp> queryCheckinInfo() {
        return CommonResponse.success(attendanceServiceWrapper.queryCheckinInfo());
    }

    @MethodDoc(
            displayName = "考勤打卡",
            description = "考勤打卡",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "考勤打卡",
                            type = CheckinRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/checkin",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "考勤打卡")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/checkin", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> checkin(@RequestBody CheckinRequest request) {
        request.valid();
        attendanceServiceWrapper.checkin(request);
        return CommonResponse.success(null);
    }

    @MethodDoc(
            displayName = "考勤打卡(拍照方式)",
            description = "考勤打卡(拍照方式)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "考勤打卡",
                            type = CheckinRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/checkinWithPhoto",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "考勤打卡")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/checkinWithPhoto", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<String> checkinWithPhoto(@RequestBody CheckinWithPhotoRequest request) {
        Optional<String> validateResult = request.selfValid();
        if (validateResult.isPresent()) {
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), validateResult.get());
        }
        return attendanceServiceWrapper.checkinWithPhoto(request);
    }

    @MethodDoc(
            displayName = "查看考勤统计",
            description = "查看考勤统计",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查看考勤统计",
                            type = QueryAttendanceStatisticsRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/statistics",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查看考勤统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/statistics", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<List<AttendanceStatisticsVO>> queryStatistics(@RequestBody QueryAttendanceStatisticsRequest request) {
        Optional<String> validateResult = request.valid();
        if (validateResult.isPresent()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), validateResult.get());
        }
        List<AttendanceStatisticsVO> attendanceStatisticsVOS = attendanceServiceWrapper.queryAttendanceStatistics(request);
        return CommonResponse.success(attendanceStatisticsVOS);
    }


    @MethodDoc(
            displayName = "查询门店当前有排班的人员名单",
            description = "查询门店当前有排班的人员名单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店当前有排班的人员名单",
                            type = QueryStoreScheduledEmployeeListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/attendance/queryStoreScheduledEmployeeList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询门店当前有排班的人员名单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStoreScheduledEmployeeList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<List<ScheduledEmployeeVO>> queryStoreScheduledEmployeeList(@RequestBody QueryStoreScheduledEmployeeListRequest request) throws TException {
        List<ScheduledEmployeeVO> scheduledEmployeeVOS = attendanceServiceWrapper.queryStoreScheduledEmployeeList(request.getStoreId());
        return CommonResponse.success(scheduledEmployeeVOS);
    }
}
