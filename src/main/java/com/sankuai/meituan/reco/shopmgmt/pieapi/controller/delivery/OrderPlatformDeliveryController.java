package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.delivery;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.TenantOrderValidator;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryOperateItemRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.AuditExceptionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.CallAgainRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.CancelRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.RaiseTipRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.ReportExceptionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryOperateItemResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.OrderPlatformDeliveryApplicationService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;

/**
 * OrderPlatformDeliveryController
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Slf4j
@Auth
@RestController
@RequestMapping("/pieapi/delivery/orderPlatform")
public class OrderPlatformDeliveryController {

    @Autowired
    private OrderPlatformDeliveryApplicationService orderPlatformDeliveryApplicationService;

    @Autowired
    private TmsServiceWrapper tmsServiceWrapper;

    /**
     * 取消配送
     */
    @MethodDoc(
            displayName = "取消配送",
            restExampleUrl = "/pieapi/delivery/orderPlatform/cancel",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "empowerOrderId", type = ParamDataType.CUSTOM,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/cancel")
    public CommonResponse<Void> cancel(@RequestBody CancelRequest req) {
        orderPlatformDeliveryApplicationService.cancel(req);
        return CommonResponse.success(null);
    }

    /**
     * 加小费
     */
    @MethodDoc(
            displayName = "加小费",
            restExampleUrl = "/pieapi/delivery/orderPlatform/raiseTip",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "empowerOrderId", type = ParamDataType.CUSTOM,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/raiseTip")
    public CommonResponse<Void> raiseTip(@RequestBody RaiseTipRequest req) {
        orderPlatformDeliveryApplicationService.raiseTip(req);
        return CommonResponse.success(null);
    }

    /**
     * 上报异常
     */
    @MethodDoc(
            displayName = "上报异常",
            restExampleUrl = "/pieapi/delivery/orderPlatform/exception/report",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "empowerOrderId", type = ParamDataType.CUSTOM,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/exception/report")
    public CommonResponse<Void> reportException(@RequestBody ReportExceptionRequest req) {
        orderPlatformDeliveryApplicationService.reportException(req);
        return CommonResponse.success(null);
    }

    /**
     * 审核异常
     */
    @MethodDoc(
            displayName = "审核异常",
            restExampleUrl = "/pieapi/delivery/orderPlatform/exception/audit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "empowerOrderId", type = ParamDataType.CUSTOM,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/exception/audit")
    public CommonResponse<Void> auditException(@RequestBody AuditExceptionRequest req) {
        orderPlatformDeliveryApplicationService.auditException(req);
        return CommonResponse.success(null);
    }

    /**
     * 再次呼叫
     */
    @MethodDoc(
            displayName = "再次呼叫",
            restExampleUrl = "/pieapi/delivery/orderPlatform/callAgain",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "empowerOrderId", type = ParamDataType.CUSTOM,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/callAgain")
    public CommonResponse<Void> callAgain(@RequestBody CallAgainRequest req) {
        orderPlatformDeliveryApplicationService.callAgain(req);
        return CommonResponse.success(null);
    }

    /**
     * 再次呼叫
     */
    @MethodDoc(
            displayName = "配送操作按钮",
            restExampleUrl = "/pieapi/delivery/orderPlatform/deliveryOperateItem",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "empowerOrderId", type = ParamDataType.CUSTOM,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/deliveryOperateItem")
    public CommonResponse<DeliveryOperateItemResponse> deliveryOperateItem(
            @RequestBody DeliveryOperateItemRequest req) {
        DeliveryOperateItemResponse response = new DeliveryOperateItemResponse();
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            Map<Long, DeliveryOperateItem> operateItemMap = tmsServiceWrapper.queryDeliveryOperateItem(
                    identityInfo.getTenantId(), identityInfo.getStoreId(), Arrays.asList(req.getEmpowerOrderId()));
            if (MapUtils.isNotEmpty(operateItemMap) && operateItemMap.containsKey(
                    req.getEmpowerOrderId()) && operateItemMap.get(req.getEmpowerOrderId()) != null) {
                response.setDeliveryOperateCode(operateItemMap.get(req.getEmpowerOrderId()).getOperateItemList());
            }

        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(),
                    ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
        }
        return CommonResponse.success(response);
    }

}
