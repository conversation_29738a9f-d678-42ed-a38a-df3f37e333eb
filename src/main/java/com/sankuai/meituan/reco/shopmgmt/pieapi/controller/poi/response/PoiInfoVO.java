package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 7:57 下午
 * Description
 */
@TypeDoc(description = "租户门店基本信息")
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class PoiInfoVO {


    @FieldDoc(
            description = "运营模式（如加盟/自营） 1：自营 2：加盟"
    )
    @ApiModelProperty(value = "运营模式")
    public Integer operationMode;
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long poiId;
    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;
    @FieldDoc(
            description = "门店地址"
    )
    @ApiModelProperty(value = "门店地址", required = true)
    private String address;
    @FieldDoc(
            description = "门店状态"
    )
    @ApiModelProperty(value = "门店状态", required = true)
    private String status;
    @FieldDoc(
            description = "门店状态编码"
    )
    @ApiModelProperty(value = "门店状态编码")
    private String statusCode;
    /**
     * 城市名称
     */
    private String cityName;
    private String areaCode;
    @FieldDoc(
            description = "外部门店编号"
    )
    @ApiModelProperty(value = "外部门店编号", required = true)
    private String outPoiId;
    private Integer entityType;
    private Integer shippingMode;
}
