package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.labor.point;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.AttendanceApprovalCreateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryApprovalListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryPointsDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryApprovalListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryPointsDetailResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryStarResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.LaborPointServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023-03-15
 * @email <EMAIL>
 */
@InterfaceDoc(
        displayName = "员工招聘审批服务",
        type = "restful",
        scenarios = "用于员工招聘服务创建、查询、修改等",
        description = "用于员工招聘服务创建、查询、修改等"
)
@RestController
@RequestMapping(value = "/pieapi/labor/management/point")
@Slf4j
public class LaborPointController {

    @Resource
    private LaborPointServiceWrapper laborPointServiceWrapper;

    @MethodDoc(
            displayName = "查询星级",
            description = "查询星级",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询星级",
                            type = AttendanceApprovalCreateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/schedule/point/queryStar",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询星级")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStar", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryStarResponse> queryStar() {
        return laborPointServiceWrapper.queryStar();
    }

    @MethodDoc(
            displayName = "查询指定时间的积分明细",
            description = "查询指定时间的积分明细",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询指定时间的积分明细",
                            type = AttendanceApprovalCreateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/labor/management/schedule/point/queryPointsDetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询指定时间的积分明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryPointsDetail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryPointsDetailResponse> queryPointsDetail(@RequestBody QueryPointsDetailRequest request) {
        return laborPointServiceWrapper.queryPointsDetail(request);
    }

}
