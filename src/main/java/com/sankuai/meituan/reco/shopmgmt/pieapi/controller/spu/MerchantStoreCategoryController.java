package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryMerchantStoreCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.MerchantStoreCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.QueryAllMerchantStoreCategoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.MerchantStoreCategoryWrapper;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */

@InterfaceDoc(
        displayName = "商品池店内分类管理",
        type = "restful",
        scenarios = "商品池店内分类管理",
        description = "商品池店内分类管理"
)
@Api(value = "商品池店内分类管理")
@Slf4j
@RestController
@RequestMapping("/pieapi/merchant/storeCategory")
public class MerchantStoreCategoryController {

    @Resource
    private MerchantStoreCategoryWrapper merchantStoreCategoryWrapper;

    @MethodDoc(
            displayName = "查询商品池店内分类",
            description = "查询商品池店内分类",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/merchant/storeCategory/queryAll",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "true"
                    )
            }
    )
    @ApiOperation("查询商品池店内分类")
    @Auth
    @PostMapping("/queryAll")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<QueryAllMerchantStoreCategoryResponse> queryAll() {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            List<MerchantStoreCategoryVO> categoryVOList = merchantStoreCategoryWrapper.queryAll(user.getTenantId());
            return CommonResponse.success(new QueryAllMerchantStoreCategoryResponse(categoryVOList));
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询商品池店内分类",
            description = "查询商品池店内分类",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/merchant/storeCategory/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "true"
                    )
            }
    )
    @ApiOperation("查询商品池店内分类")
    @Auth
    @PostMapping("/query")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<QueryAllMerchantStoreCategoryResponse> query(@RequestBody QueryMerchantStoreCategoryRequest request) {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            List<MerchantStoreCategoryVO> categoryVOList = merchantStoreCategoryWrapper.query(user.getTenantId(), request
                    .getStoreGroupId());
            return CommonResponse.success(new QueryAllMerchantStoreCategoryResponse(categoryVOList));
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

}
