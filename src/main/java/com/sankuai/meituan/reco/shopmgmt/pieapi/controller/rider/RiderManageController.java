package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider;

import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage.RiderChangeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.RiderOperateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage.QueryTransferableRiderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.manage.SelfRiderInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderManageWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 骑手管理 Controller.
 *
 * <AUTHOR>
 * @since 2021/7/28 16:30
 */

@InterfaceDoc(
        displayName = "骑手管理相关接口",
        type = "restful",
        scenarios = "包含查询可用骑手等接口",
        description = "包含查询可用骑手等接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "骑手管理相关接口")
@RestController
@RequestMapping("/pieapi/rider/manage")
public class RiderManageController {

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private RiderManageWrapper riderManageWrapper;

    @MethodDoc(
            displayName = "根据自营骑手的姓名或电话查询转单的目标骑手列表",
            description = "根据自营骑手的姓名或电话查询转单的目标骑手列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据自营骑手的姓名或电话查询转单的目标骑手",
                            type = QueryTransferableRiderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/manage/queryTransferableRider",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "根据自营骑手的姓名或电话查询转单的目标骑手列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTransferableRider", method = {RequestMethod.POST})
    public CommonResponse<List<SelfRiderInfo>> queryTransferableRider(@Valid @RequestBody QueryTransferableRiderRequest request) {
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), validateResult);
        }
        try {
            List<SelfRiderInfo> selfRiders = riderManageWrapper.queryTransferableRiderBySearchKey(request.getStoreId(),
                    request.getRiderSearchKey(), request.getCurrentRiderPhone());
            return CommonResponse.success(selfRiders);
        } catch (CommonRuntimeException e) {
            log.warn("RiderManageController.queryTransferableRider fail. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderManageController.queryTransferableRider error. request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    @MethodDoc(
            displayName = "骑手改派（商家自营配送）",
            description = "骑手改派（商家自营配送）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/manage/rider-change",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "骑手改派（商家自营配送）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @PostMapping(value = "/rider-change")
    public CommonResponse<Void> riderChange(@Valid @RequestBody RiderChangeRequest request) throws TException {
        Optional<TenantWrapper.EmployeeBaseInfo> optEmployeeBaseInfo = tenantWrapper.queryEmployeeInfo(request.getRiderAccountId());
        if (!optEmployeeBaseInfo.isPresent()) {
            return CommonResponse.fail(ResultCode.EBASE_EMPLOYEE_NOT_EXIST);
        }
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return riderManageWrapper.riderChange(request, currentUser, optEmployeeBaseInfo.get());
    }


}
