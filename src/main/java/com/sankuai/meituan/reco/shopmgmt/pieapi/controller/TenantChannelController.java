package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import java.util.*;

import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.tenant.config.TenantChannelConfigKey;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelInfoBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenantchannel.BatchQueryChannelRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenantchannel.QueryChainProductRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenantchannel.ChannelDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.ChannelUserRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.ChannelVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.UserRetrieveWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-02-23
 */

@InterfaceDoc(
        displayName = "商户相关接口",
        type = "restful",
        scenarios = "渠道相关服务，主要提供跟渠道维度相关接口查询",
        description = "商户相关服务，主要提供跟渠道维度相关接口查询",
        host = "https://pieapi-empower.meituan.com/"
)
@Api(value = "渠道相关查询接口")
@RestController
@RequestMapping("/pieapi/tenant/channels")
public class TenantChannelController {

    @Autowired
    private TenantWrapper tenantWrapper;

    @Resource
    private UserRetrieveWrapper userRetrieveWrapper;

    @Autowired
    private OCMSServiceWrapper ocmsServiceWrapper;

    @MethodDoc(
            description = "获取商家开通的渠道列表",
            displayName = "获取商家开通的渠道列表",
            returnValueDescription = "成功",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"\",\n" +
                    "    \"data\": {\n" +
                    "        \"list\": [\n" +
                    "            {\n" +
                    "                \"channelId\": \"100\",\n" +
                    "                \"channelName\": \"美团外卖\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"channelId\": \"200\",\n" +
                    "                \"channelName\": \"饿了么\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"channelId\": \"300\",\n" +
                    "                \"channelName\": \"京东到家\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"channelId\": \"400\",\n" +
                    "                \"channelName\": \"美团医药\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"channelId\": \"500\",\n" +
                    "                \"channelName\": \"有赞\"\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"channelId\": \"700\",\n" +
                    "                \"channelName\": \"微商城\"\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/channels",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "authId", required = true, paramType = "header", dataType = "string")
    })
    @ApiOperation(value = "获取商家开通的渠道列表")
    @GetMapping
    public CommonResponse<List<ChannelVO>> channels() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<ChannelTypeEnumBo> channels = tenantWrapper.queryChannelIds(identityInfo.getUser().getTenantId());
        return CommonResponse.success(ConverterUtils.convertList(channels
                , c -> new ChannelVO(Integer.valueOf(c.getChannelId()), c.getChannelName())));
    }

    @MethodDoc(
            description = "批量获取渠道接口",
            displayName = "批量获取渠道接口",
            returnValueDescription = "成功",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"\",\n" +
                    "    \"data\": {\n" +
                    "        \"list\": [\n" +
                    "            {\n" +
                    "                \"channelId\": \"700\",\n" +
                    "                \"channelName\": \"微商城\"\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/channels/batchQuery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "authId", required = true, paramType = "header", dataType = "string")
    })
    @ApiOperation(value = "批量获取渠道接口")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchQuery", method = RequestMethod.POST)
    public CommonResponse<List<ChannelDetailVO>> batchQueryChannels(@RequestBody BatchQueryChannelRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<ChannelTypeEnumBo> openedChannels = tenantWrapper.queryChannelIds(identityInfo.getUser().getTenantId());
        Map<Integer, ChannelTypeEnumBo> openedChannelMap = Fun.toMapQuietly(openedChannels, ChannelTypeEnumBo::getChannelId);

        // 如果不传，则默认传启用
        if (CollectionUtils.isEmpty(request.getStatusList())) {
            request.setStatusList(Collections.singletonList(YesNoEnum.YES.getKey()));
        }

        List<ChannelInfoBo> channels = tenantWrapper.queryAllChannelInfo(request.getChannelIds(), request.getOrderBizTypes(), request.getStatusList(), request.getStandardList());

        Map<Integer, String> channelIdAliasMap = getChannelIdAliasMap(channels);
        List<ChannelDetailVO> detailVOS = channels.stream().map(c -> {
            ChannelDetailVO detailVO = toChannelDetailVO(c);
            detailVO.setIsOpenApp(openedChannelMap.containsKey(c.getChannelId()));

            String alias = ConfigItemEnum.CHANNEL_ALIAS.getMainConfigValueAsStr(channelIdAliasMap.get(c.getChannelId()));
            String channelName = c.getChannelName();
            detailVO.setChannelAlias(alias);
            detailVO.setChannelFullName(channelName);
            detailVO.setChannelName(StringUtils.isEmpty(alias) ? channelName : alias);
            return detailVO;
        }).collect(Collectors.toList());
        return CommonResponse.success(detailVOS);
    }


    // 查询渠道配置
    public Map<Integer, String> getChannelIdAliasMap(List<ChannelInfoBo> channels) {
        if(CollectionUtils.isEmpty(channels)){
            return Maps.newHashMap();
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long tenantId = identityInfo.getUser().getTenantId();
        List<TenantChannelConfigKey> tenantChannelConfigKeys = channels.stream().map(ChannelInfoBo::getChannelId).distinct().map(channelId -> {
            TenantChannelConfigKey key = new TenantChannelConfigKey();
            key.setChannelId(channelId);
            key.setTenantId(tenantId);
            key.setSubjectId(tenantId);
            key.setConfigId(ConfigItemEnum.CHANNEL_ALIAS.getConfigId());
            return key;
        }).collect(Collectors.toList());
        return tenantWrapper.batchQueryTenantChannelConfigByKey(tenantChannelConfigKeys).stream().collect(Collectors.toMap(ChannelConfigDto::getChannelId, ChannelConfigDto::getConfigContent));
    }

    private ChannelDetailVO toChannelDetailVO(ChannelInfoBo bo) {
        ChannelDetailVO detailVO = new ChannelDetailVO();
        detailVO.setChannelId(bo.getChannelId());
        detailVO.setChannelName(bo.getChannelName());
        detailVO.setStandard(bo.getStandard());
        detailVO.setChannelCode(bo.getChannelCode());
        detailVO.setLogo(bo.getLogo());
        detailVO.setColor(bo.getColor());
        detailVO.setStatus(bo.getStatus());
        detailVO.setOrderBizType(bo.getOrderBizType());
        detailVO.setChannelAbbr(bo.getChannelAbbr());
        detailVO.setChannelPrintLogo(bo.getChannelPrintLogo());
        detailVO.setGreyLogo(bo.getGreyLogo());
        return detailVO;
    }

    @MethodDoc(
            displayName = "查询渠道用户名",
            description = "根据渠道id或订单来源和用户id，查询渠道用户名",
            parameters = {
                    @ParamDoc(name = "channelUserRequest", description = "渠道用户查询请求", example = {})
            },
            returnValueDescription = "渠道用户名",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    )
            }
    )
    @ApiOperation(value = "查询渠道用户名")
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @RequestMapping(value = "/getChannelUserName", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<String> getChannelUserName(@RequestBody @Valid ChannelUserRequest channelUserRequest) {
        if (Objects.isNull(channelUserRequest.getChannelId())
                && Objects.isNull(channelUserRequest.getOrderBizType())) {
            return CommonResponse.success(null);
        }
        Integer channelId = Objects.isNull(channelUserRequest.getChannelId())
                ? ChannelOrderConvertUtils.sourceBiz2Mid(channelUserRequest.getOrderBizType())
                : channelUserRequest.getChannelId();
        if (!Objects.equals(ChannelTypeEnum.MEITUAN.getChannelId(), channelId)
                && !Objects.equals(ChannelTypeEnum.MT_DRUNK_HORSE.getChannelId(), channelId)) {
            return CommonResponse.success(null);
        }
        return CommonResponse.success(userRetrieveWrapper.getUserNameById(channelUserRequest.getUserId()));
    }

    @MethodDoc(
            description = "查询是否支持连锁产品库配置",
            displayName = "查询是否支持连锁产品库配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询是否支持连锁产品库配置请求",
                            type = QueryChainProductRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/channels/isChainProductSupported",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/isChainProductSupported", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<Boolean> isChainProductSupported(@RequestBody QueryChainProductRequest request) {
        request.validate();

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        return ocmsServiceWrapper.isChainProductSupported(tenantId, request.getStoreId(), request.getChannelId());
    }

}
