package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategorySmartSortUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategorySpuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategorySpuSortRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreCategorySmartSortQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.spurelation.StoreCategorySpuResponseVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PoiStoreCategoryWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.PoiStoreCategorySpuListResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.dto.MerchantStoreCategoryDTO;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@InterfaceDoc(
        displayName = "牵牛花门店店内分类spu管理",
        type = "restful",
        scenarios = "牵牛花门店店内分类spu管理",
        description = "牵牛花门店店内分类spu管理"
)
@Api(value = "牵牛花门店店内分类spu管理")
@Slf4j
@RestController
@RequestMapping("/pieapi/poi/storeCategorySpu")
public class PoiStoreCategorySpuController {
    @Autowired
    private PoiStoreCategoryWrapper poiStoreCategoryWrapper;

    @MethodDoc(
            description = "分页查询分类商品（含排序）",
            displayName = "分页查询分类商品（含排序）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategorySpuRequest.class,
                            description = "分页查询分类商品（含排序）"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategorySpu/pageQueryBindSpu",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/pageQueryBindSpu")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<StoreCategorySpuResponseVo> pageQueryBindSpu(@RequestBody StoreCategorySpuRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            PoiStoreCategorySpuListResponse response = poiStoreCategoryWrapper.pageQueryPoiCategorySpu(request.to(user));
            return CommonResponse.success(StoreCategorySpuResponseVo.convert2ResponseVo(response));
        } catch (Exception e) {
            log.error("pageQueryBindSpu error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "更新分类商品排序",
            displayName = "更新分类商品排序",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategorySpuSortRequest.class,
                            description = "更新分类商品排序"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategorySpu/updateSpuSequence",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/updateSpuSequence")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse updateSpuSequence(@RequestBody StoreCategorySpuSortRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.updateSpuSequence(request.to(user));
        } catch (Exception e) {
            log.error("updateSpuSequence error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "查询智能排序按钮",
            displayName = "查询智能排序按钮",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategoryRequest.class,
                            description = "查询智能排序按钮"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategorySpu/querySmartSortSwitch",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/querySmartSortSwitch")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<StoreCategorySmartSortQueryResponseVO> querySmartSortSwitch(@RequestBody StoreCategoryRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            List<MerchantStoreCategoryDTO> storeCategory = poiStoreCategoryWrapper
                    .querySimpleInfoByCategoryId(request.toSimpleQueryReq(user));
            if(CollectionUtils.isEmpty(storeCategory)){
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "未查询到分类信息");
            }
            StoreCategorySmartSortQueryResponseVO responseVO = new StoreCategorySmartSortQueryResponseVO();
            responseVO.setCategoryId(request.getCategoryId());
            responseVO.setSmartSort(Objects.equals(storeCategory.get(0).getSmartSort(), 1));
            return CommonResponse.success(responseVO);
        } catch (Exception e) {
            log.error("querySmartSortSwitch error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "更新智能排序按钮",
            displayName = "更新智能排序按钮",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategorySmartSortUpdateRequest.class,
                            description = "更新智能排序按钮"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategorySpu/updateSmartSortSwitch",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/updateSmartSortSwitch")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse updateSmartSortSwitch(@RequestBody StoreCategorySmartSortUpdateRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.updateStoreCategory(request.to(user));
        } catch (Exception e) {
            log.error("updateSmartSortSwitch error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

}
