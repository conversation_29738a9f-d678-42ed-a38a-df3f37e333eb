package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryBrandRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QuerySpuRecommendTag;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.RecommendBrandRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BrandQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelBrandDomainVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.RecommendBrandResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSProductServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.spu.BrandWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelCategoryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ErrorCodeEnum.SPU_PUSH_CHANNEL_FAILED;

/**
 * @Title: BrandController
 * @Description: OCMS 商品品牌相关接口
 * @Author: qiuweizhi
 * @Date: 2023/2/16 12:02 下午
 */
@InterfaceDoc(
        displayName = "OCMS 商品品牌接口",
        type = "restful",
        scenarios = "OCMS 商品品牌接口",
        description = "OCMS 商品品牌接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "OCMS 商品SPU相关接口")
@RestController
@RequestMapping("/pieapi/ocms/brand")
public class BrandController {

    @Resource
    private BrandWrapper brandWrapper;



    @MethodDoc(
            displayName = "查询品牌列表",
            description = "查询品牌列表",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/queryRecommendTag",
            restExamplePostData = "{\"storeId\":123 , \"channelId\": 100, \"name\":\"土豆500g\"}",
            restExampleResponseData = "ChannelCategoryDTO"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/listByChannel", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<BrandQueryResponseVO> listByChannel(@Valid @RequestBody QueryBrandRequest request) {

        return brandWrapper.listByChannel(request.getChannelId(),request.getSearchBrandName());
    }


    @MethodDoc(
            displayName = "根据商品名称查询渠道推荐类目",
            description = "根据商品名称查询渠道推荐类目",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/store/spu/queryRecommendTag",
            restExamplePostData = "{\"storeId\":123 , \"channelId\": 100, \"name\":\"土豆500g\"}",
            restExampleResponseData = "ChannelCategoryDTO"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRecommendBrand", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RecommendBrandResponse> queryRecommendBrand(@RequestBody RecommendBrandRequest request) {
        RecommendBrandResponse response = new RecommendBrandResponse();
        response.setRecommendBrandList(brandWrapper.queryRecommendBrand(request.getChannelIds(),request.getName()));
        return CommonResponse.success(response);
    }
}
