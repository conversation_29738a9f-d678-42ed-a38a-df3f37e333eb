package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.image;

import com.dianping.cat.Cat;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.image.vo.ImageUploadResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.file.ImageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;

@InterfaceDoc(
        type = "restful",
        displayName = "图片处理服务",
        description = "图片处理服务",
        scenarios = "图片处理服务"
)
@Slf4j
@RestController
@RequestMapping("/pieapi/image")
public class ImageController {

    @MethodDoc(
            displayName = "上传图片",
            description = "上传图片，依赖集团内部的图片服务",
            parameters = {
                    @ParamDoc(
                            name = "file",
                            description = "图片",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "图片上传结果",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": {\n" +
                    "    \"fileUrl\": \"图片url\"" +
                    "  },\n" +
                    "  \"msg\": \"\"\n" +
                    "}",
            restExampleUrl = "/pieapi/image/uploadImage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验token，判断当前用户是否登录，是否有权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "FALSE"
                    ),
            }
    )
    @RequestMapping(value = "/uploadImage", method = RequestMethod.POST)
    @MethodLog(logResponse = true)
    @Auth
    public CommonResponse<ImageUploadResultVO> uploadImage(@RequestParam("file") CommonsMultipartFile file) {
        try {
            byte[] content = file.getBytes();
            String url = ImageUtil.upload(content, file.getOriginalFilename());
            return CommonResponse.success(new ImageUploadResultVO(url));
        } catch (RuntimeException e) {
            log.error("上传图片错误", e);
            Cat.logEvent("UPLOAD_IMAGE", "ERROR");
            return CommonResponse.fail(ResultCode.FAIL.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("上传图片错误", e);
            Cat.logEvent("UPLOAD_IMAGE", "ERROR");
            return CommonResponse.fail(ResultCode.FAIL.getCode(),"上传图片错误");
        }
    }
}
