package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.rhino.annotation.RateLimit;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.DepartmentSpreadQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.DepartmentSpreadVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.DepartmentWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static com.sankuai.meituan.shangou.saas.common.utils.AssertUtil.isPositiveNumber;

/**
 * <AUTHOR>
 * @since 2022/12/20
 */
@InterfaceDoc(
        displayName = "部门请求服务",
        type = "restful",
        scenarios = "部门请求服务，提供获取根列表、全部部门信息等功能",
        description = "部门请求服务，提供获取根列表、全部部门信息等功能"
)
@Slf4j
@Api(value = "部门请求服务，提供获取根列表、全部部门信息等功能")
@RestController
@RequestMapping("/pieapi/department")
public class DepartmentController {

    @Resource
    private DepartmentWrapper departmentWrapper;

    @MethodDoc(
            displayName = "部门展开接口",
            description = "部门展开接口,查询部门下属部门",
            parameters = {
                    @ParamDoc(name = "departmentId", description = "部门编号 ", paramType = ParamType.REQUEST_PARAM),
                    @ParamDoc(name = "hasEmployeeList", description = "是否包含员工", paramType = ParamType.REQUEST_PARAM)
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "/pieapi/department/spread",
            restExampleResponseData = "{\"code\":0,\"data\":{\"departmentList\":[{\"departmentId\":0,\"departmentName\":\"string\",\"departmentType\":0}],\"employeeList\":[{\"employeeId\":0,\"employeeName\":\"string\"}]},\"msg\":\"string\"}"
    )
    @ApiOperation(value = "部门展开接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/spread", method = {RequestMethod.POST, RequestMethod.GET})
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    public CommonResponse<DepartmentSpreadVo> querySpread(@RequestBody DepartmentSpreadQueryRequest request) {
        isPositiveNumber(request.getDepartmentId(), "部门ID");

        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        DepartmentSpreadVo vo = departmentWrapper.queryDepartmentSpread(request.getDepartmentId(), tenantId,
                request.getHasEmployeeList());
        return CommonResponse.success(vo);
    }
}
