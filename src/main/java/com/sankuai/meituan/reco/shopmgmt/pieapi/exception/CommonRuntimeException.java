package com.sankuai.meituan.reco.shopmgmt.pieapi.exception;


import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2018/5/10
 */
public class CommonRuntimeException extends RuntimeException {

    private ResultCode resultCode;
    private transient Object[] text;

    private boolean setMessage;

    public CommonRuntimeException() {
    }

    public CommonRuntimeException(String message) {
        super(message);
        setMessage = true;
    }

    public CommonRuntimeException(String message, Throwable cause) {
        super(message, cause);
        setMessage = true;
    }

    public CommonRuntimeException(Throwable cause) {
        super(cause);
    }

    public CommonRuntimeException(String message, ResultCode code) {
        super(message);
        setMessage = true;
        this.resultCode = code;
    }

    public CommonRuntimeException(String message, Throwable cause, ResultCode code) {
        super(message, cause);
        setMessage = true;
        this.resultCode = code;
    }

    public CommonRuntimeException(Throwable cause, ResultCode codeEnum) {
        super(cause);
        this.resultCode = codeEnum;
    }

    public CommonRuntimeException(ResultCode codeEnum) {
        this.resultCode = codeEnum;
    }

    public CommonRuntimeException(ResultCode codeEnum, Object... objects) {
        this.resultCode = codeEnum;
        this.text = objects;
    }

    public ResultCode getResultCode() {
        return resultCode;
    }

    public Object[] getText() {
        return text;
    }

    public boolean isSetMessage() {
        return setMessage;
    }
}
