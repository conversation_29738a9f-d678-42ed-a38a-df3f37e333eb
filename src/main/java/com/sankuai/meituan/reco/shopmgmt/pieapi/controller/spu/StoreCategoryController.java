package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.CreateStoreCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ModifyStoreCategoryLevelRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ModifyStoreCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryPoiStoreCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategorySortRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreCategoryTopRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.StoreCategoryInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.QueryStoreCategoryListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PoiStoreCategoryWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryBasicInfoDTO;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@InterfaceDoc(
        displayName = "牵牛花门店店内分类管理",
        type = "restful",
        scenarios = "牵牛花门店店内分类管理",
        description = "牵牛花门店店内分类管理"
)
@Api(value = "牵牛花门店店内分类管理")
@Slf4j
@RestController
@RequestMapping("/pieapi/poi/storeCategory")
public class StoreCategoryController {
    @Autowired
    private TenantWrapper tenantWrapper;
    @Autowired
    private PoiStoreCategoryWrapper poiStoreCategoryWrapper;
    @MethodDoc(
            displayName = "查询牵牛花门店店内分类",
            description = "查询牵牛花门店店内分类",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "true"
                    )
            }
    )
    @ApiOperation("查询牵牛花门店店内分类")
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/query")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<QueryStoreCategoryListResponse> query(@RequestBody QueryPoiStoreCategoryRequest request) {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

            List<StoreCategoryBasicInfoDTO> storeCategoryBasicInfoDTOS = poiStoreCategoryWrapper
                    .queryPoiRelationStoreCategory(request.toRelationRequest(user));
            QueryStoreCategoryListResponse queryStoreCategoryListResponse = new QueryStoreCategoryListResponse();
            queryStoreCategoryListResponse.setCategories(StoreCategoryInfoVO.buildStoreCategoryTreeList(storeCategoryBasicInfoDTOS));
            return CommonResponse.success(queryStoreCategoryListResponse);
        } catch (Exception e) {
            log.error("query all error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "创建门店店内分类",
            displayName = "创建门店店内分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = CreateStoreCategoryRequest.class,
                            description = "创建前台分类数据"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/create",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/create")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse createPoiStoreCategory(@RequestBody CreateStoreCategoryRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.createStoreCategory(request.toPoiRpcReq(user));
        } catch (Exception e) {
            log.error("createStoreCategory error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "修改门店店内分类",
            displayName = "修改门店店内分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = ModifyStoreCategoryRequest.class,
                            description = "修改门店店内分类"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/modify",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/modify")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse modifyPoiStoreCategory(@RequestBody ModifyStoreCategoryRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.updateStoreCategory(request.toPoiRpcReq(user));
        } catch (Exception e) {
            log.error("modifyStoreCategory error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "变更门店店内分类层级",
            displayName = "变更门店店内分类层级",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = ModifyStoreCategoryLevelRequest.class,
                            description = "变更门店店内分类层级"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/modifyLevel",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/modifyLevel")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse modifyPoiStoreCategoryLevel(@RequestBody ModifyStoreCategoryLevelRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.updateStoreCategory(request.toPoiRpcReq(user));
        } catch (Exception e) {
            log.error("modifyStoreCategoryLevel error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "删除门店店内分类",
            displayName = "删除门店店内分类",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategoryRequest.class,
                            description = "删除门店店内分类"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/delete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/delete")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse deletePoiStoreCategory(@RequestBody StoreCategoryRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.deleteStoreCategory(request.toPoiRpcReq(user));
        } catch (Exception e) {
            log.error("deleteStoreCategory error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "门店店内分类排序",
            displayName = "门店店内分类排序",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategorySortRequest.class,
                            description = "门店店内分类排序"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/sort",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/sort")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse sortPoiStoreCategory(@RequestBody StoreCategorySortRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.sort(request.toPoiRpcReq(user));
        } catch (Exception e) {
            log.error("sortStoreCategory error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "门店店内分类置顶",
            displayName = "门店店内分类置顶",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = StoreCategoryTopRequest.class,
                            description = "门店店内分类置顶"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/poi/storeCategory/topCategory",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/topCategory")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse topPoiStoreCategory(@RequestBody StoreCategoryTopRequest request) {
        try {
            request.validate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            return poiStoreCategoryWrapper.updateStoreCategory(request.toPoiRpcReq(user));
        } catch (Exception e) {
            log.error("topCategory error. request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

}
