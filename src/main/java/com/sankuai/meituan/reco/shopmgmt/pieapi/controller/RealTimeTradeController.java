package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.RealTimeSettlePayRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.RealTimeTradeConfirmedRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.RealTimeTradeQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.StoreConfigQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle.RealTimeSettleConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle.RealTimeSettleTradeStatusVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle.RealTimeSettleTradeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SettleTradeWrapper;
import com.sankuai.meituan.shangou.saas.common.exception.ParamInvalidException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@InterfaceDoc(
        displayName = "实时结算服务",
        type = "restful",
        scenarios = "实时结算服务",
        description = "实时结算服务",
        host = "https://pieapi.empower.shangou.meituan.com/"
)
@Slf4j
@Api(value = "电子协议服务")
@RestController
@RequestMapping("/pieapi/realtime")
public class RealTimeTradeController {


    @Resource
    SettleTradeWrapper settleTradeWrapper;


    @MethodDoc(
            description = "外采现结交易单号生成接口",
            displayName = "外采现结交易单号生成接口",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "431315312515\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/realtime/settle/generate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/settle/generate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<String> generateTradeNo() {
        return CommonResponse.success(settleTradeWrapper.generateTradeNo().toString());
    }


    @MethodDoc(
            description = "外采现结支付发起接口",
            displayName = "外采现结支付发起接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "外采现结支付请求",
                            type = RealTimeSettlePayRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/realtime/settle/pay",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/settle/pay", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RealTimeSettleTradeVO> realTimeSettlePay(@Valid @RequestBody RealTimeSettlePayRequest request) throws ParamInvalidException {
        request.selfCheck();
        RealTimeSettleTradeVO vo = settleTradeWrapper.realTimeSettlePay(request);
        return CommonResponse.success(vo);
    }

    @MethodDoc(
            description = "外采现结支付交易查询接口",
            displayName = "外采现结支付交易查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "外采现结查询请求",
                            type = RealTimeTradeQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/realtime/trade/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/trade/query", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RealTimeSettleTradeStatusVO> queryRealTimeTrade(@Valid @RequestBody RealTimeTradeQueryRequest request) throws ParamInvalidException {
        request.selfCheck();
        RealTimeSettleTradeStatusVO vo = settleTradeWrapper.queryRealTimeTrade(request);
        return CommonResponse.success(vo);
    }

    @MethodDoc(
            description = "门店外采现结配置查询",
            displayName = "门店外采现结配置查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "门店配置查询请求",
                            type = StoreConfigQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/realtime/store/config/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/store/config/query", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RealTimeSettleConfigVO> queryStoreRealTimeSettleConfig(@Valid @RequestBody StoreConfigQueryRequest request) throws ParamInvalidException {
        request.selfCheck();
        RealTimeSettleConfigVO vo = settleTradeWrapper.queryStoreRealTimeSettleConfig(request);
        return CommonResponse.success(vo);
    }

    @MethodDoc(
            description = "外采现结交易已确认接口",
            displayName = "外采现结交易已确认接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "外采现结交易已确认请求",
                            type = RealTimeTradeConfirmedRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/realtime/trade/confirmed",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/trade/confirmed", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse realTimeSettleTradeConfirmed(@Valid @RequestBody RealTimeTradeConfirmedRequest request) throws ParamInvalidException {
        request.selfCheck();
        settleTradeWrapper.realTimeSettleTradeConfirmed(request);
        return CommonResponse.success(null);
    }
}
