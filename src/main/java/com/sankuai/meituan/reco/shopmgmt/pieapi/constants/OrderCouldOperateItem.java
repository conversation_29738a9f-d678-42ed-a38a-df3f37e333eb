package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;


/**
 * @TypeDoc( description = "操作项"
 * )
 */
public enum OrderCouldOperateItem {
    /**
     * @FieldDoc( description = "接单"
     * )
     */
    ACCEPT_ORDER(10),
    /**
     * @FieldDoc( description = "完成拣货"
     * )
     */
    COMPLETE_PICK(20),
    /**
     * @FieldDoc( description = "补打小票"
     * )
     */
    PRINT_RECEIPT(30),
    /**
     * @FieldDoc( description = "全单退款"
     * )
     */
    FULL_ORDER_REFUND(40),
    /**
     * @FieldDoc( description = "部分退款"
     * )
     */
    PART_ORDER_REFUND(50),
    RECEIVE_REFUND_PRODUCTS(60),
    WEIGHT_REFUND(70),
    /**
     * @FieldDoc( description = "转三方配送"
     * )
     */
    TURN_AGG_DELIVERY(140),
    /**
     * @FieldDoc( description = "转自配"
     * )
     */
    TURN_SELF_DELIVERY(150);

    private final int value;

    OrderCouldOperateItem(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Thrift IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Find a the enum type by its integer value, as defined in the Thrift IDL.
     *
     * @return null if the value is not found.
     */
    public static OrderCouldOperateItem findByValue(int value) {
        switch (value) {
            case 10:
                return ACCEPT_ORDER;
            case 20:
                return COMPLETE_PICK;
            case 30:
                return PRINT_RECEIPT;
            case 40:
                return FULL_ORDER_REFUND;
            case 50:
                return PART_ORDER_REFUND;
            case 60:
                return RECEIVE_REFUND_PRODUCTS;
            case 70:
                return WEIGHT_REFUND;
            default:
                return null;
        }
    }
}
