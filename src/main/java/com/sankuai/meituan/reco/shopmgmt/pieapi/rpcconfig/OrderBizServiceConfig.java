package com.sankuai.meituan.reco.shopmgmt.pieapi.rpcconfig;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.shangou.saas.service.MtUserThriftService;
import com.meituan.shangou.saas.service.PrivacyPhoneThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.BootConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/7/18 5:56 PM
 */
@Configuration
public class OrderBizServiceConfig {

    private static final String ORDER_BIZ_APP_KEY = "com.sankuai.shangou.empower.orderbiz";

    @Bean(destroyMethod = "destroy")
    public ThriftClientProxy privacyPhoneThriftService() {
        ThriftClientProxy thriftClientProxy = new ThriftClientProxy();
        thriftClientProxy.setServiceInterface(PrivacyPhoneThriftService.class);
        thriftClientProxy.setTimeout(2000);
        thriftClientProxy.setAppKey(BootConstants.APP_KEY);
        thriftClientProxy.setRemoteAppkey(ORDER_BIZ_APP_KEY);
        thriftClientProxy.setFilterByServiceName(true);
        thriftClientProxy.setNettyIO(true);
        return thriftClientProxy;
    }


    @Bean(destroyMethod = "destroy")
    public ThriftClientProxy mtUserThriftService() {
        ThriftClientProxy thriftClientProxy = new ThriftClientProxy();
        thriftClientProxy.setServiceInterface(MtUserThriftService.class);
        thriftClientProxy.setTimeout(500);
        thriftClientProxy.setAppKey(BootConstants.APP_KEY);
        thriftClientProxy.setRemoteAppkey(ORDER_BIZ_APP_KEY);
        thriftClientProxy.setFilterByServiceName(true);
        thriftClientProxy.setNettyIO(true);
        return thriftClientProxy;
    }
}
