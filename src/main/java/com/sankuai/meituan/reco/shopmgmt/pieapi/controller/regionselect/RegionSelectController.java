package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.regionselect;

import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.PopulationDensityRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.SgBeverageGmvCoverRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.rsm.FdcAddressingTaskQueryListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FdcAddressingTaskQueryListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FileVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.FileService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.regionselect.FdcAddressingWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.saas.common.storage.StorageService;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.HeatmapPointDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.PopulationDensityDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.rsm.FormDefineDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/20
 */
@InterfaceDoc(
        displayName = "区域规划通用接口",
        type = "restful",
        scenarios = "区域规划通用接口",
        description = "区域规划通用接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "区域规划通用接口")
@RestController
@RequestMapping("/pieapi/regionselect")
public class RegionSelectController {
    private static final int REGION_FILE_EXPIRE_SECONDS = 3600 * 24 * 365 * 3; // 保存3年

    @Autowired
    private FileService fileService;

    @Resource
    private FdcAddressingWrapper fdcAddressingWrapper;

    @MethodDoc(
            displayName = "上传文件",
            description = "上传文件",
            parameters = {
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/uploadFile",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "上传文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/uploadFile", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<FileVO> uploadFile(@RequestParam(value = "file") MultipartFile file) {
        try {
            return fileService.uploadFileWithUrl(file, REGION_FILE_EXPIRE_SECONDS);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "获取仓源最新版本表单定义接口",
            description = "获取仓源最新版本表单定义接口",
            parameters = {
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/rentalSource/latestFormDefine",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "获取仓源最新版本表单定义接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/rsm/rentalSource/latestFormDefine", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<List<FormDefineDto>> queryLatestFormDefine() {
        try {
            return CommonResponse.success(fdcAddressingWrapper.queryLatestFormDefine());
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "热力图接口",
            description = "热力图接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "",
                            type = PopulationDensityRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/queryRegionPopulationDensity",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "热力图接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRegionPopulationDensity", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<List<PopulationDensityDto>> queryRegionPopulationDensity(@RequestBody PopulationDensityRequest request) {
        try {
            request.validate();
            return CommonResponse.success(fdcAddressingWrapper.queryPopulationDensity(request));
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "闪购酒水覆盖率热力图",
            description = "闪购酒水覆盖率热力图",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "",
                            type = PopulationDensityRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/heatmap/sgBeverageGmvCover",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "闪购酒水覆盖率热力图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "heatmap/sgBeverageGmvCover", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<List<HeatmapPointDto>> querySgBeverageGmvCover(@RequestBody SgBeverageGmvCoverRequest request) throws TException {
        try {
            request.validate();
            return CommonResponse.success(fdcAddressingWrapper.querySgBeverageGmvCover(request));
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }
}
