package com.sankuai.meituan.reco.shopmgmt.pieapi.converters;

import static org.apache.commons.collections4.ListUtils.emptyIfNull;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpChannelSpuStatusVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSkuClippingVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuClippingPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuClippingVO;
import org.apache.commons.collections4.CollectionUtils;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuBatchUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuPageQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuQueryChannelPriceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuSyncStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpChannelSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpChannelSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuBatchUpdateResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuChannelPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuQueryChannelPriceResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuUpdateResponseDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpTabCountVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.BatchUpdateStoreProductDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ErpChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ErpChannelSpuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ErpStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ErpStoreSpuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.OperateInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PriceAdjustRateDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuTabCountDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.UpdateStoreProductDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchUpdateStoreSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryPriceAdjustRateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuPageQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.SyncStoreSpuSaleStatusRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.SyncStoreSpuStatusRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.BatchUpdateStoreSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.PageQueryStoreSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryPriceAdjustRateResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.dto.StoreCategoryPoiRouteConfigDTO;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.constant.ChannelFilterMode;

/**
 * ERP门店商品相关转换器
 *
 * <AUTHOR>
 * @since 2023/05/15
 */
public class ErpStoreSpuConverter {
    /**
     * app操作类型
     */
    public static final int APP_OPERATE_SOURCE_TYPE = 3;

    /**
     * 下架状态tab枚举值
     */
    public static final int OFF_SHELF_TAB_STATUS = 4;

    /**
     * 渠道库存：已售罄
     */
    public static final int CHANNEL_STOCK_SELL_OUT = 1;

    /**
     * 渠道库存：未售罄
     */
    public static final int CHANNEL_STOCK_NOT_SELL_OUT = -1;

    /**
     * ERP门店商品分页请求转换
     *
     * @param identityInfo
     * @param httpReq
     * @return
     */
    public static StoreSpuPageQueryRequest erpStoreSpuPageQueryRequestConvert(IdentityInfo identityInfo, ErpStoreSpuPageQueryRequest httpReq) {
        StoreSpuPageQueryRequest rpcReq = new StoreSpuPageQueryRequest();
        rpcReq.setTenantId(identityInfo.getUser().getTenantId());
        rpcReq.setPoiId(httpReq.getStoreId());

        rpcReq.setSpuIdList(httpReq.getSpuIdList());
        rpcReq.setKeywords(httpReq.getKeyword());
        rpcReq.setSaleStatusList(httpReq.getSaleStatusList());
        rpcReq.setOnlineStatus(httpReq.getOnlineStatus());
        List<Integer> channelIds = httpReq.fetchChannelIds();
        rpcReq.setChannelIds(channelIds);
        if (CollectionUtils.isNotEmpty(channelIds) && channelIds.size() > 1) {
            rpcReq.setScope(ChannelFilterMode.ANY.getValue());
        }
        if (CollectionUtils.isNotEmpty(httpReq.getChannelStockList())) {
            // 渠道库存选项字段特殊处理：
            // 因为底层查询接口只支持单个选项，这个字段的只可能有三个值：售罄、未售罄、null
            // 前端已支持了null的展示（展示售罄），因此如果前端同时传递售罄和未售罄，那就不向下传递条件即可
            if (httpReq.getChannelStockList().contains(CHANNEL_STOCK_SELL_OUT) && httpReq.getChannelStockList().contains(CHANNEL_STOCK_NOT_SELL_OUT)) {
                rpcReq.setChannelStock(null);
            }
            else {
                rpcReq.setChannelStock(httpReq.getChannelStockList().get(0));
            }
        }
        rpcReq.setMinPrice(httpReq.getMinPrice());
        rpcReq.setMaxPrice(httpReq.getMaxPrice());
        rpcReq.setFrontCategoryIds(httpReq.getFrontCategoryIds());
        rpcReq.setNoStoreCategory(httpReq.getNoFrontCategory());
        rpcReq.setTabStatus(httpReq.getTabStatus() != null ? httpReq.getTabStatus() : 2);
        rpcReq.setPage(httpReq.getPage());
        rpcReq.setPageSize(httpReq.getSize());
        rpcReq.setNeedMtChannelSpuId(httpReq.getNeedMtChannelSpuId());
        rpcReq.setOperatorId(identityInfo.getUser().getAccountId());
        rpcReq.setStockStatus(httpReq.getStockStatus());
        return rpcReq;
    }

    /**
     * ERP tab页请求转换
     *
     * @param identityInfo
     * @param httpReq
     * @return
     */
    public static StoreSpuPageQueryRequest erpStoreSpuQueryRequestConvert4TabCount(IdentityInfo identityInfo, ErpStoreSpuQueryRequest httpReq, List<Integer> saleStatusList) {
        StoreSpuPageQueryRequest rpcReq = new StoreSpuPageQueryRequest();
        rpcReq.setTenantId(identityInfo.getUser().getTenantId());
        rpcReq.setPoiId(httpReq.getStoreId());

        rpcReq.setSpuIdList(httpReq.getSpuIdList());
        rpcReq.setKeywords(httpReq.getKeyword());
        // 注意：tab页会统计上下架商品数，不受请求里面的上下架参数影响，因此这儿需要使用saleStatusList参数
         rpcReq.setSaleStatusList(saleStatusList);
        rpcReq.setOnlineStatus(httpReq.getOnlineStatus());
        List<Integer> channelIds = httpReq.fetchChannelIds();
        rpcReq.setChannelIds(channelIds);
        if (CollectionUtils.isNotEmpty(channelIds) && channelIds.size() > 1) {
            rpcReq.setScope(ChannelFilterMode.ANY.getValue());
        }
        if (CollectionUtils.isNotEmpty(httpReq.getChannelStockList())) {
            // 渠道库存选项字段特殊处理：
            // 因为底层查询接口只支持单个选项，这个字段的只可能有三个值：售罄、未售罄、null
            // 前端已支持了null的展示（展示售罄），因此如果前端同时传递售罄和未售罄，那就不向下传递条件即可
            if (httpReq.getChannelStockList().contains(CHANNEL_STOCK_SELL_OUT) && httpReq.getChannelStockList().contains(CHANNEL_STOCK_NOT_SELL_OUT)) {
                rpcReq.setChannelStock(null);
            }
            else {
                rpcReq.setChannelStock(httpReq.getChannelStockList().get(0));
            }
        }
        rpcReq.setMinPrice(httpReq.getMinPrice());
        rpcReq.setMaxPrice(httpReq.getMaxPrice());
        rpcReq.setFrontCategoryIds(httpReq.getFrontCategoryIds());
        rpcReq.setNoStoreCategory(httpReq.getNoFrontCategory());
        rpcReq.setTabStatus(httpReq.getTabStatus() != null ? httpReq.getTabStatus() : 2);
        rpcReq.setStockStatus(httpReq.getStockStatus());

        // 仅仅只是统计，不需要过多记录数
        rpcReq.setPage(1);
        rpcReq.setPageSize(1);
        return rpcReq;
    }

    public static ErpStoreSpuPageQueryResponseVO erpStoreSpuPageQueryResponseConvert(long tenantId, PageQueryStoreSpuResponse rpcResp, StoreCategoryPoiRouteConfigDTO storeCategoryConfig) {
        ErpStoreSpuPageQueryResponseVO result = new ErpStoreSpuPageQueryResponseVO();

        // 分页和总数信息
        PageInfoVO httpPageInfo = new PageInfoVO();
        if (rpcResp.getPageInfoDTO() != null) {
            PageInfoDTO rpcPageInfo = rpcResp.getPageInfoDTO();
            Integer total = rpcPageInfo.getTotal() != null ? rpcPageInfo.getTotal().intValue() : null;
            Integer size = rpcPageInfo.getSize();
            Integer totalPage = total != null && size != null ? (total + size - 1) / size : null;
            Integer page = rpcPageInfo.getPage();
            httpPageInfo.setTotalSize(total);
            httpPageInfo.setSize(size);
            httpPageInfo.setTotalPage(totalPage);
            httpPageInfo.setPage(page);
        }
        result.setPageInfo(httpPageInfo);

        // 数据列表
        result.setStoreSpuList(emptyIfNull(rpcResp.getErpStoreSpuDTOList())
                .stream()
                .map(spu -> ErpStoreSpuConverter.buildErpStoreSpuVO(tenantId, spu, storeCategoryConfig))
                .collect(Collectors.toList()));

        return result;
    }

    public static ErpTabCountVO erpStoreSpuPageQueryResponseConvert4TabCount(PageQueryStoreSpuResponse onShelfRpcResp, PageQueryStoreSpuResponse offShelfRpcResp,PageQueryStoreSpuResponse manualOffShelfRpcResp) {
        ErpTabCountVO tabCountVO = new ErpTabCountVO();

        // 上架数量
        if (onShelfRpcResp.getPageInfoDTO() != null && onShelfRpcResp.getPageInfoDTO().getTotal() != null) {
            tabCountVO.setOnShelfCount(onShelfRpcResp.getPageInfoDTO().getTotal().intValue());
        }

        // 下架数量
        if (offShelfRpcResp.getPageInfoDTO() != null && offShelfRpcResp.getPageInfoDTO().getTotal() != null) {
            tabCountVO.setOffShelfCount(offShelfRpcResp.getPageInfoDTO().getTotal().intValue());
        }

        //手动下架数量
        if(manualOffShelfRpcResp.getPageInfoDTO() != null && manualOffShelfRpcResp.getPageInfoDTO().getTotal() != null){
            tabCountVO.setManualOffShelfCount(manualOffShelfRpcResp.getPageInfoDTO().getTotal().intValue());
        }
        return tabCountVO;
    }

    public static BatchUpdateStoreSpuRequest batchUpdateSpuRequestConvert(IdentityInfo identityInfo,
                                                                          ErpStoreSpuBatchUpdateRequest httpReq,
                                                                          Boolean mustSaleOffShelves) {
        BatchUpdateStoreSpuRequest rpcReq = new BatchUpdateStoreSpuRequest();
        // step1: 租户和操作信息
        rpcReq.setTenantId(identityInfo.getUser().getTenantId());
        OperateInfoDTO operateInfoDTO = new OperateInfoDTO();
        operateInfoDTO.setOperateId(identityInfo.getUser().getAccountId());
        operateInfoDTO.setOperateName(identityInfo.getUser().getAccountName());
        operateInfoDTO.setSourceType(APP_OPERATE_SOURCE_TYPE);
        rpcReq.setOperateInfo(operateInfoDTO);

        // step2: 操作类型
        rpcReq.setOpType(httpReq.getOpType());

        // step3: 处理上下线（售卖）状态
        if (httpReq.getOnlineStatus() != null) {
            rpcReq.setOnlineStatus(httpReq.getOnlineStatus());
        }

        // step4: 处理上下架状态
        if (httpReq.getSaleStatus() != null) {
            rpcReq.setSellStatus(httpReq.getSaleStatus());
        }
        rpcReq.setMustSaleOffshelves(mustSaleOffShelves);

        // step5: 处理价格相关操作
        rpcReq.setOriginPrice(httpReq.getOriginPrice());
        rpcReq.setSellingPrice(httpReq.getSalePrice());
        if (httpReq.getPriceStatus() != null) {
            rpcReq.setPriceStatus(httpReq.getPriceStatus());
        }

        // step6: 处理库存相关操作
        rpcReq.setStock(httpReq.getStock());
        if (httpReq.getStockStatus() != null) {
            rpcReq.setStockStatus(httpReq.getStockStatus());
        }

        // step7: 处理门店商品相关参数
        Map<Long, BatchUpdateStoreProductDTO> storeRelationProductMap = new HashMap<>();
        for (ErpStoreSpuBatchUpdateRequest.StoreSpuUpdateParamVO paramVO : httpReq.getStoreSpuList()) {
            BatchUpdateStoreProductDTO storeRelationProduct = storeRelationProductMap.computeIfAbsent(paramVO.getStoreId(), storeId -> {
                BatchUpdateStoreProductDTO batchUpdateStoreProductDTO = new BatchUpdateStoreProductDTO();
                batchUpdateStoreProductDTO.setPoiId(storeId);
                batchUpdateStoreProductDTO.setUpdateStoreProductDTOList(Lists.newArrayList());
                return batchUpdateStoreProductDTO;
            });
            // 参考eapi的相同接口，会基于upc进行裂变
            for (String upc : paramVO.getUpc()) {
                UpdateStoreProductDTO updateStoreProductDTO = new UpdateStoreProductDTO();
                updateStoreProductDTO.setUpc(upc);
                updateStoreProductDTO.setErpCode(paramVO.getErpCode());
                updateStoreProductDTO.setSpuId(paramVO.getSpuId());
                storeRelationProduct.getUpdateStoreProductDTOList().add(updateStoreProductDTO);
            }
        }
        List<BatchUpdateStoreProductDTO> storeSpuList = Lists.newArrayList(storeRelationProductMap.values());
        rpcReq.setStoreSpuList(storeSpuList);

        // 参考eapi的相同接口（疑惑是该接口没考虑upc为多个情况，当然这在ERP租户模式下也不会存在）
        rpcReq.setTotalNum(storeSpuList.size());

        rpcReq.setActCheckType(httpReq.getActCheckType());
        return rpcReq;
    }

    public static ErpStoreSpuBatchUpdateResponseVO batchUpdateSpuResponseConvert(BatchUpdateStoreSpuResponse rpcResp) {
        ErpStoreSpuBatchUpdateResponseVO erpStoreSpuUpdateResult = new ErpStoreSpuBatchUpdateResponseVO();
        erpStoreSpuUpdateResult.setTotalNum(rpcResp.getTotalNum());
        erpStoreSpuUpdateResult.setSuccessNum(rpcResp.getSuccessNum());
        erpStoreSpuUpdateResult.setFailNum(rpcResp.getFailNum());
        erpStoreSpuUpdateResult.setFailList(emptyIfNull(rpcResp.getFailList()).stream()
                .map(rpcFailItem -> {
                    ErpStoreSpuUpdateResponseDetailVO detailVO = new ErpStoreSpuUpdateResponseDetailVO();
                    detailVO.setTenantId(rpcFailItem.getTenantId());
                    detailVO.setStoreId(rpcFailItem.getStoreId());
                    detailVO.setSpuId(rpcFailItem.getSpuId());
                    detailVO.setErpCode(rpcFailItem.getErpCode());
                    detailVO.setUpc(rpcFailItem.getUpc());
                    detailVO.setSkuId(rpcFailItem.getSkuId());
                    detailVO.setSpuName(rpcFailItem.getSpuName());
                    detailVO.setCode(rpcFailItem.getCode());
                    detailVO.setErrorMsg(rpcFailItem.getErrorMsg());
                    return detailVO;
                }).collect(Collectors.toList()));
        return erpStoreSpuUpdateResult;
    }

    public static SyncStoreSpuStatusRequest syncStoreSpuStatusRequestConvert(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest httpReq) {
        SyncStoreSpuStatusRequest rpcReq = new SyncStoreSpuStatusRequest();
        rpcReq.setTenantId(identityInfo.getUser().getTenantId());
        rpcReq.setStoreId(httpReq.getStoreId());
        rpcReq.setSpuId(httpReq.getSpuId());
        rpcReq.setOperatorId(identityInfo.getUser().getAccountId());
        rpcReq.setOperatorName(identityInfo.getUser().getAccountName());
        return rpcReq;
    }

    public static SyncStoreSpuSaleStatusRequest syncStoreSpuSaleStatusRequestConvert(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest httpReq) {
        SyncStoreSpuSaleStatusRequest rpcReq = new SyncStoreSpuSaleStatusRequest();
        rpcReq.setTenantId(identityInfo.getUser().getTenantId());
        rpcReq.setStoreId(httpReq.getStoreId());
        rpcReq.setSpuId(httpReq.getSpuId());
        rpcReq.setOperatorId(identityInfo.getUser().getAccountId());
        rpcReq.setOperatorName(identityInfo.getUser().getAccountName());
        return rpcReq;
    }

    public static ErpStoreSpuVO buildErpStoreSpuVO(long tenantId, ErpStoreSpuDTO storeSpuDTO, StoreCategoryPoiRouteConfigDTO storeCategoryConfig) {
        String spuId = storeSpuDTO.getSpuId();
        Long storeId = Optional.ofNullable(storeSpuDTO.getStore())
                .map(StoreDTO::getStoreId)
                .orElse(null);
        ErpStoreSpuVO vo = new ErpStoreSpuVO();
        vo.setTenantId(tenantId);
        vo.setStoreId(storeId);
        vo.setSpuId(spuId);
        vo.setName(storeSpuDTO.getName());
        vo.setMainImageUrl(CollectionUtils.isNotEmpty(storeSpuDTO.getUrls()) ? storeSpuDTO.getUrls().get(0) : "");
        vo.setImageUrls(storeSpuDTO.getUrls());
        vo.setOnlineStatus(storeSpuDTO.getOnlineStatus());
        vo.setSaleStatus(storeSpuDTO.getSaleStatus());
        vo.setPriceStatus(storeSpuDTO.getPriceStatus());
        vo.setStockStatus(storeSpuDTO.getStockStatus());
        vo.setMustSale(storeSpuDTO.getMustSale());
        if (storeCategoryConfig != null && StoreCategorySceneIdentityEnum.isPoiScene(storeCategoryConfig.getScene())) {
            // 门店分类
            List<StoreCategoryDTO> poiFrontCategories = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(storeSpuDTO.getPoiMainFrontCategories())) {
                poiFrontCategories.addAll(storeSpuDTO.getPoiMainFrontCategories());
            }
            if (CollectionUtils.isNotEmpty(storeSpuDTO.getPoiAuxiliaryFrontCategories())) {
                poiFrontCategories.addAll(storeSpuDTO.getPoiAuxiliaryFrontCategories());
            }
            vo.setFrontCategories(buildFrontCategory(poiFrontCategories));
        }
        else {
            // 总部分类（底层已处理了frontCategory的分组情况）
            vo.setFrontCategories(buildFrontCategory(storeSpuDTO.getFrontCategories()));
        }
        vo.setStoreSkuList(buildErpStoreSkuVOs(tenantId, storeId, storeSpuDTO.getStoreSkuList()));
        List<ErpChannelSpuVO> erpChannelSpuVOList = buildErpChannelSpuVOs(tenantId, storeId, storeSpuDTO.getChannelSpuList());
        vo.setChannelSpuList(erpChannelSpuVOList);
        vo.setMonthSaleAmount(storeSpuDTO.getMonthSaleAmount());
        return vo;
    }

    public static List<ErpChannelSpuVO> buildErpChannelSpuVOs(Long tenantId, Long storeId, List<ErpChannelSpuDTO> erpChannelSpuDTOList) {
        return emptyIfNull(erpChannelSpuDTOList)
                .stream()
                .map(erpChannelSpuDTO -> {
                    ErpChannelSpuVO erpChannelSpuVO = new ErpChannelSpuVO();
                    String spuId = erpChannelSpuDTO.getSpuId();
                    erpChannelSpuVO.setTenantId(tenantId);
                    erpChannelSpuVO.setStoreId(storeId);
                    erpChannelSpuVO.setSpuId(spuId);
                    erpChannelSpuVO.setChannelId(erpChannelSpuDTO.getChannelId());
                    erpChannelSpuVO.setSpuStatus(erpChannelSpuDTO.getSellStatus());
                    erpChannelSpuVO.setChannelSkuList(buildErpChannelSkuVO(tenantId, storeId, spuId, erpChannelSpuDTO.getErpChannelSkuDTOList()));
                    erpChannelSpuVO.setMtChannelSpuId(erpChannelSpuDTO.getMtChannelSpuId());
                    return erpChannelSpuVO;
                }).collect(Collectors.toList());
    }

    public static List<ErpStoreSkuVO> buildErpStoreSkuVOs(Long tenantId, Long storeId, List<ErpStoreSkuDTO> storeSkuList) {
        return emptyIfNull(storeSkuList)
                .stream()
                .map(erpStoreSkuDTO -> {
                    ErpStoreSkuVO erpStoreSkuVO = new ErpStoreSkuVO();
                    erpStoreSkuVO.setTenantId(tenantId);
                    erpStoreSkuVO.setStoreId(storeId);
                    erpStoreSkuVO.setTenantId(erpStoreSkuDTO.getTenantId());
                    erpStoreSkuVO.setSpuId(erpStoreSkuDTO.getSpuId());
                    erpStoreSkuVO.setSkuId(erpStoreSkuDTO.getSkuId());
                    erpStoreSkuVO.setErpCode(erpStoreSkuDTO.getErpCode());
                    erpStoreSkuVO.setUpcList(erpStoreSkuDTO.getUpcList());
                    erpStoreSkuVO.setSpec(erpStoreSkuDTO.getSpec());
                    erpStoreSkuVO.setWeight(erpStoreSkuDTO.getWeight());
                    erpStoreSkuVO.setSaleUnit(erpStoreSkuDTO.getSaleUnit());
                    erpStoreSkuVO.setTransferRatio(erpStoreSkuDTO.getTransferRatio());
                    erpStoreSkuVO.setOriginPrice(erpStoreSkuDTO.getOriginalPrice());
                    erpStoreSkuVO.setSalePrice(erpStoreSkuDTO.getSalePrice());
                    erpStoreSkuVO.setOfflineSalePrice(erpStoreSkuDTO.getOfflineSalePrice());
                    erpStoreSkuVO.setOnlineValidStock(erpStoreSkuDTO.getOnlineValidStock());
                    erpStoreSkuVO.setOfflineValidStock(erpStoreSkuDTO.getOfflineValidStock());
                    return erpStoreSkuVO;
                }).collect(Collectors.toList());
    }

    public static List<ErpChannelSkuVO> buildErpChannelSkuVO(Long tenantId, Long storeId, String spuId,
                                                             List<ErpChannelSkuDTO> erpChannelSkuDTOList) {
        return emptyIfNull(erpChannelSkuDTOList)
                .stream()
                .map(erpChannelSkuDTO -> {
                    ErpChannelSkuVO erpChannelSkuVO = new ErpChannelSkuVO();
                    erpChannelSkuVO.setTenantId(tenantId);
                    erpChannelSkuVO.setStoreId(storeId);
                    erpChannelSkuVO.setSpuId(spuId);
                    erpChannelSkuVO.setChannelId(erpChannelSkuDTO.getChannelId());
                    erpChannelSkuVO.setSkuId(erpChannelSkuDTO.getSkuId());
                    return erpChannelSkuVO;
                }).collect(Collectors.toList());
    }

    public static List<FrontCategorySimpleVO> buildFrontCategory(List<StoreCategoryDTO> storeCategoryDTOS) {
        return emptyIfNull(storeCategoryDTOS)
                .stream()
                .map(storeCategoryDTO -> {
                    FrontCategorySimpleVO frontCategorySimpleVO = new FrontCategorySimpleVO();
                    frontCategorySimpleVO.setFrontCategoryCode(storeCategoryDTO.getStoreCategoryCode());
                    frontCategorySimpleVO.setFrontCategoryName(storeCategoryDTO.getStoreCategoryName());
                    frontCategorySimpleVO.setFrontCategoryCodePath(storeCategoryDTO.getStoreCategoryCodePath());
                    frontCategorySimpleVO.setFrontCategoryNamePath(storeCategoryDTO.getStoreCategoryNamePath());
                    return frontCategorySimpleVO;
                }).collect(Collectors.toList());
    }

    public static QueryPriceAdjustRateRequest queryChannelPriceWithAdjustRateRequestConvert(IdentityInfo identityInfo, ErpStoreSpuQueryChannelPriceRequest request) {
        QueryPriceAdjustRateRequest rpcReq = new QueryPriceAdjustRateRequest();
        rpcReq.setTenantId(identityInfo.getUser().getTenantId());
        rpcReq.setStoreId(request.getStoreId());
        rpcReq.setSpuId(request.getSpuId());
        rpcReq.setNeedPromotionPrice(true);
        return rpcReq;
    }

    public static ErpStoreSpuQueryChannelPriceResponseVO queryChannelPriceWithAdjustRateResponseConvert(QueryPriceAdjustRateResponse rpcResp) {
        List<ErpStoreSpuChannelPriceVO> channelPriceVOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rpcResp.getPriceAdjustRateDtoList())) {
            channelPriceVOS.addAll(rpcResp.getPriceAdjustRateDtoList().stream()
                    .filter(priceAdjustRateDto -> priceAdjustRateDto != null && priceAdjustRateDto.getChannelId() != null)
                    .sorted(Comparator.comparingInt(PriceAdjustRateDto::getChannelId))
                    .map(priceAdjustRateDto -> {
                        ErpStoreSpuChannelPriceVO priceVO = new ErpStoreSpuChannelPriceVO();
                        priceVO.setSkuId(priceAdjustRateDto.getSkuId());
                        priceVO.setChannelId(priceAdjustRateDto.getChannelId());
                        priceVO.setChannelAdjustRate(priceAdjustRateDto.getChannelAdjustRate());
                        priceVO.setSkuAdjustRate(priceAdjustRateDto.getSkuAdjustRate());
                        priceVO.setChannelPrice(priceAdjustRateDto.getChannelPrice() != null ? String.valueOf(priceAdjustRateDto.getChannelPrice()) : null);
                        priceVO.setPrice(priceAdjustRateDto.getPrice() != null ? String.valueOf(priceAdjustRateDto.getPrice()) : null);
                        priceVO.setOnlineChannelPrice(priceAdjustRateDto.getOnlineChannelPrice() != null ? String.valueOf(priceAdjustRateDto.getOnlineChannelPrice()) : null);
                        priceVO.setPromotionPrice(priceAdjustRateDto.getPromotionPrice() != null ? String.valueOf(priceAdjustRateDto.getPromotionPrice()) : null);
                        return priceVO;
                    }).collect(Collectors.toList()));
        }
        ErpStoreSpuQueryChannelPriceResponseVO responseVO = new ErpStoreSpuQueryChannelPriceResponseVO();
        responseVO.setChannelPriceList(channelPriceVOS);
        return responseVO;
    }

    public static ErpStoreSpuClippingPageQueryResponseVO erpStoreSpuClippingPageQueryResponseConvert(PageQueryStoreSpuResponse rpcResp) {
        ErpStoreSpuClippingPageQueryResponseVO result = new ErpStoreSpuClippingPageQueryResponseVO();

        // 分页和总数信息
        PageInfoVO httpPageInfo = new PageInfoVO();
        if (rpcResp.getPageInfoDTO() != null) {
            PageInfoDTO rpcPageInfo = rpcResp.getPageInfoDTO();
            Integer total = rpcPageInfo.getTotal() != null ? rpcPageInfo.getTotal().intValue() : null;
            Integer size = rpcPageInfo.getSize();
            Integer totalPage = total != null && size != null ? (total + size - 1) / size : null;
            Integer page = rpcPageInfo.getPage();
            httpPageInfo.setTotalSize(total);
            httpPageInfo.setSize(size);
            httpPageInfo.setTotalPage(totalPage);
            httpPageInfo.setPage(page);
        }
        result.setPageInfo(httpPageInfo);

        // 数据列表
        result.setStoreSpuList(emptyIfNull(rpcResp.getErpStoreSpuDTOList())
                .stream()
                .map(storeSpu -> ErpStoreSpuConverter.buildErpStoreSpuClippingVO(storeSpu))
                .collect(Collectors.toList()));

        return result;
    }

    public static ErpStoreSpuClippingVO buildErpStoreSpuClippingVO(ErpStoreSpuDTO storeSpuDTO) {

        Long storeId = Optional.ofNullable(storeSpuDTO.getStore())
                .map(StoreDTO::getStoreId)
                .orElse(null);

        ErpStoreSpuClippingVO vo = new ErpStoreSpuClippingVO();
        vo.setStoreId(storeId);
        vo.setSpuId(storeSpuDTO.getSpuId());
        vo.setName(storeSpuDTO.getName());
        vo.setMainImageUrl(CollectionUtils.isNotEmpty(storeSpuDTO.getUrls()) ? storeSpuDTO.getUrls().get(0) : "");
        vo.setOnlineStatus(storeSpuDTO.getOnlineStatus());
        vo.setSaleStatus(storeSpuDTO.getSaleStatus());
        vo.setPriceStatus(storeSpuDTO.getPriceStatus());
        vo.setStockStatus(storeSpuDTO.getStockStatus());
        vo.setMustSale(storeSpuDTO.getMustSale());
        vo.setChannelSpuStatusList(Optional.ofNullable(storeSpuDTO.getChannelSpuList()).orElse(new ArrayList<>())
                .stream()
                .map(erpChannelSpuDTO -> {
                    ErpChannelSpuStatusVO channelSpuStatusVO = new ErpChannelSpuStatusVO();
                    channelSpuStatusVO.setChannelId(erpChannelSpuDTO.getChannelId());
                    channelSpuStatusVO.setSpuStatus(erpChannelSpuDTO.getSellStatus());
                    return channelSpuStatusVO;
                }).collect(Collectors.toList()));
        vo.setStoreSkuList(buildErpStoreSkuClippingVO(storeSpuDTO.getStoreSkuList()));
        return vo;
    }

    private static List<ErpStoreSkuClippingVO> buildErpStoreSkuClippingVO(List<ErpStoreSkuDTO> storeSkuList) {

        return Optional.ofNullable(storeSkuList).orElse(new ArrayList<>()).stream()
                .map(clippingDto -> {
                    ErpStoreSkuClippingVO vo = new ErpStoreSkuClippingVO();
                    vo.setSpec(clippingDto.getSpec());
                    vo.setErpCode(clippingDto.getErpCode());
                    vo.setOfflineValidStock(clippingDto.getOfflineValidStock());
                    vo.setOnlineValidStock(clippingDto.getOnlineValidStock());
                    vo.setSalePrice(clippingDto.getSalePrice());
                    vo.setTransferRatio(clippingDto.getTransferRatio());
                    vo.setUpcList(clippingDto.getUpcList());
                    return vo;
                }).collect(Collectors.toList());
    }
}
