package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 *
 */
@TypeDoc(
        description = "通过PoiId查询channnelPoiCode请求体"
)
@Data
public class GetChannelPoiCodeByPoiIdRequest {
    @FieldDoc(
            description = "租户ID",
            example = {}
    )
    private long tenantId;
    @FieldDoc(
            description = "渠道ID",
            example = {}
    )
    private int channelId;
    @FieldDoc(
            description = "门店ID",
            example = {}
    )
    private long poiId;
}
