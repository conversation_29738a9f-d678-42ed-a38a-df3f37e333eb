package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import java.util.Map;

import com.google.common.collect.ImmutableMap;
import com.sankuai.inf.octo.mns.model.HostEnv;

public class DxSSOConstants {

    /**
     * sso server oauth交互获取accessToken&userInfo的url https://epassport.meituan.com/account/unitivelogin?service=shuguopai-admin&bgSource=3&continue=https%3A%2F%2Fshuguopai.waimai.meituan.com%2Fapi%2Fv1%2Feplogin%2Fcallback%3FcallbackUrl%3Dhttps%253A%252F%252Fshuguopai.waimai.meituan.com%252Fepassport-callback.html
     */
    public static final String SSO_LOGIN_URL = "%s/account/unitivelogin?service=shuguopai-admin&bgSource=3&continue=%s";

    public static final String SSO_CALLBACK_URL = "%s/pieapi/dx/sso/callback?callbackUrl=%s";
    public static final String PIEAPI_SWIM_URLS = "http://%s-sl-pieapi.empower.shangou.test.sankuai.com";

    /**
     * epassport 环境对应的域名地址
     */
    public static final Map<HostEnv, String> EPASSPORT_ENV_URLS = ImmutableMap.<HostEnv, String>builder()
            .put(HostEnv.TEST, "http://fepassport.sjst.test.sankuai.com")
            .put(HostEnv.STAGING, "https://epassport.meituan.com")
            .put(HostEnv.PPE, "https://epassport.meituan.com")
            .put(HostEnv.PROD, "https://epassport.meituan.com")
            .build();

    /**
     * pieapi环境对应的地址
     */
    public static final Map<HostEnv, String> PIEAPI_ENV_URLS = ImmutableMap.<HostEnv, String>builder()
//            .put(HostEnv.TEST, "http://127.0.0.1:8412")
            .put(HostEnv.TEST, "http://pieapi.empower.shangou.test.sankuai.com")
            .put(HostEnv.PPE, "http://pieapi.empower.shangou.st.sankuai.com")
            .put(HostEnv.STAGING, "http://pieapi.empower.shangou.st.sankuai.com")
            .put(HostEnv.PROD, "https://pieapi-empower.meituan.com")
            .build();

    /**
     * 域名白名单
     */
    public static final String[] WHITE_URL = {"*.sankuai.com", "*.meituan.com","*.neixin.cn", "localhost", "127.0.0.1"};

    /**
     * 大象给接入业务分配的登录参数签名key&secret
     */
    public static final String SSO_FROM_KEY = "com.sankuai.shangou.empower.saasauth";
    public static final String SSO_FROM_SECRET = "365544e621154e44be9563dd66f8e64a";

    public static final String CHARSET = "UTF-8";

    public static final String REDIRECT_KEY = "redirect_uri";

    public static final String CALLBACK_KEY = "callbackUrl";

    public static final String BSID_KEY = "BSID";

    public static final String REDIRECT = "redirect:";

    /**
     * 大象domain 拼装
     */
    public static final String DOMAIN_CONCAT = "@";

    public static final String COOKIE_KEY = "e_token";

    public static final String COOKIE_PATH = "/";
    public static final String CONTENT_TYPE_APPLICATION_JSON = "application/json";

    public static final  int MAX_AGE = -1;

    //大象token请求头
    public static final String SSO_TOKEN = "ssoToken";
}
