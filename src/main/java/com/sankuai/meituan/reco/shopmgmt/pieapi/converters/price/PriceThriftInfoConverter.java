package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.price;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ExtendedAttributesDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuBaseInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.strategy.QuerySkuHitSyncStrategyDetailRequest;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 价格相关功能依赖的thrift接口信息转换
 *
 * <AUTHOR>
 */
public abstract class PriceThriftInfoConverter {

    public static StoreSpuDetailRequest buildStoreSpuDetailRpcRequest(User user, Long storeId, String spuId) {

        StoreSpuDetailRequest rpcRequest = new StoreSpuDetailRequest();

        // 基础查询条件
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setStoreId(storeId);
        rpcRequest.setSpuId(spuId);

        // 定制化查询条件
        ExtendedAttributesDTO extendedAttributesDTO = new ExtendedAttributesDTO();
        extendedAttributesDTO.setChannelInfo(true);
        extendedAttributesDTO.setChannelFrontCategory(false);
        extendedAttributesDTO.setChannelCategory(false);
        extendedAttributesDTO.setChannelBrand(false);
        extendedAttributesDTO.setChannelPrice(true);
        extendedAttributesDTO.setSpuSale(false);
        extendedAttributesDTO.setBoothInfo(false);
        extendedAttributesDTO.setTagInfo(false);
        extendedAttributesDTO.setProductionArea(false);
        extendedAttributesDTO.setOfflineStock(false);
        extendedAttributesDTO.setSkuSale(false);
        extendedAttributesDTO.setSkuQuoteInfo(false);
        extendedAttributesDTO.setWithPromotionInfo(true);
        rpcRequest.setExtendedAttributes(extendedAttributesDTO);

        return rpcRequest;
    }

    public static QuerySkuHitSyncStrategyDetailRequest buildSkuHitSyncStrategyDetailRequest(
            User user, Long storeId, StoreSpuVO storeSpuVO) {

        List<StoreSkuVO> storeSkuVOList = storeSpuVO.getStoreSkuList();
        List<ChannelSpuVO> channelSpuVOList = storeSpuVO.getChannelSpuList();
        Map<String, StoreSkuVO> skuId2StoreSkuVOMap = Optional.ofNullable(storeSkuVOList)
                .map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(StoreSkuVO::getSkuId, Function.identity(), (oldVal, newVal) -> newVal));

        // 构建查询商品定价策略所需要的商品信息
        List<SkuBaseInfoDTO> skuBaseInfoDTOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(channelSpuVOList)) {
            channelSpuVOList.forEach(channelSpuVO -> {
                List<ChannelSkuVO> channelSkuVOList = channelSpuVO.getChannelSkuList();
                if (CollectionUtils.isEmpty(channelSkuVOList)) {
                    return;
                }
                for (ChannelSkuVO channelSkuVO : channelSkuVOList) {
                    String skuId = channelSkuVO.getSkuId();
                    if (!skuId2StoreSkuVOMap.containsKey(skuId)) {
                        continue;
                    }
                    SkuBaseInfoDTO dto = new SkuBaseInfoDTO();
                    dto.setStoreId(storeId);
                    dto.setChannelId(channelSpuVO.getChannelId());
                    dto.setSkuId(skuId);
                    dto.setOfflinePrice(skuId2StoreSkuVOMap.get(skuId).getStorePrice());
                    skuBaseInfoDTOList.add(dto);
                }
            });
        }

        QuerySkuHitSyncStrategyDetailRequest detailRequest = new QuerySkuHitSyncStrategyDetailRequest();
        detailRequest.setTenantId(user.getTenantId());
        detailRequest.setSkuDTOS(skuBaseInfoDTOList);
        detailRequest.setIncludeCentConfig(true);

        return detailRequest;
    }
}
