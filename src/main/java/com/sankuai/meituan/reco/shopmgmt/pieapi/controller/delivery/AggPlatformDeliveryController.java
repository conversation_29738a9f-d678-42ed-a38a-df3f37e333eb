package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.delivery;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.TenantOrderValidator;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.QueryAggStoreConfigLinkRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.QueryOrderDetailLinkRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.QueryAggDeliveryLinkResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.AggPlatformDeliveryApplicationService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-14
 */
@Slf4j
@Auth
@RestController
@RequestMapping("/pieapi/delivery/aggPlatform")
public class AggPlatformDeliveryController {
    @Autowired
    private AggPlatformDeliveryApplicationService aggPlatformDeliveryApplicationService;

    @MethodDoc(
            displayName = "获取配送订单详情链接",
            description = "获取配送订单详情链接",
            restExampleUrl = "/pieapi/delivery/aggPlatform/order/detail/link",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "orderId", type = ParamDataType.CUSTOM, source = ParamSource.REQUEST_BODY,
                    customValidator = TenantOrderValidator.class)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/order/detail/link")
    public CommonResponse<QueryAggDeliveryLinkResponse> getAggDeliveryOrderDetailLinkUrl(@RequestBody QueryOrderDetailLinkRequest req) {
        req.validate();
        String url = aggPlatformDeliveryApplicationService.queryAggDeliveryOrderDetailLinkUrl(req.getOrderId(), req.getPoiId());
        return CommonResponse.success(new QueryAggDeliveryLinkResponse(url));
    }

    @MethodDoc(
            displayName = "获取聚合配送门店配置链接",
            description = "获取聚合配送门店配置链接",
            restExampleUrl = "/pieapi/delivery/aggPlatform/settings/link",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "entityId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logRequest = true)
    @PostMapping("/settings/link")
    public CommonResponse<QueryAggDeliveryLinkResponse> queryAggStoreSettingsLinkUrl(@RequestBody QueryAggStoreConfigLinkRequest req) {
        req.validate();
        String url = aggPlatformDeliveryApplicationService.queryAggStoreSettingsLinkUrl(req.getEntityId(), req.getPlatformCode());
        return CommonResponse.success(new QueryAggDeliveryLinkResponse(url));
    }
}
