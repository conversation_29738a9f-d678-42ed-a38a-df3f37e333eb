package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

/**
 * 店内分类spu关系管理常量类
 *
 * <AUTHOR>
 */
public abstract class FrontCategorySpuConstants {

    /**
     * 根据店内分类分页查询SPU列表的最大页码
     */
    public static final int QUERY_CHANNEL_SPU_BY_FRONT_CATEGORY_PAGE_MAX = 100;

    /**
     * 根据店内分类分页查询SPU列表的PageSize
     */
    public static final int QUERY_CHANNEL_SPU_BY_FRONT_CATEGORY_PAGE_SIZE = 50;

    /**
     * 价格排序字段
     */
    public static String SORT_FIELD_PRICE = "price";

    /**
     * 销量排序字段
     */
    public static String SORT_FIELD_MONTH_SALE_AMOUNT = "monthSaleAmount";
}
