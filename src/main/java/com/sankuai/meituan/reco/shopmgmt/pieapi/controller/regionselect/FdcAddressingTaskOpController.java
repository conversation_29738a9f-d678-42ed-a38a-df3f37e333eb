package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.regionselect;

import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.AddressingTaskEventRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.PopulationDensityRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.rsm.FdcAddressingTaskQueryListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FdcAddressingTaskQueryListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FdcAddressingTaskStatVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FdcAddressingTaskVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.regionselect.FdcAddressingWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.PopulationDensityDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/12/6 15:55
 **/
@InterfaceDoc(
        displayName = "寻仓任务操作相关接口",
        type = "restful",
        scenarios = "寻仓任务操作相关接口",
        description = "寻仓任务操作相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "寻仓任务操作相关接口")
@RestController
@RequestMapping("/pieapi/regionselect/rsm/op/addressingTask")
public class FdcAddressingTaskOpController {
    @Resource
    private FdcAddressingWrapper fdcAddressingWrapper;

    @MethodDoc(
            displayName = "作废寻仓任务接口",
            description = "作废寻仓任务接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "寻仓任务id",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/addressingTask/invalid",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "作废寻仓任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/invalid", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<Void> invalidTask(@RequestBody AddressingTaskEventRequest request) {
        try {
            fdcAddressingWrapper.invalidAddressingTask(request.getTaskId());
            return CommonResponse.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "寻仓任务状态统计",
            description = "寻仓任务状态统计",
            parameters = {
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/addressingTask/queryStatusStatistics",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "寻仓任务状态统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryStatusStatistics", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<FdcAddressingTaskStatVO> queryStatusStatistics() {
        try {
            return fdcAddressingWrapper.queryStatusStatisticsForOp();
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询寻仓任务详情",
            description = "查询寻仓任务详情",
            parameters = {
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/addressingTask/detailTask",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询寻仓任务详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detailTask", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<FdcAddressingTaskVO> detailTask(@RequestBody AddressingTaskEventRequest request) {
        try {
            return fdcAddressingWrapper.queryAddressingTask(request.getTaskId(), true);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "寻仓任务列表",
            description = "寻仓任务列表",
            parameters = {
            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/addressingTask/queryTaskList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "寻仓任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTaskList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<FdcAddressingTaskQueryListResponse> queryTaskList(@RequestBody FdcAddressingTaskQueryListRequest request) {
        try {
            return fdcAddressingWrapper.queryTaskListForOp(request);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

}
