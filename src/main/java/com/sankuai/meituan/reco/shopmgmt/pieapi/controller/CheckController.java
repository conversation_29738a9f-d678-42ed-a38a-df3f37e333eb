package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/9/28
 */
@Slf4j
@RestController
@RequestMapping("/")
public class CheckController {

    @MethodDoc(
            displayName = "泳道检查接口",
            description = "订单tab页首页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "泳道检查接口",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/monitor/alive",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：无须鉴权"
                    ),
            }
    )
    @RequestMapping(value = "/monitor/alive")
    @ResponseBody
    public String checkAlive() {

        return "ok";
    }
}
