package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChainRelationEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.TenantPageListRequest;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DxSSOConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.BatchQueryTenantChannelConfigRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.BatchQueryTenantConfigRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.PageQueryTenantRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.QueryPoiByWarehouseIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.UpdateTenantConfigForBizRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TenantBizModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.SsoUserService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.TenantConfigTypeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-03-14
 */

@InterfaceDoc(
        displayName = "租户相关接口",
        type = "restful",
        scenarios = "租户相关接口",
        description = "租户相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Api(value = "租户相关接口")
@RestController
@RequestMapping("/pieapi/tenant")
@Slf4j
public class TenantController {
    public static final String TENANT_CONFIG_FOR_BIZ_WEIGHT_KEY = "tenant_weight_key";

    @Autowired
    private TenantWrapper tenantWrapper;

    @Autowired
    private SsoUserService ssoUserService;

    @MethodDoc(
            description = "获取大象用户已开通权限的租户列表",
            displayName = "获取大象用户已开通权限的租户列表",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/tenant/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户ssoToken，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "获取大象用户已开通权限的租户列表")
    @RequestMapping(value = "/list", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<TenantListResponse> tenantList(HttpServletRequest request,
                                                         @Valid @RequestBody PageQueryTenantRequest pageQueryTenantRequest) {
        String ssoToken = request.getHeader(DxSSOConstants.SSO_TOKEN);
        User user = ssoUserService.getUserInfoByToken(ssoToken);
        log.info("获取大象用户已开通权限的租户列表，token {} user{}", ssoToken, user);
        if (user == null) {
            return CommonResponse.fail2(ResultCode.UNAUTHORIZED);
        }
        TenantPageListRequest tenantPageListRequest = new TenantPageListRequest();
        tenantPageListRequest.setPage(pageQueryTenantRequest.getPage());
        tenantPageListRequest.setPageSize(pageQueryTenantRequest.getPageSize());
        Long tenantId = pageQueryTenantRequest.getTenantId();
        if (tenantId != null) {
            tenantPageListRequest.setTenantIdList(Arrays.asList(tenantId));
        }
        tenantPageListRequest.setTenantName(pageQueryTenantRequest.getTenantName());
        tenantPageListRequest.setTenantStatus(TenantStatusEnum.ONLINE.getKey());
        tenantPageListRequest.setOptUser(user.getLogin());
        TenantListResponse tenantListResponse = tenantWrapper.pageQueryTenant(tenantPageListRequest);

        return CommonResponse.success(tenantListResponse);
    }


    @MethodDoc(
            description = "epassport免密登录鉴权回调地址",
            displayName = "epassport免密登录鉴权回调地址",
            returnValueDescription = "成功",
            restExampleUrl = "/pieapi/tenant/checkAclLoginAuth",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无数据/数据无须鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @ApiOperation(value = "epassport免密登录鉴权回调地址")
    @RequestMapping(value = "/checkAclLoginAuth", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject checkAclLoginAuth() {
        return JSONObject.parseObject("{\"data\": {\"result\": 1}}");
    }


    @MethodDoc(
            displayName = "根据仓库id查询关联的门店列表",
            description = "根据仓库id查询关联的门店列表",
            parameters = {
                    @ParamDoc(name = "request", description = "仓库id请求", example = {})
            },
            returnValueDescription = "门店列表",
            restExampleUrl = "/pieapi/tenant/queryPoiByWarehouseId",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，并判定当前用户是否有仓库/门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "warehouseId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "根据仓库id查询关联的门店列表")
    @RequestMapping(value = "/queryPoiByWarehouseId", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<List<PoiVO>> queryPoiByWarehouseId(@RequestBody QueryPoiByWarehouseIdRequest request) {
        Long warehouseId = request.getWarehouseId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        if (Objects.isNull(warehouseId)) {
            return CommonResponse.success(null);
        }
        List<PoiInfoDto> poiInfoDtos = tenantWrapper.queryPoiByWarehouseId(tenantId, warehouseId);
        return CommonResponse.success(PoiVO.ofDTO(poiInfoDtos));
    }

    @MethodDoc(
            description = "查询租户业务模式",
            displayName = "查询租户业务模式",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":{\n" +
                    "        \"bizMode\":\"convenience_store\"\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/bizMode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/bizMode", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<TenantBizModeVo> bizMode() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        TenantBizModeEnum tenantBizMode = tenantWrapper.getTenantBizMode(tenantId);
        if (Objects.isNull(tenantBizMode)) {
            tenantBizMode = TenantBizModeEnum.UNKNOWN;
        }
        TenantBizModeVo tenantBizModeVo = new TenantBizModeVo();
        tenantBizModeVo.setBizMode(tenantBizMode.getCode());
        return CommonResponse.success(tenantBizModeVo);
    }

    @MethodDoc(
            displayName = "租户加盟模式",
            description = "租户加盟模式",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "返回见Result",
            restExampleUrl = "/pieapi/tenant/chainRelation",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @ApiOperation(value = "租户加盟模式")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/chainRelation", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<TenantChainRelationVo> getTenantChainRelationMode() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        ChainRelationEnum tenantChainRelationMode = tenantWrapper.getTenantChainRelationMode(tenantId);
        return CommonResponse.success(TenantChainRelationVo.builder().chainRelation(tenantChainRelationMode).build());
    }


    @MethodDoc(
            displayName = "海商加盟主采购平台信息列表",
            description = "海商加盟主采购平台信息列表",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "返回见Result",
            restExampleUrl = "/pieapi/tenant/hsPurchaseShopInfo",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @ApiOperation(value = "海商加盟主采购平台信息列表")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/hsPurchaseShopInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<List<HsPurchasePlatformInfoVO>> getHsPurchaseShopInfo() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        return CommonResponse.success(tenantWrapper.getHsPurchaseShopInfo(tenantId));
    }


    @MethodDoc(
            description = "批量查询租户配置",
            displayName = "批量查询租户配置",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":[]\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/batchQueryTenantConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchQueryTenantConfig", method = {RequestMethod.POST})
    public CommonResponse<List<TenantConfigVo>> batchQueryTenantConfig(@RequestBody BatchQueryTenantConfigRequest request) {
        // 设置配置查询主体列表为租户ID
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        request.setSubjectIds(Collections.singletonList(tenantId));
        request.validate();
        List<TenantConfigVo> tenantConfigVos = tenantWrapper.batchQueryTenantConfig(request);
        return CommonResponse.success(tenantConfigVos);
    }


    @MethodDoc(
            description = "批量查询门店配置",
            displayName = "批量查询门店配置",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":[]\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/batchQueryPoiConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口；如果有传门店ID，进行门店ID鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "subjectIds", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchQueryPoiConfig", method = {RequestMethod.POST})
    public CommonResponse<List<TenantConfigVo>> batchQueryPoiConfig(@RequestBody BatchQueryTenantConfigRequest request) {
        if (CollectionUtils.isEmpty(request.getSubjectIds())) {
            // 如果请求中没传门店ID（绝大多数情况），则设置当前门店ID为配置主体ID
            List<Long> curStoreIds = ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList();
            request.setSubjectIds(curStoreIds);
        }
        request.validate();
        List<TenantConfigVo> tenantConfigVos = tenantWrapper.batchQueryTenantConfig(request);
        return CommonResponse.success(tenantConfigVos);
    }

    @MethodDoc(
            description = "批量查询租户渠道配置",
            displayName = "批量查询租户渠道配置",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":[]\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/batchQueryTenantChannelConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchQueryTenantChannelConfig", method = {RequestMethod.POST})
    public CommonResponse<List<TenantChannelConfigVo>> batchQueryTenantChannelConfig(@RequestBody BatchQueryTenantChannelConfigRequest request) {
        // 设置配置查询主体列表为租户ID
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        request.setSubjectIds(Collections.singletonList(tenantId));
        request.validate();
        List<TenantChannelConfigVo> tenantConfigVos = tenantWrapper.batchQueryTenantChannelConfig(request);
        return CommonResponse.success(tenantConfigVos);
    }


    @MethodDoc(
            description = "批量查询门店渠道配置",
            displayName = "批量查询门店渠道配置",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":[]\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/batchQueryPoiChannelConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口；如果有传门店ID，进行门店ID鉴权"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "subjectIds", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchQueryPoiChannelConfig", method = {RequestMethod.POST})
    public CommonResponse<List<TenantChannelConfigVo>> batchQueryPoiChannelConfig(@RequestBody BatchQueryTenantChannelConfigRequest request) {
        if (CollectionUtils.isEmpty(request.getSubjectIds())) {
            // 如果请求中没传门店ID（绝大多数情况），则设置当前门店ID为配置主体ID
            List<Long> curStoreIds = ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList();
            request.setSubjectIds(curStoreIds);
        }
        request.validate();
        List<TenantChannelConfigVo> tenantConfigVos = tenantWrapper.batchQueryTenantChannelConfig(request);
        return CommonResponse.success(tenantConfigVos);
    }

    @MethodDoc(
            description = "租户维度配置整合查询",
            displayName = "租户维度配置整合查询",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":{}\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/aggTenantLevelConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：参数tenantId,用户只能当前租户配置"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/aggTenantLevelConfig", method = {RequestMethod.POST})
    public CommonResponse<AggTenantLevelConfigVo> aggTenantLevelConfig() {
        return CommonResponse.success(tenantWrapper.aggTenantLevelConfig());
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getTenantConfigForBiz", method = {RequestMethod.POST})
    public CommonResponse<Map<Integer, TenantConfigForBizVo>> getTenantConfigForBiz() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        if (tenantId == null) {
            CommonResponse.fail(ResultCode.AUTHORIZE_ERROR);
        }
        return CommonResponse.success(tenantWrapper.queryTenantConfigForBiz(tenantId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updateTenantConfigForBiz", method = {RequestMethod.POST})
    public CommonResponse updateTenantConfigForBiz(@RequestBody UpdateTenantConfigForBizRequest updateTenantConfigForBizRequest) {

        if(!checkUpdateTenantConfigForBizRequest(updateTenantConfigForBizRequest)){
            return CommonResponse.fail(ResultCode.CHECK_PARAM_ERR);
        }
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        long uid = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
        String name = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getOperatorName();
        if (tenantId == 0 || uid == 0) {
            return CommonResponse.fail(ResultCode.AUTHORIZE_ERROR);
        }

        tenantWrapper.updateTenantConfigForBiz(tenantId, updateTenantConfigForBizRequest.getType(), updateTenantConfigForBizRequest.getConfigValue(), uid + "", name);

        return CommonResponse.success(ResultCode.SUCCESS);
    }


    @MethodDoc(
            description = "查询租户类型",
            displayName = "查询租户类型",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"data\":1\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/getTenantType",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getTenantType", method = {RequestMethod.POST,RequestMethod.GET})
    public CommonResponse<Integer> getTenantType() {
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        TenantInfoDto tenantInfo = tenantWrapper.getTenantInfo(tenantId);
        return CommonResponse.success(tenantInfo == null ? null : tenantInfo.getTenantType());
    }

    private boolean checkUpdateTenantConfigForBizRequest(UpdateTenantConfigForBizRequest updateTenantConfigForBizRequest) {
        if (updateTenantConfigForBizRequest.getType() == null || StringUtils.isEmpty(updateTenantConfigForBizRequest.getConfigValue())) {
            return false;
        }

        if (TenantConfigTypeEnum.enumOf(updateTenantConfigForBizRequest.getType()) == null) {
            return false;
        }

        Map<String, Object> configValueMap = JSONObject.parseObject(updateTenantConfigForBizRequest.getConfigValue(), Map.class);
        if (updateTenantConfigForBizRequest.getType()==TenantConfigTypeEnum.TYPE_TENANT_SPU_WEIGH.getCode()
                && !configValueMap.containsKey(TENANT_CONFIG_FOR_BIZ_WEIGHT_KEY)) {
            return false;
        }
        return true;
    }

    @MethodDoc(
            description = "查询租户门店商品建品使用总部零售价开关设置",
            displayName = "查询租户门店商品建品使用总部零售价开关设置",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"configSwitch\":1,\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/suggestPriceCoverageStoreSwitch",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/suggestPriceCoverageStoreSwitch", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Integer> suggestPriceCoverageStoreSwitch() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        if (tenantId == null) {
            CommonResponse.fail(ResultCode.AUTHORIZE_ERROR);
        }
        try {
            return CommonResponse.success(tenantWrapper.queryTenantConfigSwitchForBiz(tenantId,
                    TenantConfigTypeEnum.TYPE_CREATE_STORE_SPU_USE_SUGGEST_PRICE.getCode()));
        } catch (BizException e) {
            log.error("查询租户门店商品建品使用总部零售价开关设置异常.", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

    }

    @MethodDoc(
            description = "查询租户门店管品权限配置信息",
            displayName = "查询租户门店管品权限配置信息",
            parameters = {},
            restExampleResponseData = "{\n" +
                    "    \"code\":0,\n" +
                    "    \"message\":\"\",\n" +
                    "    \"configSwitch\":1,\n" +
                    "}",
            restExampleUrl = "/pieapi/tenant/queryTenantStoreSelfPropertiesConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTenantStoreSelfPropertiesConfig", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<TenantStoreConfigInfoVo> queryTenantStoreSelfPropertiesConfig() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        List<Long> storeIdList = ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList();
        if (tenantId == null) {
            CommonResponse.fail(ResultCode.AUTHORIZE_ERROR);
        }

        try {
            return CommonResponse.success(tenantWrapper.queryTenantStoreSelfPropertiesConfigList(tenantId, storeIdList));
        } catch (BizException e) {
            log.error("查询租户门店自定义属性配置失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            description = "查询当前租户的销售属性功能开启状态 ",
            displayName = "查询当前租户的销售属性功能开启状态 ",
            parameters = {},
            restExampleResponseData = "{}",
            restExampleUrl = "/pieapi/tenant/product/saleAttrConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @GetMapping(value = "/product/saleAttrConfig")
    @ResponseBody
    public CommonResponse<Map<String, Object>> querySaleAttrConfig() {
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Map<String, Object> result = ImmutableMap.of("status", MccConfigUtil.saleAttrConfig(tenantId));

        try {
            return CommonResponse.success(result);
        } catch (BizException e) {
            log.error("查询是否启用销售属性配置异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }
}
