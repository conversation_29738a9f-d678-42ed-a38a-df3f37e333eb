package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/1 20:11
 * @Description:
 */
@TypeDoc(
        name = "根据渠道ID查询门店请求对象",
        description = "根据渠道ID查询门店请求对象"
)
@Data
public class QueryByPoiIdRequest {
    /**
     * 门店id
     */
    private String storeId;
}
