package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.SignerInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.SignConfirmLetterRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.ElecContractConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract.ConfirmLetterSignRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract.ContractSignRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract.SendVerificationCodeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.securitydeposit.StoreIdRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuTotalCountOpTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.eleccontract.SignerInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.ConfirmLetterFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ElecContractWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 18:42
 * @Description:
 */
@InterfaceDoc(
        displayName = "电子协议服务",
        type = "restful",
        scenarios = "电子协议服务",
        description = "电子协议服务",
        host = "https://pieapi.empower.shangou.meituan.com/"
)
@Slf4j
@Api(value = "电子协议服务")
@RestController
@RequestMapping("/pieapi/contract")
public class ElecContractController {

    @Autowired
    private ElecContractWrapper elecContractWrapper;

    @Autowired
    private ConfirmLetterFacade confirmLetterFacade;

    @MethodDoc(
            description = "查询门店的电子协议签约人信息接口",
            displayName = "查询门店的电子协议签约人信息接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店的电子协议签约人信息请求",
                            type = SkuTotalCountOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\n" +
                    "        \"SignerInfoVO\": null\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/contract/querySignerInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，并判定当前用户是否有门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querySignerInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SignerInfoVO> querySignerInfo(@Valid @RequestBody StoreIdRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        SignerInfoDTO signerInfoDTO = elecContractWrapper.querySignerInfo(user.getTenantId(), request.getStoreId(),
                user.getAccountId());
        SignerInfoVO signerInfoVO = ConverterUtils.nonNullConvert(signerInfoDTO, ElecContractConverter::SignerInfoConvert);

        return CommonResponse.success(signerInfoVO);
    }

    @MethodDoc(
            description = "查询门店的所有可选的签署主体信息接口",
            displayName = "查询门店的所有可选的签署主体信息接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店的所有可选的签署主体信息请求",
                            type = StoreIdRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": [{\n" +
                    "        \"merchantName\": null\n" +
                    "        \"address\": null\n" +
                    "        \"legalPerson\": null\n" +
                    "        \"signerName\": null\n" +
                    "        \"signerPhoneNum\": null\n" +
                    "        \"subjectWmPoiId\": 1\n" +
                    "    }]\n" +
                    "}",
            restExampleUrl = "/pieapi/contract/queryAllPossibleSignerInfos",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，并判定当前用户是否有门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryAllPossibleSignerInfos", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<SignerInfoVO>> queryAllPossibleSignerInfos(@Valid @RequestBody StoreIdRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<SignerInfoDTO> signerInfoDTOList = elecContractWrapper.queryAllPossibleSignerInfos(user.getTenantId(), request.getStoreId(),
                user.getAccountId());
        List<SignerInfoVO> signerInfoVOList = Fun.map(signerInfoDTOList, ElecContractConverter::SignerInfoConvert);

        return CommonResponse.success(signerInfoVOList);
    }


    @MethodDoc(
            description = "签署电子协议接口",
            displayName = "签署电子协议接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "签署电子协议请求",
                            type = SkuTotalCountOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "}",
            restExampleUrl = "/pieapi/contract/sign",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sign", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse sign(@Valid @RequestBody ContractSignRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        elecContractWrapper.signContract(user.getTenantId(), user.getAccountId(), user.getAccountName(), request);
        return CommonResponse.success(null);
    }

    @MethodDoc(
            description = "签署确认函接口",
            displayName = "签署确认函接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "签署确认函请求",
                            type = ConfirmLetterSignRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "}",
            restExampleUrl = "/pieapi/contract/signConfirmLetter",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/signConfirmLetter", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Object> signConfirmLetter(@Valid @RequestBody ConfirmLetterSignRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if (user.getAccountType() != AccountTypeEnum.ADMIN.getValue()) {
            throw new CommonRuntimeException("仅管理员才能签署电子协议确认函，请联系管理员");
        }

        confirmLetterFacade.sign(buildRequest(user, request));

        return CommonResponse.success(null);
    }

    private SignConfirmLetterRequest buildRequest(User user, ConfirmLetterSignRequest request) {
        SignConfirmLetterRequest thriftRequest = new SignConfirmLetterRequest();
        thriftRequest.setRequestCode(request.getRequestCode());
        thriftRequest.setResponseCode(request.getResponseCode());
        thriftRequest.setConfirmLetterId(request.getConfirmLetterId());
        thriftRequest.setOperator(user.getAccountName());
        return thriftRequest;
    }

    @MethodDoc(
            description = "发送协议签署短信验证码",
            displayName = "发送协议签署短信验证码",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发送协议签署短信验证码请求",
                            type = SkuTotalCountOpTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "}",
            restExampleUrl = "/pieapi/contract/sendVerificationCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sendVerificationCode", method = RequestMethod.POST)
    public CommonResponse<String> sendVerificationCode(@RequestBody SendVerificationCodeRequest request) {
        HttpServletRequest httpRequest =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        String ip = httpRequest.getHeader("x-real-ip");
        String ua = httpRequest.getHeader("user-agent");
        String requestCode = elecContractWrapper.sendVerificationCode(request.getMobile(), ip, ua, user.getAccountId());

        return CommonResponse.success(requestCode);
    }

}
