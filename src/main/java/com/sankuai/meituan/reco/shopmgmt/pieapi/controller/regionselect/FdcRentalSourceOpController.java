package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.regionselect;

import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.RentalSourceEventRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.RentalSourceOfflineSurveyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.RentalSourceUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FdcRentalSourceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.regionselect.FdcAddressingWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.rsm.FormDefineDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/12/6 15:56
 **/
@InterfaceDoc(
        displayName = "寻仓任务操作相关接口",
        type = "restful",
        scenarios = "寻仓任务操作相关接口",
        description = "寻仓任务操作相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "寻仓任务操作相关接口")
@RestController
@RequestMapping("/pieapi/regionselect/rsm/op/rentalSource")
public class FdcRentalSourceOpController {
    @Resource
    private FdcAddressingWrapper fdcAddressingWrapper;

    @MethodDoc(
            displayName = "仓源复核通过接口",
            description = "仓源复核通过接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "仓源id",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/rentalSource/preliminaryReviewPass",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "仓源复核通过接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/preliminaryReviewPass", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<Void> preliminaryReviewPass(@RequestBody RentalSourceEventRequest request) {
        try {
            fdcAddressingWrapper.preliminaryReviewPass(request.getSourceId());
            return CommonResponse.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "仓源线下已共识接口",
            description = "仓源线下已共识接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "仓源id",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/rentalSource/offlineSurveyPass",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "仓源线下已共识接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/offlineSurveyPass", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<Void> offlineSurveyPass(@Valid @RequestBody RentalSourceOfflineSurveyRequest request) {
        try {
            fdcAddressingWrapper.offlineSurveyPassRentalSource(request);
            return CommonResponse.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "签约仓源接口",
            description = "签约仓源接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "仓源id",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/rentalSource/sign",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "签约仓源接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/sign", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<Void> signRentalSource(@RequestBody RentalSourceEventRequest request) {
        try {
            fdcAddressingWrapper.signRentalSource(request.getSourceId());
            return CommonResponse.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "作废仓源接口",
            description = "作废仓源接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "仓源id",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/rentalSource/invalid",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "作废寻仓任务接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/invalid", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<Void> invalidRentalSource(@RequestBody RentalSourceEventRequest request) {
        try {
            fdcAddressingWrapper.invalidRentalSource(request.getSourceId());
            return CommonResponse.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询仓源详情",
            description = "查询仓源详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "仓源id",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/addressingTask/detailSource",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询仓源详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/detailSource", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<FdcRentalSourceVO> detailSource(@RequestBody RentalSourceEventRequest request) {
        try {
            return fdcAddressingWrapper.queryRentalSource(request.getSourceId(), true);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "编辑仓源接口",
            description = "编辑仓源接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "",
                            type = RentalSourceUpdateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/regionselect/rsm/op/rentalSource/updateSource",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "编辑仓源接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header",
                    dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updateSource", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> updateSource(@RequestBody RentalSourceUpdateRequest request) {
        if (Objects.isNull(request.getFormItems()) || request.getFormItems().isEmpty()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "表单不能为空");
        }
        try {
            fdcAddressingWrapper.updateRentalSource(request);
            return CommonResponse.success(null);
        } catch (IllegalArgumentException e) {
            log.warn("参数异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (BusinessException e) {
            log.warn("业务异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }
}
