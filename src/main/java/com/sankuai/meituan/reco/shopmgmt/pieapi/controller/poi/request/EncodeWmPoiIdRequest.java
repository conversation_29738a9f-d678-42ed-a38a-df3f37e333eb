package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "混淆PoiId请求体"
)
@Data
public class EncodeWmPoiIdRequest {
    /**
     * 外卖门店编码
     */
    @FieldDoc(
            description = "外卖门店编码",
            example = {},
            requiredness = Requiredness.REQUIRED
    )
    private long wmPoiId;
}
