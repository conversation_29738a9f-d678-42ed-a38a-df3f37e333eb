package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.productintelligent;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.productintelligent.SimilarGoodsRequestVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.productintelligent.SimilarGoodsResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.productintelligent.SimilarGoodsServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@InterfaceDoc(
        type = "restful",
        displayName = "商品对标系统 相似商品相关接口",
        description = "商品对标系统 相似商品相关接口",
        scenarios = "商品对标系统 相似商品相关接口",
        authors = {"lixingzhong"}
)
@Slf4j
@RestController
@RequestMapping("/pieapi/productintelligent/goods/similar")
public class SimilarGoodsController {

    @Resource
    private SimilarGoodsServiceWrapper similarGoodsServiceWrapper;

    @MethodDoc(
            displayName = "查询相似商品接口",
            description = "查询相似商品接口",
            parameters = {
                    @ParamDoc(
                            name = "similarGoodsRequestVO",
                            description = "请求参数",
                            example = {}
                    )
            },
            returnValueDescription = "相应结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "不需要鉴权")
            }
    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/market", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<SimilarGoodsResponseVO> market(@Valid @RequestBody SimilarGoodsRequestVO similarGoodsRequestVO) {
        return similarGoodsServiceWrapper.marketSimilarGoods(similarGoodsRequestVO);
    }

}
