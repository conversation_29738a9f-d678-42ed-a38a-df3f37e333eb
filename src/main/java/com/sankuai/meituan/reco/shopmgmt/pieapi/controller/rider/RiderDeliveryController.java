package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.DeliveryCompleteConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.PrivacyNumberCallRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.RiderDeliveryOrderCountResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsDeliveryStatusDesc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderPickingServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.RiderDeliveryOrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderDeliveryServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 骑手配送订单 Controller.
 *
 * <AUTHOR>
 * @since 2021/6/11 14:24
 */
@InterfaceDoc(
        displayName = "骑手配送相关接口",
        type = "restful",
        scenarios = "包含骑手查询、操作配送订单的接口",
        description = "包含骑手查询、操作配送订单的接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "骑手配送订单")
@RestController
@RequestMapping("/pieapi/rider/delivery")
public class RiderDeliveryController {

    @Resource
    private RiderDeliveryServiceWrapper riderDeliveryService;

    @Resource
    private RiderPickingServiceWrapper riderPickingServiceWrapper;

    @MethodDoc(
            displayName = "分页查询骑手可领取的订单列表",
            description = "分页查询骑手可领取的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手可领取的订单列表",
                            type = QueryRiderWaitToGetOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/querywaitget",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "分页查询骑手可领取的订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querywaitget", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryOrderListResponse> queryWaitGetOrder(@Valid @RequestBody QueryRiderWaitToGetOrderRequest request) {
        return riderDeliveryService.queryWaitGetOrder(request);
    }

    @MethodDoc(
            displayName = "分页查询骑手待取货的订单列表",
            description = "分页查询骑手待取货的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手待取货的订单列表",
                            type = QueryRiderWaitToTakeGoodsOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/querywaittakegoods",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询骑手待取货的订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querywaittakegoods", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryOrderListResponse> queryWaitTakeGoodsOrder(@Valid @RequestBody QueryRiderWaitToTakeGoodsOrderRequest request) {
        return riderDeliveryService.queryWaitTakeGoodsOrder(request);
    }

    @MethodDoc(
            displayName = "分页查询骑手配送中的订单列表",
            description = "分页查询骑手配送中的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手配送中的订单列表",
                            type = QueryRiderInDeliveryOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/queryindelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询骑手配送中的订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryindelivery", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryOrderListResponse> queryInDeliveryOrder(@Valid @RequestBody QueryRiderInDeliveryOrderRequest request) {
        return riderDeliveryService.queryInDeliveryOrder(request);
    }

    @MethodDoc(
            displayName = "分页查询骑手已送达+已取消的订单列表",
            description = "分页查询骑手已送达+已取消的订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手已送达+已取消的订单列表",
                            type = QueryRiderCompletedOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/querycompleted",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询骑手已送达的订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querycompleted", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryOrderListResponse> queryCompleted(@Valid @RequestBody QueryRiderCompletedOrderRequest request) {
        return riderDeliveryService.queryCompletedOrder(request);
    }

    @MethodDoc(
            displayName = "骑手接单",
            description = "骑手接单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手取货请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/accept",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "骑手接单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/accept", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> acceptOrder(@Valid @RequestBody RiderOperateRequest request) {
        try {
            return riderDeliveryService.accept(request);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.accept fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.accept error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "骑手取货",
            description = "骑手取货",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手取货请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/takeAway",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "骑手取货")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/takeAway", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> takeAway(@Valid @RequestBody RiderOperateRequest request) {
        try {
            riderDeliveryService.takeAway(request);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.takeAway fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.takeAway error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "骑手锁定运单状态（暂停配送）",
            description = "骑手锁定运单状态（暂停配送）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手锁定运单状态（暂停配送）请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/lockDeliveryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "骑手锁定运单状态（暂停配送）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/lockDeliveryStatus", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> lockDeliveryStatus(@Valid @RequestBody RiderOperateRequest request) {
        try {
            return riderDeliveryService.changeDeliveryStatusLock(request, 1);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.lockDeliveryStatus fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.lockDeliveryStatus error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "骑手解锁运单状态（继续配送）",
            description = "骑手解锁运单状态（继续配送）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手解锁运单状态（继续配送）请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/unlockDeliveryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "骑手解锁运单状态（继续配送）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/unlockDeliveryStatus", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> unlockDeliveryStatus(@Valid @RequestBody RiderOperateRequest request) {
        try {
            riderDeliveryService.changeDeliveryStatusLock(request, 0);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.unlockDeliveryStatus fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.unlockDeliveryStatus error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "骑手送达",
            description = "骑手送达",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手取货请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/complete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "骑手送达")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/complete", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> complete(@Valid @RequestBody RiderOperateRequest request) {
        try {
            riderDeliveryService.complete(request);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.complete fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.complete error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "骑手坐标同步",
            description = "骑手坐标同步",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手坐标同步请求参数",
                            type = RiderLocationSyncRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/location",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "骑手坐标同步")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/location", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> location(@Valid @RequestBody RiderLocationSyncRequest request) {
        try {
            riderDeliveryService.location(request);
            return CommonResponse.success(null);
        } catch (CommonLogicException e) {
            log.warn("RiderDeliveryController.location logic fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.location runtime fail.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        } catch (Exception e) {
            log.error("RiderDeliveryController.location error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "分页查询骑手正在进行中的订单列表",
            description = "分页查询骑手正在进行中的订单列表，包括骑手接单，骑手取货",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询骑手正在进行中的订单列表",
                            type = QueryRiderInProgressOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/queryinprogress",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询骑手正在进行中的订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryinprogress", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryOrderListResponse> queryRiderInProgressOrder(@Valid @RequestBody QueryRiderInProgressOrderRequest request) {
        return riderDeliveryService.queryInProgressOrder(request);
    }


    @MethodDoc(
            displayName = "上报骑手送达时刻经纬度",
            description = "上报骑手送达时刻经纬度",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报骑手送达时刻经纬度请求参数",
                            type = RiderOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/riderArrivalLocation",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "骑手送达")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/riderArrivalLocation", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> riderArrivalLocation(@Valid @RequestBody RiderArrivalLocationRequest request) {
        try {
            riderDeliveryService.riderArrivalLocation(request);
            return CommonResponse.success(null);
        } catch (CommonLogicException e) {
            log.error("RiderDeliveryController.riderArrivalLocation fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.riderArrivalLocation error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "预订单立即配送",
            description = "预订单立即配送",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "预订单立即配送请求参数",
                            type = Long.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/immediatelyDelivery",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "预订单立即配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/immediatelyDelivery", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> immediatelyDelivery(@Valid @RequestBody ImmediatelyDeliveryRequest req) {
        try {
            if (!MccConfigUtil.immediatelyDeliveryGerySwitch()) {
                return CommonResponse.fail(ResultCode.NOT_APPROVE_IMMEDIATELY_DELIVERY.getCode(), ResultCode.NOT_APPROVE_IMMEDIATELY_DELIVERY.getErrorMessage());
            }

            riderPickingServiceWrapper.immediatelyPushDownFulfillWorkOrder4BookingOrder(req);
            riderDeliveryService.immediatelyDelivery(req);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.immediatelyDelivery fail.", e);
            if (e.getResultCode() != null) {
                return CommonResponse.fail(e.getResultCode().getCode(), e.getMessage());
            } else {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            log.error("RiderDeliveryController.immediatelyDelivery error.", e);
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(),ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
        }
    }

    @MethodDoc(
            displayName = "查询骑手订单数量",
            description = "查询骑手订单数量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手订单数量",
                            type = QueryRiderDeliveryOrderCountRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/riderOrderCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询骑手订单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/riderOrderCount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<RiderDeliveryOrderCountResponse> riderDeliveryOrderCount(@Valid @RequestBody QueryRiderDeliveryOrderCountRequest request) {
        RiderDeliveryOrderCountResponse response = new RiderDeliveryOrderCountResponse();
        try {
            Map<Integer, Integer> deliveryStatusQuantityMap = riderDeliveryService.queryDeliveryOrderQuantity(Arrays.asList(TmsDeliveryStatusDesc.WAITING_TO_ASSIGN_RIDER,TmsDeliveryStatusDesc.RIDER_ASSIGNED,TmsDeliveryStatusDesc.RIDER_TAKEN_GOODS));
            response.setRiderWaitToGetOrderCount(
                    deliveryStatusQuantityMap.getOrDefault(TmsDeliveryStatusDesc.WAITING_TO_ASSIGN_RIDER.getCode(), 0));
            response.setRiderWaitToTakeGoodsCount(
                    deliveryStatusQuantityMap.getOrDefault(TmsDeliveryStatusDesc.RIDER_ASSIGNED.getCode(), 0));
            response.setRiderInDeliveryCount(
                    deliveryStatusQuantityMap.getOrDefault(TmsDeliveryStatusDesc.RIDER_TAKEN_GOODS.getCode(), 0)
            );
            return CommonResponse.success(response);
        } catch (CommonLogicException e) {
            log.error("RiderDeliveryController.riderArrivalLocation fail.", e);
            return CommonResponse.fail(e.getResultCode().getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.riderArrivalLocation error.", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "上传骑手送达照片",
            description = "上传骑手送达照片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上传骑手送达照片",
                            type = PostDeliveryProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/postDeliveryProofPhoto",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "上传骑手送达照片")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/postDeliveryProofPhoto", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> postDeliveryProofPhoto(@Valid @RequestBody PostDeliveryProofPhotoRequest request) {
        try {
            riderDeliveryService.postDeliveryProofPhoto(request);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.postDeliveryProofPhoto fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.postDeliveryProofPhoto error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    @MethodDoc(
            displayName = "确认送达并且上传送达照片",
            description = "确认送达并且上传送达照片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "确认送达并且上传送达照片",
                            type = PostDeliveryProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/completeWithProofPhoto",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "确认送达并且上传送达照片")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/completeWithProofPhoto", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> completeWithProofPhoto(@Valid @RequestBody CompleteWithProofPhotoRequest request) {
        if (StringUtils.isNotBlank(request.validate())) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), request.validate());
        }

        try {
            return riderDeliveryService.completeWithProofPhoto(request);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.completeWithProofPhoto fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.completeWithProofPhoto error.", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "确认送达失败");
        }
    }

    @MethodDoc(
            displayName = "查询配送完成配置",
            description = "查询配送完成配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询配送完成配置",
                            type = PostDeliveryProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/completeDeliveryConfig",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询配送完成配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/completeDeliveryConfig", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<DeliveryCompleteConfigVO> deliveryCompleteConfig() throws TException {
        return CommonResponse.success(riderDeliveryService.getDeliveryCompleteConfig());
    }

    @MethodDoc(
            displayName = "查询订单的虚拟号通话记录",
            description = "查询订单的虚拟号通话记录",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单的虚拟号通话记录",
                            type = PostDeliveryProofPhotoRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/queryPrivacyNumberCallRecords",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "查询订单的虚拟号通话记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryPrivacyNumberCallRecords", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<List<PrivacyNumberCallRecordVO>> queryPrivacyNumberCallRecords(@Valid @RequestBody QueryPrivacyNumberCallRecordsRequest request) throws TException {
        if (StringUtils.isNotBlank(request.validate())) {
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), request.validate(), null);
        }

        return CommonResponse.success(riderDeliveryService.queryPrivacyNumberCallRecords(request.getChannelOrderId(), request.getChannelId()));
    }

}
