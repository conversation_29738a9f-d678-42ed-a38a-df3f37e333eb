package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.DeliveryExceptionReportRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.QueryDeliveryExceptionListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.QueryExceptionDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.QueryDeliveryExceptionDetailListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.QueryDeliveryExceptionListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderDeliveryServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 自营骑手配送异常Controller
 *
 * <AUTHOR>
 * @since 2022-06-24
 */

@InterfaceDoc(
        displayName = "自营骑手配送异常相关接口",
        type = "restful",
        scenarios = "包含骑手上报配送异常接口、骑手查询配送异常接口",
        description = "包含骑手上报配送异常接口、骑手查询配送异常接口",
        host = "https://pieapi-empower.meituan.com/"
)

@Slf4j
@Api(value = "自营骑手配送异常")
@RestController
@RequestMapping("/pieapi/rider/delivery/exception")
public class RiderDeliveryExceptionController {
    @Resource
    private RiderDeliveryServiceWrapper riderDeliveryService;

    @MethodDoc(
            displayName = "骑手上报配送异常",
            description = "骑手上报配送异常",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手上报配送异常请求",
                            type = DeliveryExceptionReportRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/exception/exceptionReport",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "骑手上报配送异常")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/exceptionReport", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> exceptionReport(@Valid @RequestBody DeliveryExceptionReportRequest request) {
        request.preHandle();
        try {
            riderDeliveryService.reportException(request);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.exceptionReport fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.exceptionReport error.", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "上报失败，请重试");
        }
    }


    @MethodDoc(
            displayName = "查询配送异常列表",
            description = "查询配送异常列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询配送异常列表请求",
                            type = QueryDeliveryExceptionListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/exception/exceptionList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询配送异常列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/exceptionList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryDeliveryExceptionListResponse> exceptionList(@Valid @RequestBody QueryDeliveryExceptionListRequest request) {
        try {
            return riderDeliveryService.queryExceptionList(request);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.exceptionList fail.", e);
            return CommonResponse.fail(e.getResultCode().getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.exceptionList error.", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询配送异常失败");
        }
    }

    @MethodDoc(
            displayName = "查询配送异常明细",
            description = "查询配送异常明细",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询配送异常请求",
                            type = QueryExceptionDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/delivery/exception/exceptionDetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询配送异常明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/exceptionDetail", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<QueryDeliveryExceptionDetailListResponse> exceptionDetail(@Valid @RequestBody QueryExceptionDetailRequest request) {
        try {
            return riderDeliveryService.queryExceptionDetail(request.getChannelOrderId(), request.getChannelId());
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.exceptionDetail fail.", e);
            return CommonResponse.fail(e.getResultCode().getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.exceptionDetail error.", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询配送异常明细失败");
        }

    }
}
