package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSequenceUpdateByConditionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSequenceUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSmartSortQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSmartSortUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.FrontCategorySpuSmartSortQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.FrontCategorySpuWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@InterfaceDoc(
        displayName = "店内分类spu关系管理",
        type = "restful",
        scenarios = "店内分类spu关系管理",
        description = "店内分类spu关系管理"
)
@Api(value = "店内分类spu关系管理")
@Slf4j
@RestController
@RequestMapping("/pieapi/ocms/channel/frontCategorySpu")
public class FrontCategorySpuController {

    @Autowired
    private FrontCategorySpuWrapper frontCategorySpuWrapper;

    @MethodDoc(
            description = "更新店内分类下商品排序顺序",
            displayName = "更新店内分类下商品排序顺序",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "更新店内分类下商品排序顺序请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/ocms/channel/frontCategorySpu/updateSequence",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"成功\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true)
    @ResponseBody
    @RequestMapping("/updateSequence")
    CommonResponse updateSequence(@RequestBody FrontCategorySpuSequenceUpdateRequest request) {

        // 检验参数
        request.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        // 更新当前店内分类下商品排序顺序
        return frontCategorySpuWrapper.updateSequence(user, request);
    }

    @MethodDoc(
            description = "根据条件更新店内分类下商品排序顺序",
            displayName = "根据条件更新店内分类下商品排序顺序",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "更新店内分类下商品排序顺序请求",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/ocms/channel/frontCategorySpu/updateSequenceByCondition",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"成功\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
    })
    @MethodLog(logResponse = true)
    @ResponseBody
    @RequestMapping("/updateSequenceByCondition")
    public CommonResponse updateSequenceByCondition(@RequestBody FrontCategorySpuSequenceUpdateByConditionRequest request) {

        // 检验参数
        request.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        // 更新当前店内分类下商品排序顺序
        return frontCategorySpuWrapper.updateSequenceByCondition(user, request);
    }

    @MethodDoc(
            description = "更新店内分类下商品智能排序开关",
            displayName = "更新店内分类下商品智能排序开关",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "更新店内分类下商品智能排序开关",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/ocms/channel/frontCategorySpu/updateSmartSortSwitch",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"成功\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true)
    @ResponseBody
    @RequestMapping("/updateSmartSortSwitch")
    CommonResponse updateSmartSortSwitch(@RequestBody FrontCategorySpuSmartSortUpdateRequest request) {

        // 检验参数
        request.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        return frontCategorySpuWrapper.updateSmartSort(user, request);
    }

    @MethodDoc(
            description = "查询店内分类下商品智能排序开关",
            displayName = "查询店内分类下商品智能排序开关",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询店内分类下商品智能排序开关",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/ocms/channel/frontCategorySpu/querySmartSortSwitch",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"成功\"\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @MethodLog(logResponse = true)
    @ResponseBody
    @RequestMapping("/querySmartSortSwitch")
    CommonResponse<FrontCategorySpuSmartSortQueryResponseVO> querySmartSortSwitch(@RequestBody FrontCategorySpuSmartSortQueryRequest request) {

        // 检验参数
        request.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        return frontCategorySpuWrapper.querySmartSort(user, request);
    }
}
