package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

/**
 * <AUTHOR>
 * @since 2022/9/26 20:16
 *
 * 查询配送单列表时,需要额外查询的信息类型
 **/
public enum RiderDeliveryOrderExtInfoType {
    DELIVERY_EXCEPTION_INFO(1, "配送异常信息"),
    ORDER_REVENUE_INFO(2, "营收信息"),
    TURN_DELIVERY_BUTTON_INFO(3, "展示按钮信息"),
    LACK_STOCK(4, "展示缺货信息"),
    HIGH_PRICE_TAG(5, "展示高价值标签")
    ;

    private int code;
    private String desc;

    RiderDeliveryOrderExtInfoType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
