package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-24 15:45
 * @Description: 退款原因code枚举,此处用于筛选，数据源定义在ocms mcc
 */
@AllArgsConstructor
@Getter
@Deprecated
public enum RefundReasonEnum {
    GOODS_SOLD_OUT(3, "商品已售完"),
    USER_APPLY_REFUND(5,"用户申请退款"),
    REPEATED_ORDER(6,"重复订单"),
    BUSY_SHOP(7,"店铺太忙"),
    CUSTOMER_INFO_INCORRECT(8,"客户信息错误"),
    DISABLE_DELIVERY(13, "无法配送"),
    GOODS_TAKEN_OFF(15, "商品停止供应"),
    GOODS_DELIVERY_FAILED(16,"商品错送/漏送");
    /**
     * 退款原因code
     */
    private int reasonCode;
    /**
     * 退款原因
     */
    private String reason;

    public static RefundReasonEnum findByReasonCode(int reasonCode) {
        for (RefundReasonEnum refundReasonEnum : RefundReasonEnum.values()) {
            if (refundReasonEnum.getReasonCode() == reasonCode) {
                return refundReasonEnum;
            }
        }
        return null;
    }
}
