package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.backendcategory.BackendCategoryTreeNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.backendcategory.BackendCategoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.BackendCategoryWrapper;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@InterfaceDoc(
        displayName = "后台类目服务",
        type = "restful",
        scenarios = "后台类目服务",
        description = "后台类目服务，在赋能侧维护的商户维度的类目列表",
        host = "https://pieapi.empower.shangou.meituan.com/"
)
@Slf4j
@Api(value = "后台类目服务")
@RestController
@RequestMapping("/pieapi/backend-category/")
public class BackendCategoryController {

    @Autowired
    BackendCategoryWrapper backendCategoryWrapper;

    @MethodDoc(
            displayName = "查询商家后台类目树",
            description = "查询商家后台类目树",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/backend-category/tree",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation("商家后台类目树")
    @Auth
    @PostMapping("/tree")
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<BackendCategoryResponse> backendCategoryTreeQuery() {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

            List<BackendCategoryTreeNodeVO> categoryVOList = backendCategoryWrapper.getBackendCategoryList(user.getTenantId());

            return CommonResponse.success(new BackendCategoryResponse(categoryVOList));
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }
}
