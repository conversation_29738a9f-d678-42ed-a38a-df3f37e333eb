package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import java.util.Objects;

public enum StockDockingType {
	QNH_MANAGEMENT(0, "牵牛花管理库存"),
	EXTERNAL_MANAGEMENT(1, "外部系统管理库存"),

	;
	private Integer type;
	private String desc;

	StockDockingType(Integer type, String desc) {
		this.type = type;
		this.desc = desc;
	}

	public Integer getType() {
		return type;
	}

	public String getDesc() {
		return desc;
	}

	public static StockDockingType findByType(Integer type) {
		if (Objects.isNull(type)) {
			return getDefaultType();
		}
		for (StockDockingType stockDockingType : StockDockingType.values()) {
			if (Objects.equals(stockDockingType.getType(), type)) {
				return stockDockingType;
			}
		}
		return null;
	}

	public static StockDockingType getDefaultType() {
		return QNH_MANAGEMENT;
	}
}
