package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.sn;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage.QueryTransferableRiderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sn.CheckSnCodeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sn.FuzzySearchSnCodeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.manage.SelfRiderInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sn.SnInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.sn.SnCenterServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.shangou.logistics.wio.client.sn.model.SnDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/14 14:54
 **/

@InterfaceDoc(
        displayName = "sn相关接口",
        type = "restful",
        scenarios = "包含sn校验、模糊搜索等接口",
        description = "包含sn校验、模糊搜索等接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "sn相关接口")
@RestController
@RequestMapping("/pieapi/common/sn")
public class SnController {
    @Resource
    private SnCenterServiceWrapper snCenterServiceWrapper;

    @MethodDoc(
            displayName = "校验sn是否在库",
            description = "校验sn是否在库",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "校验sn是否在库",
                            type = QueryTransferableRiderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/common/sn/checkSnCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "根据自营骑手的姓名或电话查询转单的目标骑手列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/checkSnCode", method = {RequestMethod.POST})
    public CommonResponse<Void> checkSnCode(@Valid @RequestBody CheckSnCodeRequest request) {
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), validateResult);
        }
        boolean snCodeIsValid = snCenterServiceWrapper.checkSnCode(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                request.getSnCode(), request.getSkuId());

        if (!snCodeIsValid) {
            return CommonResponse.fail(ResultCode.SN_CODE_NOT_VALID.getCode(), ResultCode.SN_CODE_NOT_VALID.getDefaultMessage());
        }

        return CommonResponse.success(null);

    }



    @MethodDoc(
            displayName = "模糊搜索sn码",
            description = "模糊搜索sn码",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "模糊搜索sn码",
                            type = QueryTransferableRiderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/common/sn/fuzzySearchSnCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @Auth
    @ApiOperation(value = "根据自营骑手的姓名或电话查询转单的目标骑手列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/fuzzySearchSnCode", method = {RequestMethod.POST})
    public CommonResponse<List<SnInfoVo>> fuzzySearchSnCode(@Valid @RequestBody FuzzySearchSnCodeRequest request) {
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), validateResult);
        }
        List<SnInfoVo> snInfoVoList = snCenterServiceWrapper.fuzzySearchSnCode(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                request.getSnCodeKeyWord(), request.getSkuId());

        return CommonResponse.success(snInfoVoList);
    }
}
