package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.messagetask.MessageTaskQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.messagetask.MessageTaskQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.MessageTaskWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@InterfaceDoc(
        displayName = "消息中心相关接口",
        type = "restful",
        scenarios = "用于获取用户待处理任务等功能.",
        description = "用于获取用户待处理任务等功能",
        host = "https://pieapi-empower.shangou.meituan.com/"
)
@Api("消息中心相关接口")
@RestController
@RequestMapping("/pieapi/messagecenter")
public class MessageCenterController {

    @Autowired
    private MessageTaskWrapper messageTaskWrapper;


    @MethodDoc(
            description = "获取用户任务列表接口",
            displayName = "获取用户任务列表接口",
            returnValueDescription = "",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "获取用户任务列表请求参数",
                            type = MessageTaskQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExamplePostData = "{\"storeId\":12093128951}\n",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": [{\"badgeNum\":1,\"displayMode\":2,\"displayName\":\"未缴纳保证金\",\"jumpUrl\":\"http://www.google.com\",\"taskBizCode\":\"BAIL\"}\n]"+
                    "}",
            restExampleUrl = "/pieapi/messagecenter/getUserTaskTemplate",
            extensions = {
            @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @PostMapping("/getUserTaskTemplate")
    @ApiOperation(value = "获取用户任务列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<List<MessageTaskQueryVO>> getUserTaskTemplate(@RequestBody MessageTaskQueryRequest request) {
        return messageTaskWrapper.getUserTaskTemplate(ApiMethodParamThreadLocal.getIdentityInfo().getUser(), request.getStoreId());
    }

}
