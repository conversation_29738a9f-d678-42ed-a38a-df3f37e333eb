package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pendingtask.PendingTaskQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pendingtask.PendingTaskQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PendingTaskWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@InterfaceDoc(
        displayName = "代办任务查询服务",
        type = "restful",
        scenarios = "代办任务查询服务",
        description = "代办任务查询服务",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "代办任务查询服务")
@RestController
@RequestMapping("/pieapi/pendingtask")
public class PendingTaskController {

    @Resource
    PendingTaskWrapper pendingTaskWrapper;


    @MethodDoc(
            displayName = "查询代办数量",
            description = "查询代办数量",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/pendingtask/query",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "查询代办数量")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/query", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<PendingTaskQueryResponse> queryPendingTask(@Valid @RequestBody PendingTaskQueryRequest request) {
        if (CollectionUtils.isNotEmpty(request.getAuthCodes())) {
            List<AuthCodeEnum> authCodeEnumList = Lists.newArrayList();
            for (String authCode : request.getAuthCodes()) {
                authCodeEnumList.add(AuthCodeEnum.authOf(authCode));
            }
            return pendingTaskWrapper.queryPendingTask(request);
        } else {
           return CommonResponse.success(null);
        }
    }


}
