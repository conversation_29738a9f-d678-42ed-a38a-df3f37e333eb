package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.wechatpullnew;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PageQueryWarehouseRankingListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewMultiModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.QueryWarehouseRankingDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewStatMultiModuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.QueryPullNewByOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewMyRecordVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewQueryByOrderIdVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewWarehouseRankDetailInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewWarehouseRankingPageVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.BindWeComPhoneRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewQrCodeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewRecordRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewStatRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.SavePicRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.SubordinateRecordRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewQrCodeVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewRecordVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewStatAggrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.SubordinateStatAggrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FlowValveException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.PictureService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pullnew.PullNewWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/14 14:44
 * Description: 蔬果派摊位相关Controller
 */
@InterfaceDoc(
        displayName = "微信拉新服务",
        type = "restful",
        scenarios = "微信拉新服务",
        description = "微信拉新服务",
        host = "https://pieapi.empower.shangou.meituan.com/"
)
@Slf4j
@Api(value = "摊位服务")
@RestController
@RequestMapping("/pieapi/pullnew")
public class WeChatPullNewController {

    @Autowired
    private PullNewWrapper pullNewWrapper;
    @Resource
    private PictureService pictureService;

    @MethodDoc(
            description = "展示微信二维码",
            displayName = "展示微信二维码",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "展示微信二维码请求",
                            type = PullNewQrCodeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/qrcode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/qrcode", method = RequestMethod.POST)
    public CommonResponse<PullNewQrCodeVo> qrcode(@Valid @RequestBody PullNewQrCodeRequest request) {
        try {
            return pullNewWrapper.qrCode(request);
        } catch (ParamException e) {
            log.info("获取二维码失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("获取二维码失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("获取二维码失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "查询绑定的企微手机号",
            displayName = "查询绑定的企微手机号",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/queryWeComPhone",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryWeComPhone", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<String> queryWeComPhone() {
        try {
            String phone = pullNewWrapper.queryBindPhone();
            return CommonResponse.success(phone);
        } catch (ParamException e) {
            log.info("查询绑定的企微手机号", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("查询绑定的企微手机号", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("查询绑定的企微手机号", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "绑定企微手机号",
            displayName = "绑定企微手机号",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "绑定企微手机号请求",
                            type = BindWeComPhoneRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/bindWeComPhone",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/bindWeComPhone", method = RequestMethod.POST)
    public CommonResponse bindWeComPhone(@Valid @RequestBody BindWeComPhoneRequest request) {
        try {
            pullNewWrapper.bindPhone(request.getPhone());
            return CommonResponse.success(null);
        } catch (ParamException e) {
            log.info("绑定企微手机号", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("绑定企微手机号", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("绑定企微手机号", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "用户拉新记录",
            displayName = "用户拉新记录",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "用户拉新记录请求",
                            type = PullNewRecordRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/record",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/record", method = RequestMethod.POST)
    @Deprecated
    public CommonResponse<PullNewRecordVo> record(@Valid @RequestBody PullNewRecordRequest request) {
        try {
            PullNewRecordVo recordVo = pullNewWrapper.queryPullNewDetail(request);
            return CommonResponse.success(recordVo);
        } catch (ParamException e) {
            log.info("获取拉新记录失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("获取拉新记录失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("获取拉新记录失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "用户的下级数量",
            displayName = "用户的下级数量",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": 10 \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/subordinate/count",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/subordinate/count", method = RequestMethod.GET)
    public CommonResponse<Integer> countSubordinate() {
        try {
            return CommonResponse.success(pullNewWrapper.countSubordinate());
        } catch (ParamException e) {
            log.info("获取用户的下级数量失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("获取用户的下级数量失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("获取用户的下级数量失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "用户拉新统计",
            displayName = "用户拉新统计",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "拉新统计请求",
                            type = PullNewRecordRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\"selfStat\":{\"validCount\":1,\"cancelCount\":0,\"firstOrderCount\":0},\"subordinateCount\":1,\"subordinateStat\":{\"validCount\":1,\"cancelCount\":0,\"firstOrderCount\":0},}} \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/stat",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/stat", method = RequestMethod.POST)
    @Deprecated
    public CommonResponse<PullNewStatAggrVo> stat(@Valid @RequestBody PullNewStatRequest request) {
        try {
            return CommonResponse.success(pullNewWrapper.pullNewStat(request));
        } catch (ParamException e) {
            log.info("获取用户拉新统计失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("获取用户拉新统计失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("获取用户拉新统计失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "用户下级拉新统计",
            displayName = "用户下级拉新统计",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "拉新记录请求",
                            type = PullNewRecordRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\"hasMore\":false,\"markId\":2,\"subordinateStats\":[{\"accountId\":1,\"accountName\":\"123\",\"stat\":{\"validCount\":1,\"cancelCount\":0,\"firstOrderCount\":0}}]} \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/subordinate/stat",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/subordinate/stat", method = RequestMethod.POST)
    @Deprecated
    public CommonResponse<SubordinateStatAggrVo> subordinateStat(@RequestBody PullNewRecordRequest request) {
        try {
            return CommonResponse.success(pullNewWrapper.subordinateStat(request));
        } catch (ParamException e) {
            log.info("获取用户拉新统计失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("获取用户拉新统计失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("获取用户拉新统计失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "用户下级拉新记录",
            displayName = "用户下级拉新记录",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "用户下级拉新记录请求",
                            type = SubordinateRecordRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/qrcode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/subordinate/record", method = RequestMethod.POST)
    @Deprecated
    public CommonResponse<PullNewRecordVo> subordinateRecord(@Valid @RequestBody SubordinateRecordRequest request) {
        try {
            PullNewRecordVo recordVo = pullNewWrapper.subordinatePullNewDetail(request);
            return CommonResponse.success(recordVo);
        } catch (ParamException e) {
            log.info("获取拉新记录失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("获取拉新记录失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("获取拉新记录失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "合成图片",
            displayName = "合成图片",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "合成图片",
                            type = SavePicRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/pullnew/save/picture",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/save/picture", method = RequestMethod.POST)
    public CommonResponse<String> savePic(@RequestBody SavePicRequest request) {
        try {
            String url = pictureService.combinePicture(request.getWidth(), request.getLength(), request.getMiniProgramQrUrl());
            return CommonResponse.success(url);
        } catch (ParamException e) {
            log.error("savePic 失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (FlowValveException e) {
            log.info("savePic 失败", e);
            return CommonResponse.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("savePic 失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "用户的地推事件统计列表",
            description = "用户的地推事件统计列表",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "/pieapi/pullnew/multi/module/query",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":1022}"
    )
    @Auth
    @ApiOperation(value = "用户的地推事件统计列表")
    @MethodLog(logResponse = true, logger = "http")
    @PostMapping(value = "/multi/module/query")
    public CommonResponse<PullNewStatMultiModuleVo> queryPullNewWithModule(@RequestBody PullNewMultiModuleRequest req) {
        try {
            PullNewStatMultiModuleVo vo = pullNewWrapper.queryPullNewWithModule(req);
            return CommonResponse.success(vo);
        } catch (IllegalArgumentException e) {
            log.error("参数错误", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询用户的地推事件列表错误", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "用户的地推事件列表V2",
            description = "用户的地推事件列表V2",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "/pieapi/pullnew/list/query/v2",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":1022}"
    )
    @Auth
    @ApiOperation(value = "用户的地推事件列表V2")
    @MethodLog(logResponse = true, logger = "http")
    @PostMapping(value = "/list/query/v2")
    public CommonResponse<PullNewMyRecordVo> pageQueryPullNewRecord(@RequestBody PullNewRecordRequest req) {
        try {
            PullNewMyRecordVo vo = pullNewWrapper.pageQueryPullNewRecord(req);
            return CommonResponse.success(vo);
        } catch (IllegalArgumentException e) {
            log.error("参数错误", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询用户的地推事件列表错误", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }


    @MethodDoc(
            displayName = "根据单号查询用户的地推事件列表",
            description = "根据单号查询用户的地推事件列表",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "/pieapi/pullnew/order/query",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":1022}"
    )
    @Auth
    @ApiOperation(value = "根据单号查询用户的地推事件列表")
    @MethodLog(logResponse = true, logger = "http")
    @PostMapping(value = "/order/query")
    public CommonResponse<PullNewQueryByOrderIdVo> queryByOrder(@Valid @RequestBody QueryPullNewByOrderRequest req) {
        try {
            List<PullNewDetailVo> pullNewDetailVos = pullNewWrapper.queryByOrder(req);
            PullNewQueryByOrderIdVo vo = new PullNewQueryByOrderIdVo();
            vo.setList(pullNewDetailVos);
            return CommonResponse.success(vo);
        } catch (IllegalArgumentException e) {
            log.error("参数错误", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("根据单号查询用户的地推事件列表错误", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }



    @MethodDoc(
            displayName = "查询前置仓排行榜",
            description = "查询前置仓排行榜",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "/pieapi/pullnew/warehouse/rankingList",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":1022}"
    )
    @Auth
    @ApiOperation(value = "查询前置仓排行榜")
    @MethodLog(logResponse = true, logger = "http")
    @PostMapping(value = "/warehouse/rankingList")
    public CommonResponse<PullNewWarehouseRankingPageVo> queryRankingList(@Valid @RequestBody PageQueryWarehouseRankingListRequest req) {
        try {
            PullNewWarehouseRankingPageVo rankingPageVo=pullNewWrapper.queryRankingList(req);
            return CommonResponse.success(rankingPageVo);
        } catch (IllegalArgumentException e) {
            log.error("参数错误", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询前置仓排行榜错误", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询前置仓排行榜置底信息",
            description = "查询前置仓排行榜置底信息",
            parameters = {
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            restExampleUrl = "/pieapi/pullnew/warehouse/rankingDetail",
            restExamplePostData = "{}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":1022}"
    )
    @Auth
    @ApiOperation(value = "查询前置仓排行榜置底信息")
    @MethodLog(logResponse = true, logger = "http")
    @PostMapping(value = "/warehouse/rankingDetail")
    public CommonResponse<PullNewWarehouseRankDetailInfo> queryRankingDetail(@Valid @RequestBody QueryWarehouseRankingDetailRequest req) {
        try {
            PullNewWarehouseRankDetailInfo detailInfo=pullNewWrapper.queryRankingDetail(req);
            return CommonResponse.success(detailInfo);
        } catch (IllegalArgumentException e) {
            log.error("参数错误", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询前置仓排行榜置底信息错误", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

}
