package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.tenant.thrift.dto.biz.TenantBizModuleDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.AllStoreRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appraise.AppraiseResultQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appraise.IndicatorDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.AppModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise.AppraiseResultHistoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise.IndicatorDetailsResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.ManagementHomepageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.ManagementReversionHomepageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.MerchantExaminationResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.TenantBaseConfigVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ManagementWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ResourceManagementWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@InterfaceDoc(
        displayName = "经营服务",
        type = "restful",
        scenarios = "经营服务",
        description = "经营服务",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "经营服务")
@RestController
@RequestMapping("/pieapi/management")
public class ManagementController {

    @Autowired
    ManagementWrapper managementWrapper;

    @Autowired
    ResourceManagementWrapper resourceManagementWrapper;

    @Autowired
    private TenantWrapper tenantWrapper;

    @MethodDoc(
            displayName = "经营tab页首页查询接口",
            description = "经营tab页首页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "经营tab页首页查询接口",
                            type = AppModuleRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    ),
                    @ParamDoc(
                            name = "check",
                            description = "经营tab页首页查询接口",
                            type = BindingResult.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/management/homepage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "查询App权限及数字角标")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnApp", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/homepage", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    @Deprecated
    public CommonResponse<ManagementHomepageResponse> homepage(@RequestBody @Valid AppModuleRequest req, BindingResult check) {
        if (check.hasErrors()) {
            CommonResponse.fail(ResultCode.CHECK_PARAM_ERR);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return managementWrapper.homepage(identityInfo.getUser().getTenantId(),identityInfo.getStoreIdList(), req);
    }

    @MethodDoc(
            displayName = "经营tab页首页查询接口-改版后",
            description = "经营tab页首页查询接口-改版后",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "经营tab页首页查询接口",
                            type = AppModuleRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    ),
                    @ParamDoc(
                            name = "check",
                            description = "经营tab页首页查询接口",
                            type = BindingResult.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/management/revisionHomepage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "经营tab页首页查询接口-改版后")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnApp", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/revisionHomepage", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<ManagementReversionHomepageResponse> revisionHomepage(@RequestBody @Valid AppModuleRequest req, BindingResult check) {
        if (check.hasErrors()) {
            CommonResponse.fail(ResultCode.CHECK_PARAM_ERR);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return managementWrapper.revisionHomepage(identityInfo.getUser().getTenantId(), req.getEntityId(), identityInfo.getStoreIdList(), req);
    }


    @MethodDoc(
            displayName = "商家体检信息查询接口",
            description = "商家体检信息查询接口",
            parameters = {
                    @ParamDoc(
                            name = "req",
                            description = "商家体检信息查询接口参数",
                            type = AllStoreRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/management/queryMerchantExamination",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "商家体检信息查询接口参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnApp", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/queryMerchantExamination", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<MerchantExaminationResponse> queryMerchantExamination() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return resourceManagementWrapper.getPreviewForUser(identityInfo);
    }



    @MethodDoc(
            displayName = "商家体检指标明细数据查询接口",
            description = "商家体检指标明细数据查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "指标明细查询接口参数",
                            type = IndicatorDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/management/queryIndicatorDetails",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "商家体检指标明细数据查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnApp", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")

    })
    @Auth
    @RequestMapping(value = "/queryIndicatorDetails", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<IndicatorDetailsResponse> queryIndicatorDetails(@RequestBody IndicatorDetailRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return resourceManagementWrapper.queryIndicatorDetail(identityInfo, request);
    }




    @MethodDoc(
            displayName = "考核历史结果查询接口",
            description = "考核历史结果查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "考核历史结果查询接口参数",
                            type = AppraiseResultQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/management/queryIndicatorDetails",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "考核历史结果查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnApp", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/queryAppraiseHistory", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<AppraiseResultHistoryResponse> queryAppraiseHistory(@RequestBody AppraiseResultQueryRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();

        return resourceManagementWrapper.queryAppraiseResultHistory(identityInfo.getUser().getTenantId(), request.getPoiId(), request.getMonth());


    }


    @MethodDoc(
            displayName = "所有模块查询接口",
            description = "所有模块查询接口",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/management/modules",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiOperation(value = "考核历史结果查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnApp", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "mrnVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @Auth
    @RequestMapping(value = "/modules", method = RequestMethod.POST)
    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<TenantBaseConfigVo> getAllModules() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<TenantBizModuleDto> tenantModules = tenantWrapper.getTenantModules(identityInfo.getUser().getTenantId());
        return CommonResponse.success(TenantBaseConfigVo.of(tenantModules, identityInfo.getUser().getTenantId()));
    }



}
