package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.wechatpullnew;

import com.meituan.shangou.sac.dto.model.CombinationRuleDTO;
import com.meituan.shangou.sac.dto.request.rule.QueryCombinationRuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.CombinationRuleClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/pieapi/combinationRule")
@Slf4j
public class CombinationRuleController {

    @Autowired
    private CombinationRuleClient combinationRuleClient;

    @ApiOperation(value = "查询规则详情")
    @RequestMapping(value = "/detail", method = {RequestMethod.POST})
    public CommonResponse<List<CombinationRuleDTO>> detail(@RequestBody QueryCombinationRuleRequest request) {
        return CommonResponse.success(combinationRuleClient.queryCombinationRule(request));
    }

}
