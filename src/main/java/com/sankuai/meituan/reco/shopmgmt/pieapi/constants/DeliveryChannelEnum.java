package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import java.util.Objects;

import lombok.Getter;

/**
 * 配送渠道信息
 *
 * <AUTHOR>
 * @since 2021/3/17 14:17
 */
@Getter
public enum DeliveryChannelEnum {
    SHUNFENG(10001, "顺丰同城", "http://s3plus.sankuai.com/eapi/248ffc76a9d26f66b4da56e76dd5bda7_1615896400334.jpg?AWSAccessKeyId=689e9e56b7de41f9951b8e42e2a5ba07&Expires=1931256400&Signature=ltv%2B2K6y0aN3lhce3ILdexkqPAo%3D"),
    DADA(10002, "达达快送", "http://s3plus.sankuai.com/eapi/e9f14e0b1178c1be8fb53a7680e23654_1615896321409.jpg?AWSAccessKeyId=689e9e56b7de41f9951b8e42e2a5ba07&Expires=1931256321&Signature=DIfcS%2BIbnLG6oNBXy2VWe3gG1j0%3D"),
    ;

    /**
     * 渠道编码
     */
    private final Integer channelCode;

    /**
     * 渠道名称
     */
    private final String channelName;

    /**
     * 渠道图标
     */
    private final String channelIcon;

    DeliveryChannelEnum(int channelCode, String channelName) {
        this.channelCode = channelCode;
        this.channelName = channelName;
        this.channelIcon = null;
    }

    DeliveryChannelEnum(int channelCode, String channelName, String channelIcon) {
        this.channelCode = channelCode;
        this.channelName = channelName;
        this.channelIcon = channelIcon;
    }

    public static DeliveryChannelEnum findByChannelCode(Integer channelCode) {
        if (Objects.isNull(channelCode)) {
            return null;
        }
        switch (channelCode) {
            case 10001:
                return SHUNFENG;
            case 10002:
                return DADA;
            default:
                return null;
        }
    }

}
