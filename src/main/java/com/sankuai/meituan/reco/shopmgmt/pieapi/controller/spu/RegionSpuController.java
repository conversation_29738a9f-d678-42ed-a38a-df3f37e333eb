package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.AddTenantSkuApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.PageTaggedSpuByQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryTenantRegionSpuByNameRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryTenantSkuBySpuIdApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.QueryTenantRegionSpuByNameResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.RegionSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OcmsTaggedSpuCoverageWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


@InterfaceDoc(
        displayName = "OCMS 区域商品SPU相关接口",
        type = "restful",
        scenarios = "OCMS 区域商品SPU相关接口",
        description = "OCMS 区域商品SPU相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Auth
@Api(value = "OCMS 区域商品SPU相关接口")
@RestController
@RequestMapping("/pieapi/ocms/spu/region")
public class RegionSpuController {

    @Resource
    private OcmsTaggedSpuCoverageWrapper ocmsTaggedSpuCoverageWrapper;

    @MethodDoc(
            description = "获取城市某tag商品不在某门店的分页接口",
            displayName = "获取城市某tag商品不在某门店的分页接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询标签商品覆盖信息",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/ocms/spu/region",
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"suggestSpuList\": \"[]\"" +
                    "    \"pageInfo\":\"\"" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true)
    @RequestMapping(value = "/pageQueryRegionSpuExcludeStoreByTag", method = RequestMethod.POST)
    @ResponseBody
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<RegionSpuPageQueryResponseVO> pageQueryRegionSpuExcludeStoreByTag(@Valid @RequestBody PageTaggedSpuByQueryRequest request) {
        return ocmsTaggedSpuCoverageWrapper.pageQueryRegionSpuExcludeStoreByTag(request, ApiMethodParamThreadLocal.getIdentityInfo().getUser());
    }
}
