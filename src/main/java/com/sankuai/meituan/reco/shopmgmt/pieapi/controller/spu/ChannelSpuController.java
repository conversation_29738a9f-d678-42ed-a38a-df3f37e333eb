// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality.RecommendWeightRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality.SpuQualityInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchChannelCategoryPropertyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelCategoryPropertyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelSpecAttrRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelSpuKeyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.CreateChannelSpuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.PageQueryChannelSpuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality.CalSpuQualityResultDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality.RecommendWeightVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelAdventRuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AdventRuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelCategoryPropertyVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpecialAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ChannelSpuWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.service.ChannelCategoryThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.CalSpuQualityDetailInfoResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.SpuRecommendWeightResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.service.ChannelProductQueryThriftService;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2020/11/26 上午11:42
 **/
@InterfaceDoc(
        displayName = "渠道SPU相关接口",
        type = "restful",
        scenarios = "渠道SPU相关接口",
        description = "渠道SPU相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Auth
@Api(value = "渠道SPU相关接口")
@RestController
@RequestMapping("/pieapi/ocms/channel/spu")
public class ChannelSpuController {
    @Autowired
    private ChannelSpuWrapper channelSpuWrapper;
    @Autowired
    private ChannelProductQueryThriftService channelProductQueryThriftService;

    @Autowired
    private ChannelCategoryThriftService channelCategoryThriftService;

    @MethodDoc(
            displayName = "渠道商品分页查询接口",
            description = "渠道商品分页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询渠道商品请求参数",
                            type = PageQueryChannelSpuRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/pageQuery",
            returnValueDescription = "待补充",
            extensions = {
                @ExtensionDoc(
                        name = "SECURITY_PRIVILEGE",
                        content = "数据鉴权逻辑：正常login用户"
                ),
                @ExtensionDoc(
                        name = "SECURITY_PRIVILEGE_VERIFY",
                        content = "True"
                ),
        }
    )
    @RequestMapping("/pageQuery")
    CommonResponse<ChannelSpuPageQueryResponseVO> pageQuery(@RequestBody PageQueryChannelSpuRequest request) {
        if (!request.validate()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "参数错误");
        }
        try {
            ChannelSpuPageQueryResponseVO responseVO = channelSpuWrapper.pageQueryChannelSpu(request);
            return CommonResponse.success(responseVO);
        } catch (Exception e) {
            log.error("ChannelSpuController.pageQuery, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "同步指定渠道商品",
            description = "同步指定渠道商品",
            parameters = {
                    @ParamDoc(name = "request", description = "请求参数", type = ChannelSpuKeyRequest.class)
            },
            returnValueDescription = "返回结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType =
                    "string"),
    })
    @ResponseBody
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @PostMapping(value = "/syncChannelSpu")
    public CommonResponse<Boolean> syncChannelSpu(@RequestBody ChannelSpuKeyRequest request) {
        try {
            request.validate();

            return channelSpuWrapper.syncChannelSpu(request);
        } catch (IllegalArgumentException e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "处理异常，请稍后重试");
        }
    }

    @MethodDoc(
            displayName = "创建渠道商品接口",
            description = "创建渠道商品接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "创建渠道商品请求参数",
                            type = CreateChannelSpuRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/create",
            returnValueDescription = "创建结果",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @PostMapping("/create")
    CommonResponse<Void> createChannelSpu(@RequestBody CreateChannelSpuRequest request) {
        if (!request.validate()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "参数错误");
        }
        return channelSpuWrapper.createChannelSpu(request.toBatchCreateChannelSpu());
    }

    @MethodDoc(
            displayName = "查询渠道类目属性",
            description = "查询渠道类目属性",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道类目属性请求"
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/queryChannelCategoryProperty",
            returnValueDescription = "详见Result",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @PostMapping("/queryChannelCategoryProperty")
    CommonResponse<ChannelCategoryPropertyVo> queryChannelCategoryProperty(@RequestBody ChannelCategoryPropertyRequest request) {
        if (!request.validate()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "参数错误");
        }
        try {
            ChannelCategoryPropertyVo responseVO = channelSpuWrapper.queryChannelCategoryProperty(request);
            return CommonResponse.success(responseVO);
        } catch (Exception e) {
            log.error("ChannelSpuController.queryChannelCategoryProperty, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "批量查询渠道类目属性",
            description = "批量查询渠道类目属性",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道类目属性请求"
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/batchQueryChannelCategoryProperty",
            returnValueDescription = "详见Result",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @PostMapping("/batchQueryChannelCategoryProperty")
    CommonResponse<Map<Integer, List<ChannelCategoryPropertyVo>>> batchQueryChannelCategoryProperty(@RequestBody BatchChannelCategoryPropertyRequest request) {
        if (!request.validate()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "参数错误");
        }
        try {
            Map<Integer, List<ChannelCategoryPropertyVo>> result = channelSpuWrapper.batchQueryChannelCategoryProperty(request);
            return CommonResponse.success(result);
        }
        catch (Exception e) {
            log.error("查询类目属性异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询渠道特殊属性",
            description = "查询渠道特殊属性",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询渠道特殊属性请求"
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/queryChannelSpecialProperty",
            returnValueDescription = "详见Result",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @PostMapping("/queryChannelSpecialProperty")
    CommonResponse<ChannelSpecialAttrVo> queryChannelSpecialProperty(@RequestBody ChannelSpecAttrRequest request) {
        if (!request.validate()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "参数错误");
        }
        try {
            ChannelSpecialAttrVo responseVO = channelSpuWrapper.queryChannelSpecialProperty(request);
            return CommonResponse.success(responseVO);
        } catch (Exception e) {
            log.error("ChannelSpuController.queryChannelSpecialProperty, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "查询商品的推荐总量",
            description = "查询商品的推荐总量",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询商品的推荐总量"
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/queryRecommendWeight",
            returnValueDescription = "详见Result",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @PostMapping("/queryRecommendWeight")
    CommonResponse<RecommendWeightVO> queryRecommendWeight(@RequestBody RecommendWeightRequest request) {
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getName()), "商品名称必填");
            SpuRecommendWeightResponse rpcResponse = channelProductQueryThriftService.querySpuRecommendWeight(request.convertRpcRequest());

            if (rpcResponse.getCode() == null ) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取推荐商品重量失败，请稍后再试");
            }
            CommonResponse<RecommendWeightVO> commonResponse = new CommonResponse<>();
            commonResponse.setCode(rpcResponse.getCode());
            commonResponse.setMessage(rpcResponse.getMsg());

            if (Objects.nonNull(rpcResponse.getWeightForUnit())) {
                RecommendWeightVO recommendWeightVO = new RecommendWeightVO();
                recommendWeightVO.setWeightForUnit(BigDecimal.valueOf(rpcResponse.getWeightForUnit()).toString());
                recommendWeightVO.setWeightUnit(rpcResponse.getWeighUnit());
                commonResponse.setData(recommendWeightVO);
            }
            return commonResponse;
        } catch (Exception e) {
            log.error("ChannelSpuController.queryRecommendWeight, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "获取商品质量详情",
            description = "查询商品的推荐总量",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询商品的推荐总量"
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/querySpuQualityInfo",
            returnValueDescription = "详见Result",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @PostMapping("/querySpuQualityInfo")
    CommonResponse<CalSpuQualityResultDetailVo> querySpuQualityInfo(@RequestBody SpuQualityInfoRequest request) {
        try {
            Preconditions.checkArgument(StringUtils.isNotBlank(request.getName()), "商品名称必填");
            CalSpuQualityDetailInfoResponse rpcResponse = channelProductQueryThriftService.calSpuQualityDetailInfo(request.convertRpcRequest());

            if (rpcResponse.getCode() == null ) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取商品质量分失败，请稍后再试");
            }
            CommonResponse<CalSpuQualityResultDetailVo> commonResponse = new CommonResponse<>();
            commonResponse.setCode(rpcResponse.getCode());
            commonResponse.setMessage(rpcResponse.getMsg());
            commonResponse.setData(CalSpuQualityResultDetailVo.of(rpcResponse.getSpuQualityResultDetail()));
            return commonResponse;
        } catch (Exception e) {
            log.error("ChannelSpuController.querySpuQualityInfo, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "类目临期推荐规则查询",
            description = "类目临期推荐规则查询",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "类目临期推荐规则请求"
                    )
            },
            restExampleUrl = "/pieapi/ocms/channel/spu/queryChannelAdventRule",
            returnValueDescription = "详见Result",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：正常login用户"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @PostMapping("/queryChannelAdventRule")
    CommonResponse<List<AdventRuleVo>> queryChannelAdventRule(@RequestBody ChannelAdventRuleRequest request) {
        try {
            List<AdventRuleVo> ruleVoList = channelSpuWrapper.queryChannelAdventRule(request);
            return CommonResponse.success(ruleVoList);
        } catch (Exception e) {
            log.error("ChannelSpuController.getExpiredRule, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }
}
