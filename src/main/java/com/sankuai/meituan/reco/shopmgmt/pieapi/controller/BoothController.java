package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiBoothModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.AllStoreRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.booth.BoothOperateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.booth.BoothSearchCodeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth.BoothMngVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth.BoothSettingVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth.SearchCodeCheckResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.BoothWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/10/14 14:44
 * Description: 蔬果派摊位相关Controller
 */
@InterfaceDoc(
        displayName = "摊位服务",
        type = "restful",
        scenarios = "摊位服务",
        description = "摊位服务",
        host = "https://pieapi.empower.shangou.meituan.com/"
)
@Slf4j
@Api(value = "摊位服务")
@RestController
@RequestMapping("/pieapi/booth")
public class BoothController {

    @Autowired
    private BoothWrapper boothWrapper;

    @MethodDoc(
            description = "创建摊位",
            displayName = "创建摊位",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "摊位操作请求",
                            type = BoothOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/booth/create",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse create(@Valid @RequestBody BoothOperateRequest request) {
        try {
            request.baseValidate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            boothWrapper.boothCreate(user.getTenantId(), user.getAccountId(), user.getAccountName(), request);
            return CommonResponse.success(null);
        } catch (ParamException e) {
            log.info("摊位创建失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (CommonRuntimeException e) {
            log.info("摊位创建失败", e);
            return CommonResponse.fail(e.getResultCode() != null ? e.getResultCode() : ResultCode.FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("摊位创建失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }




    @MethodDoc(
            description = "获取摊位配置接口",
            displayName = "获取摊位配置接口",
            parameters = {
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/booth/setting",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/setting", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<BoothSettingVO> canManageBooth() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Integer boothMode = boothWrapper.getBoothMode(identityInfo.getUser().getTenantId(), identityInfo.getStoreId());
        return CommonResponse.success(BoothSettingVO.builder()
                .canManageBooth(ConverterUtils.nonNullConvert(boothMode, m -> m.equals(PoiBoothModeEnum.POI_WITH_SEVERAL_BOOTHS.getType()))).build());
    }



    @MethodDoc(
            description = "编辑摊位",
            displayName = "编辑摊位",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "摊位操作请求",
                            type = BoothOperateRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": null \n" +
                    "}",
            restExampleUrl = "/pieapi/booth/modify",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse modify(@Valid @RequestBody BoothOperateRequest request) {
        try {
            request.baseValidate();
            if (Objects.isNull(request.getBoothId())) {
                throw new ParamException("摊位Id不能为空");
            }
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            boothWrapper.boothModify(user.getTenantId(), user.getAccountId(), user.getAccountName(), request);
            return CommonResponse.success(null);
        } catch (ParamException e) {
            log.info("摊位编辑失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (CommonRuntimeException e) {
            log.info("摊位编辑失败", e);
            return CommonResponse.fail(e.getResultCode() != null ? e.getResultCode() : ResultCode.FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("摊位编辑失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "查询门店的摊位",
            displayName = "查询门店的摊位",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询门店的摊位",
                            type = AllStoreRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": [{\"boothId\":1,\"boothName\":\"name\",\"depId\":1,\"poiId\":1,\"boothPickingType\":1," +
                                "\"onSiteAlipayAccount\":\"xxx\",\"quickSearchCode\":\"xxx\",\"searchCodeBindTime\":\"2020-01-01 00:00:00\"}] \n" +
                    "}",
            restExampleUrl = "/pieapi/booth/poiBooths",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: 参数storeId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/poiBooths", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<List<BoothMngVO>> poiBooths(@Valid @RequestBody AllStoreRequest request) {
        try {
            if (Objects.isNull(request.getStoreId())) {
                throw new ParamException("门店Id无效");
            }
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            Long poiId = Long.valueOf(request.getStoreId());
            List<BoothMngVO> poiBooths = boothWrapper.queryPoiBooths(user.getTenantId(), poiId);
            return CommonResponse.success(poiBooths);
        } catch (ParamException e) {
            log.info("查询门店摊位失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (CommonRuntimeException e) {
            log.info("查询门店摊位失败", e);
            return CommonResponse.fail(e.getResultCode() != null ? e.getResultCode().getCode() : ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询门店的摊位失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "检查摊位检索码是否可用",
            displayName = "检查摊位检索码是否可用",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查摊位检索码是否可用",
                            type = BoothSearchCodeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\"isPass\":true,\"errorMsg\":null,} \n" +
                    "}",
            restExampleUrl = "/pieapi/booth/checkSearchCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: poiId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/checkSearchCode", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<SearchCodeCheckResultVO> checkSearchCode(@Valid @RequestBody BoothSearchCodeRequest request) {
        try {
            request.baseValidate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            SearchCodeCheckResultVO checkResult = boothWrapper.checkSearchCode(user.getTenantId(), request.getPoiId(), request.getBoothSearchCode());
            return CommonResponse.success(checkResult);
        } catch (ParamException e) {
            log.info("检查摊位检索码失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (CommonRuntimeException e) {
            log.info("检查摊位检索码失败", e);
            return CommonResponse.fail(e.getResultCode() != null ? e.getResultCode().getCode() : ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("检查摊位检索码失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

    @MethodDoc(
            description = "根据摊位检索码查询门店摊位",
            displayName = "根据摊位检索码查询门店摊位",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据摊位检索码查询门店摊位",
                            type = BoothSearchCodeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": null,\n" +
                    "    \"data\": {\"boothId\":1,\"boothName\":\"name\",\"depId\":1,\"poiId\":1,\"boothPickingType\":1," +
                                    "\"onSiteAlipayAccount\":\"xxx\",\"quickSearchCode\":\"xxx\",\"searchCodeBindTime\":\"2020-01-01 00:00:00\"} \n" +
                    "}",
            restExampleUrl = "/pieapi/booth/queryBySearchCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑: poiId,用户只能访问拥有权限的门店"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "etoken", required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryBySearchCode", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    public CommonResponse<BoothMngVO> queryBySearchCode(@Valid @RequestBody BoothSearchCodeRequest request) {
        try {
            request.baseValidate();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            BoothMngVO boothMngVO = boothWrapper.queryBySearchCode(user.getTenantId(), request.getPoiId(), request.getBoothSearchCode());
            return CommonResponse.success(boothMngVO);
        } catch (ParamException e) {
            log.info("根据摊位检索码查询门店摊位", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getErrMsg());
        } catch (CommonRuntimeException e) {
            log.info("根据摊位检索码查询门店摊位", e);
            return CommonResponse.fail(e.getResultCode() != null ? e.getResultCode().getCode() : ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("根据摊位检索码查询门店摊位", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage() == null ? "系统内部错误" : e.getMessage());
        }
    }

}
