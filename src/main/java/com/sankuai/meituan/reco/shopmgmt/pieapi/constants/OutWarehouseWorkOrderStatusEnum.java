package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;


import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.fulfill.constants.Status;

import java.util.Arrays;
import java.util.Map;

/**
 * 调拨出库单工单状态枚举
 */
public enum OutWarehouseWorkOrderStatusEnum {

    /**
     * 初始状态
     * 20220803-【拣货V3.8】移动调拨波次拣货需求规定：调拨出库单初始状态为待集单状态
     */
    INITIAL(0),

    /**
     * 集单中
     */
    IN_COLLECTING(2),

    /**
     * 待锁定库存
     */
    WAIT_TO_LOCK_STOCK(6),

    /**
     * 库存锁定成功
     */
    STOCK_LOCK_SUCCEED(8),

    /**
     * 库存锁定失败
     */
    STOCK_LOCK_FAILED(11),

    /**
     * 待拣货
     */
    WAIT_FOR_PICK(12),

    /**
     * 拣货中
     */
    PICKING(14),

    /**
     * 待出库
     */
    WAIT_FOR_OUT(16),

    /**
     * 出库成功
     */
    STOCK_OUT_SUCCEED(100),

    /**
     * 已取消
     */
    OUT_ABORT(102)

    ;

    private final int code;

    OutWarehouseWorkOrderStatusEnum(int code) {
        this.code = code;
    }

    public int getcode() {
        return code;
    }

}
