package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ListResultData;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplierSkuInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.TaxRateOptionQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.TenantChannelSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ProductBizServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantProductWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 16:07
 */
@InterfaceDoc(
        displayName = "OCMS 租户商品SPU相关接口",
        type = "restful",
        scenarios = "OCMS 租户商品SPU相关接口",
        description = "OCMS 租户商品SPU相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "OCMS 租户商品SPU相关接口")
@RestController
@RequestMapping("/pieapi/ocms/tenant/spu")
public class TenantSpuController {
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;
    @Autowired
    private TenantProductWrapper tenantProductWrapper;
    @Autowired
    private ProductBizServiceWrapper productBizServiceWrapper;


    @MethodDoc(
            description = "商品名称联想查询分页查询接口(菜大全)",
            displayName = "商品名称联想查询分页查询接口(菜大全)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品名称联想查询分页查询接口"
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"suggestSpuList\": \"[]\"" +
                    "    \"pageInfo\":\"\"" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTenantRegionSpuByName", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<QueryTenantRegionSpuByNameResponseVO> queryTenantRegionSpuByName(@Valid @RequestBody QueryTenantRegionSpuByNameRequest request) {
        return ocmsServiceWrapper.queryTenantRegionSpuByName(request);
    }

    @MethodDoc(
            description = "快捷创建商品搜索接口，同时返回商品池和标品库",
            displayName = "快捷创建商品搜索接口，同时返回商品池和标品库",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "快捷创建商品搜索接口"
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"suggestSpuList\": \"[]\"" +
                    "    \"pageInfo\":\"\"" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryForFastCreate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<MutliSourceQueryVo> queryForFastCreate(@Valid @RequestBody ProductQuery4FastCreateSpuRequest request) {
        try {
            List<BaseSearchVO> productList = tenantProductWrapper.queryForFastCreate(request);
            MutliSourceQueryVo queryVo = new MutliSourceQueryVo();
            queryVo.setList(productList);
            return CommonResponse.success(queryVo);
        } catch (IllegalArgumentException | IllegalStateException e) {
            log.warn("业务校验失败", e);
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("系统异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询异常，请稍后重试");
        }
    }

    @MethodDoc(
            description = "租户商品规格查询接口",
            displayName = "租户商品规格查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = QueryTenantSkuBySpuIdApiRequest.class,
                            description = "租户商品规格查询接口请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryTenantSkuBySpuId",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTenantSkuBySpuId", method = RequestMethod.POST)
    @ResponseBody
    @Deprecated
    public CommonResponse<List<TenantSkuVO>> queryTenantSkuBySpuId(@Valid @RequestBody QueryTenantSkuBySpuIdApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryTenantSkuBySpuId(request, user);
    }


    @MethodDoc(
            description = "租户商品规格查询接口，带spu信息",
            displayName = "租户商品规格查询接口，带spu信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = QueryTenantSkuBySpuIdApiRequest.class,
                            description = "租户商品规格查询接口请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryTenantSku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTenantSku", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSpuSimpleVO> queryTenantSku(@Valid @RequestBody QueryTenantSkuBySpuIdApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryTenantSku(request, user);
    }

    @MethodDoc(
            description = "加盟租户商品规格查询接口，带spu信息",
            displayName = "加盟租户商品规格查询接口，带spu信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = QueryFranchiseTenantSkuBySpuIdApiRequest.class,
                            description = "租户商品规格查询接口请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/store/spu/franchise/queryTenantSku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/franchise/queryTenantSku", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSpuSimpleVO> queryFranchiseTenantSku(@Valid @RequestBody QueryFranchiseTenantSkuBySpuIdApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return productBizServiceWrapper.queryFranchiseTenantSku(request, user);
    }

    @MethodDoc(
            description = "新增租户SKU（新增规格）带返回",
            displayName = "新增租户SKU（新增规格）带返回",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = AddTenantSkuApiRequest.class,
                            description = "新增租户SKU（新增规格）请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/addSku",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/addSku", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSkuVO> addSku(@Valid @RequestBody AddTenantSkuApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.addSku(request, user);
    }

    @MethodDoc(
            description = "根据spuId查询商品信息（名称和图片若城市商品存在，则返回城市的；反之，返回租户的）",
            displayName = "根据spuId查询商品信息（名称和图片若城市商品存在，则返回城市的；反之，返回租户的）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品名称联想查询分页查询接口"
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"suggestSpuList\": \"[]\"" +
                    "    \"pageInfo\":\"\"" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getTenantRegionSpuInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantRegionSpuVO> getTenantRegionSpuInfo(@Valid @RequestBody GetTenantSpuBySpuIdRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.getTenantRegionSpuInfo(request, user);
    }

    @MethodDoc(
            description = "根据spuId租户id以及重量查询相似规格",
            displayName = "根据spuId租户id以及重量查询相似规格",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品名称联想查询分页查询接口"
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"suggestSpuList\": \"[]\"" +
                    "    \"pageInfo\":\"\"" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/findTenantSimilarSku", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<TenantSkuVO>> findTenantSimilarSku(@Valid @RequestBody FindTenantSimilarSkuRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.findSimilarSpec(user.getTenantId(), request);
    }


    @MethodDoc(
            description = "租户spu数据预览接口",
            displayName = "租户spu数据预览接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "商品名称联想查询分页查询接口"
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"suggestSpuList\": \"[]\"" +
                    "    \"pageInfo\":\"\"" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/tenantSpuPreview", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSpuPreviewVO> tenantSpuPreview(@Valid @RequestBody TenantSpuPreviewRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.tenantSpuPreview(user.getTenantId(), request.getSpuId(), request.getTypeList());
    }

    @MethodDoc(
            displayName = "查询总部渠道所有规格包括删除规格",
            description = "查询总部渠道所有规格包括删除规格",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "导出全国商品池列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见TenantSpuVO",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/tenant/spu/queryChannelSkuList",
            restExamplePostData = "{\"type\":1,\"skuName\":\"测试商品\"}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":\"\"}"
    )
    @Auth
    @ApiOperation(value = "查询总部渠道所有规格包括删除规格")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/queryChannelSkuList", method = {RequestMethod.POST})
    public CommonResponse<List<TenantChannelSkuVO>> queryChannelSkuList(@Valid @RequestBody QueryTenantSkuBySpuIdApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryChannelSkuList(request, user);
    }

    @MethodDoc(
            description = "租户商品spu分页查询接口",
            displayName = "租户商品spu分页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = TenantSpuPageQueryApiRequest.class,
                            description = "租户商品spu分页查询请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryTenantSpuList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTenantSpuList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSpuPageQueryResponseVO> pageQuery(@Valid @RequestBody TenantSpuPageQueryApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryTenantSpuList(request, user);
    }

    @MethodDoc(
            description = "获取商品主档异常详情",
            displayName = "获取商品主档异常详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = TenantSpuPageQueryApiRequest.class,
                            description = "获取商品主档异常详情请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/abnormal/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/abnormal/list", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<ProductAbnormalVO> getSpuAbnormalList(@Valid @RequestBody QueryTenantSpuAbnormalRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.getSpuAbnormalList(request, user);
    }

    @MethodDoc(
            description = "获取租户商品详情接口",
            displayName = "获取租户商品详情接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = TenantSpuInfoApiRequest.class,
                            description = "获取租户商品详情请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryTenantSpuInfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryTenantSpuInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSpuVO> queryTenantSpuInfo(@Valid @RequestBody TenantSpuInfoApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryTenantSpuInfo(request, user);
    }

    @MethodDoc(
            description = "新增或更新租户商品接口",
            displayName = "新增或更新租户商品接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = TenantSpuAddOrUpdateApiRequest.class,
                            description = "新增或更新租户商品请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/addOrUpdateTenantSpu",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/addOrUpdateTenantSpu", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<String> addOrUpdateTenantSpu(@Valid @RequestBody TenantSpuAddOrUpdateApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        boolean isEditSpu = StringUtils.equals("edit", request.getSaveType());
        if (isEditSpu) {
            return ocmsServiceWrapper.updateTenantSpu(request, user);
        } else {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "没有新建商品功能");
        }
    }

    @MethodDoc(
            description = "删除租户商品接口",
            displayName = "删除租户商品接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = TenantSpuDeleteApiRequest.class,
                            description = "删除租户商品请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/deleteTenantSpu",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/deleteTenantSpu", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse deleteTenantSpu(@Valid @RequestBody TenantSpuDeleteApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.deleteTenantSpu(request, user);
    }

    @MethodDoc(
            description = "查询筛选项商品数量接口（无图 & 不可售）",
            displayName = "查询筛选项商品数量接口（无图 & 不可售）",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = TenantSpuQueryFilterCountApiRequest.class,
                            description = "查询筛选项商品数量接口请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryFilterTenantSpuCount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryFilterTenantSpuCount", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<TenantSpuQueryFilterCountResponseVO> queryFilterTenantSpuCount(@Valid @RequestBody TenantSpuQueryFilterCountApiRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return ocmsServiceWrapper.queryFilterTenantSpuCount(request, user);
    }

    @MethodDoc(
            description = "查询重量单位选项列表",
            displayName = "查询重量单位选项列表",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":[{\"unitName\":\"磅\","
                    + "\"weightToGramRatio\":454,\"minValue\":1,\"maxValue\":440,\"allowFloatScale\":0}]}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryWeightUnitOptionList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @GetMapping(value = "/queryWeightUnitOptionList")
    public CommonResponse<List<WeightUnitConfigVO>> queryWeightUnitOptionList() {
        return tenantProductWrapper.queryWeightUnitOptionList();
    }

    @MethodDoc(
            description = "查询箱规单位选项列表",
            displayName = "查询箱规单位选项列表",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":[{\"unitName\":\"磅\","
                    + "\"weightToGramRatio\":454,\"minValue\":1,\"maxValue\":440,\"allowFloatScale\":0}]}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/listPurchaseUnit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/listPurchaseUnit", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<String>> queryPurchaseUnitList() {
        return tenantProductWrapper.queryPurchaseUnitList();
    }

    @MethodDoc(
            displayName = "查询税率选项列表，如进项税率，销项税率",
            description = "查询税率选项列表，如进项税率，销项税率",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询税率枚举列表，如进项税率，销项税率"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见TaxRateOptionQueryVO",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryTaxRateOptionList",
            restExamplePostData = "{\"queryType\":1}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":\"\"}"
    )
    @ApiOperation(value = "查询税率选项列表")
    @MethodLog(logRequest = true, logResponse = true)
    @PostMapping(value = "/queryTaxRateOptionList")
    public CommonResponse<TaxRateOptionQueryVO> queryTaxRateOptionList(@RequestBody TaxRateOptionQueryRequest request) {
        return tenantProductWrapper.queryTaxRateOptionList(request.getQueryType());
    }

    @MethodDoc(
            displayName = "查询供货商规格信息",
            description = "查询供货商规格信息",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "错误信息",
            restExampleUrl = "/pieapi/ocms/tenant/spu/supplierSkuInfo"
    )
    @ApiOperation(value = "查询供货商规格信息")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/supplierSkuInfo", method = {RequestMethod.POST})
    public CommonResponse<ListResultData<SupplierSkuInfoVO>> querySupplierSkuInfo(@RequestBody SupplierSkuInfoQueryRequest request) {
        request.selfCheck();
        return productBizServiceWrapper.querySupplierSkuInfo(request);
    }

    @MethodDoc(
            displayName = "查询大松鼠租户ID",
            description = "查询大松鼠租户ID",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "错误信息",
            restExampleUrl = "/pieapi/ocms/tenant/spu/bigSquirrelTenantId"
    )
    @ApiOperation(value = "查询大松鼠租户ID")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/bigSquirrelTenantId", method = {RequestMethod.POST})
    public CommonResponse<Long> queryBigSquirrelTenantId() {
        return CommonResponse.success(MccConfigUtil.getBigSquirrelTenantId());
    }


    @MethodDoc(
            displayName = "查询所有异常类型列表接口",
            description = "查询所有异常类型列表接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询所有异常类型列表接口请求参数",
                            type = QueryStoreSpuAbnormalRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "允许为空",
                            requiredness = Requiredness.OPTIONAL,
                            example = {}
                    )
            },
            restExampleUrl = "/pieapi/ocms/tenant/spu/getAllAbnormalTypeList",
            returnValueDescription = "待补充",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getAllAbnormalTypeList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<AbnormalTypeQueryResponseVO> getAllAbnormalTypeList(@RequestBody(required = false) QueryAllAbnormalTypeListRequest request) {
        return productBizServiceWrapper.getAllAbnormalTypeList(request);
    }

    @MethodDoc(
            description = "获取医药标品库商品详情接口",
            displayName = "获取医药标品库商品详情接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            type = MedicalStandardSpuQueryRequest.class,
                            description = "获取医药标品库商品详情请求参数"
                    )
            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": \"[]\"" +
                    "}",
            restExampleUrl = "/pieapi/ocms/tenant/spu/queryMedicalStandardSpu",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryMedicalStandardSpu", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<List<MedicalStandardSpuAndControlFieldsVO>> queryMedicalStandardSpu(@RequestBody MedicalStandardSpuQueryRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return productBizServiceWrapper.queryMedicalStandardSpuInfo(request, user);
    }

    @MethodDoc(
            description = "歪马融合灰度接口",
            displayName = "歪马融合灰度接口",

            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"msg\": \"成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"skuSaleTypeDisplay\":true\n" +
                    "    }\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            },
            restExampleUrl = "/pieapi/ocms/tenant/spu/merged"

    )
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/merged", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Map<String, Boolean>> getMergedStatus() {
        long tenantId = SessionContext.getCurrentSession().getTenantId();
        boolean waimaMergedTenant = MccConfigUtil.isWaimaMergedTenant(tenantId);
        Map<String, Boolean> restMap = new HashMap<>();
        restMap.put("skuSaleTypeDisplay", waimaMergedTenant);
        return CommonResponse.success(restMap);
    }
}
