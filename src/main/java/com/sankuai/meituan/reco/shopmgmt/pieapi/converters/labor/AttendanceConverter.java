package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.labor;

import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.constants.ApprovalActivityStatusEnum;
import com.sankuai.drunkhorsemgmt.labor.constants.AttendanceCheckinTypeEnum;
import com.sankuai.drunkhorsemgmt.labor.constants.AttendanceResultStatusEnum;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.dto.AttendanceApprovalBaseInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.*;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.LaborAttendanceCheckinReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryAttendanceCalendarReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.CheckinRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryAttendanceStatisticsRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceStatisticsVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryCheckinInfoResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.LocationStoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.RequestContextUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/10/19 20:12
 **/
public class AttendanceConverter {

    public static QueryCheckinInfoResp buildQueryCheckinInfoResp(AttendanceCheckinInfoDTO dto, AttendanceApprovalBaseInfoDTO infoDTO, String employeeName) {
        QueryCheckinInfoResp resp = new QueryCheckinInfoResp();
        resp.setEmployeeName(employeeName);
        resp.setCheckinType(dto.getCheckinEventType());
        resp.setLocationInfos(locationDtoToVo(dto.getScheduleRuleLocationDTOS()));
        resp.setBeLateTimePoint(dto.getBeLateTimePoint());
        resp.setLeaveEarlyTimePoint(dto.getLeaveEarlyTimePoint());
        //填充加班申请信息
        if (infoDTO != null) {
            resp.setExtraWorkAttendanceApplyId(infoDTO.getId());
            resp.setExtraWorkAttendanceApplyStatus(infoDTO.getApprovalStatus());
        }
        return resp;
    }

    public static LaborAttendanceCheckinReq buildLaborAttendanceCheckinReq(CheckinRequest req, IdentityInfo identityInfo) {
        LaborAttendanceCheckinReq request = new LaborAttendanceCheckinReq();
        request.setCheckinTimeStamp(System.currentTimeMillis());
        request.setCheckinType(AttendanceCheckinTypeEnum.GPS.getType());
        request.setEmployeeId(identityInfo.getUser().getEmployeeId());
        request.setTenantId(identityInfo.getUser().getTenantId());
        request.setUserIp(RequestContextUtils.getClientIp());
        request.setLatitude(req.getLatitude());
        request.setLongitude(req.getLongitude());
        request.setDeviceUuid(ApiMethodParamThreadLocal.getIdentityInfo().getUuid());
        if(CollectionUtils.isNotEmpty(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList())){
            request.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().get(0));
        }
        return request;
    }

    public static QueryAttendanceCalendarReq buildQueryAttendanceCalendarReq(QueryAttendanceStatisticsRequest req,
                                                                       IdentityInfo identityInfo) {
        QueryAttendanceCalendarReq request = new QueryAttendanceCalendarReq();
        request.setBeginDate(req.getBeginDate());
        request.setEndDate(req.getEndDate());
        request.setEmployeeId(identityInfo.getUser().getEmployeeId());
        if (req.getNeedCheckinJsonContent() != null && req.getNeedCheckinJsonContent() == 1) {
            request.setNeedCheckinJsonContent(true);
        } else {
            request.setNeedCheckinJsonContent(false);
        }
        return request;
    }

    public static List<AttendanceStatisticsVO> buildAttendanceStatisticsVOList(List<AttendanceCalendarDTO> attendanceCalendarDTOS,
                                                                               Map<LocalDate, AttendanceApprovalBaseInfoDTO> infoDTOMap) {
        if (CollectionUtils.isEmpty(attendanceCalendarDTOS)) {
            attendanceCalendarDTOS = Collections.emptyList();
        }

        List<AttendanceStatisticsVO> voList = attendanceCalendarDTOS.stream()
                .map(dto -> AttendanceConverter.toAttendanceStatisticsVO(dto,
                        infoDTOMap.get(TimeUtils.stringFormatToLocalDate(dto.getScheduleDate()))))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //填充额外加班申请
        Set<String> dateSet = voList.stream().map(AttendanceStatisticsVO::getDate).collect(Collectors.toSet());
        if (MapUtils.isNotEmpty(infoDTOMap)) {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            Set<Map.Entry<LocalDate, AttendanceApprovalBaseInfoDTO>> entrySet = infoDTOMap.entrySet().stream().filter(entry -> !dateSet.contains(entry.getKey().format(df))).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(entrySet)) {
                for (Map.Entry<LocalDate, AttendanceApprovalBaseInfoDTO> entry : entrySet) {
                    AttendanceStatisticsVO vo = new AttendanceStatisticsVO();
                    vo.setDate(entry.getKey().format(df));
                    //加班申请默认都是正常
                    vo.setIsAbnormal(false);
                    vo.setExtraWorkAttendanceApplyId(entry.getValue().getId());
                    vo.setExtraWorkAttendanceApplyStatus(entry.getValue().getApprovalStatus());
                    vo.setIsSettled(dateSet.contains(entry.getKey().format(df)));
                    //昨天以前的一定已经结算
                    if (LocalDate.now().minusDays(1).isAfter(entry.getKey())) {
                        vo.setIsSettled(true);
                    } else if (LocalDate.now().minusDays(1).equals(entry.getKey())) {
                        //昨天看有没有结算记录
                        vo.setIsSettled(dateSet.contains(entry.getKey().format(df)));
                    } else {  //昨天以后的一定没结算
                        vo.setIsSettled(false);
                    }
                    voList.add(vo);
                }
            }
        }
        return voList;
    }

    public static AttendanceStatisticsVO toAttendanceStatisticsVO(AttendanceCalendarDTO attendanceCalendarDTO,
                                                                  AttendanceApprovalBaseInfoDTO baseInfoDTO) {
        if (Objects.isNull(attendanceCalendarDTO)) {
            return null;
        }
        AttendanceStatisticsVO statisticsVO = new AttendanceStatisticsVO();
        statisticsVO.setDate(attendanceCalendarDTO.getScheduleDate());
        statisticsVO.setIsSettled(attendanceCalendarDTO.getIsSettled());
        statisticsVO.setAttendanceResults(toAttendanceResultVOs(attendanceCalendarDTO.getAttendanceResultStatisticsDTO()));
        statisticsVO.setIsAbnormal(judgeAttendanceIsAbnormal(attendanceCalendarDTO.getAttendanceResultStatisticsDTO()));
        if (baseInfoDTO != null) {
            statisticsVO.setExtraWorkAttendanceApplyId(baseInfoDTO.getId());
            statisticsVO.setExtraWorkAttendanceApplyStatus(baseInfoDTO.getApprovalStatus());
            if (baseInfoDTO.getApprovalStatus() == ApprovalActivityStatusEnum.APPROVED.getStatus()) {
                statisticsVO.setIsAbnormal(false);
            }
        }
        return statisticsVO;
    }

    public static List<AttendanceResultVO> toAttendanceResultVOs(List<AttendanceResultStatisticsDTO> attendanceResultDTOS) {
        if (CollectionUtils.isEmpty(attendanceResultDTOS)) {
            return Collections.emptyList();
        }

        return attendanceResultDTOS.stream()
                .map(AttendanceConverter::toAttendanceResultVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static AttendanceResultVO toAttendanceResultVO(AttendanceResultStatisticsDTO attendanceResultDTO) {

        if (Objects.isNull(attendanceResultDTO)) {
            return null;
        }

        AttendanceResultVO vo = new AttendanceResultVO();

        vo.setStatisticsId(attendanceResultDTO.getId());
        vo.setStoreId(attendanceResultDTO.getStoreId());

        ShiftDTO scheduleShiftDTO = attendanceResultDTO.getScheduleShiftDTO();
        if (Objects.nonNull(scheduleShiftDTO)) {
            vo.setScheduleName(scheduleShiftDTO.getName());
            vo.setStartWorkTime(scheduleShiftDTO.getStartWorkTime());
            vo.setStartWorkDurationBeginTime(scheduleShiftDTO.getStartWorkCheckInDurationBegin());
            vo.setStartWorkDurationEndTime(scheduleShiftDTO.getStartWorkCheckInDurationEnd());

            vo.setEndWorkTime(scheduleShiftDTO.getEndWorkTime());
            vo.setEndWorkDurationBeginTime(scheduleShiftDTO.getEndWorkCheckInDurationBegin());
            vo.setEndWorkDurationEndTime(scheduleShiftDTO.getEndWorkCheckInDurationEnd());
        }

        vo.setStartWorkCheckInAppealId(attendanceResultDTO.getStartWorkAppealId());
        vo.setStartWorkCheckInTime(Optional.ofNullable(attendanceResultDTO.getStartWorkCheckinTime())
                .map(TimeUtils::convertTimeStamp2HMStr)
                .orElse(""));
        vo.setStartWorkCheckInExceptions(Optional.ofNullable(attendanceResultDTO.getStartWorkAttendanceExceptions())
                .orElse(Collections.emptyList()));


        vo.setEndWorkCheckInAppealId(attendanceResultDTO.getEndWorkAppealId());
        vo.setEndWorkCheckInTime(Optional.ofNullable(attendanceResultDTO.getEndWorkCheckinTime())
                .map(TimeUtils::convertTimeStamp2HMStr)
                .orElse(""));
        vo.setEndWorkCheckInExceptions(Optional.ofNullable(attendanceResultDTO.getEndWorkAttendanceExceptions())
                .orElse(Collections.emptyList()));

        vo.setStartWorkAttendanceStatus(attendanceResultDTO.getStartWorkAttendanceStatus());
        vo.setEndWorkAttendanceStatus(attendanceResultDTO.getEndWorkAttendanceStatus());

        if (attendanceResultDTO.getStartWorkCheckinJsonDTO() != null) {
            vo.setStartWorkCheckInPhotoUrl(attendanceResultDTO.getStartWorkCheckinJsonDTO().getPhotoUrl());
        }
        if (attendanceResultDTO.getEndWorkCheckinJsonDTO() != null) {
            vo.setEndWorkCheckInPhotoUrl(attendanceResultDTO.getEndWorkCheckinJsonDTO().getPhotoUrl());
        }
        vo.setBeLateExceptionDuration(attendanceResultDTO.getBeLateExceptionDuration());
        vo.setLeaveEarlyExceptionDuration(attendanceResultDTO.getLeaveEarlyExceptionDuration());
        return vo;
    }

    private static Boolean judgeAttendanceIsAbnormal(List<AttendanceResultStatisticsDTO> attendanceResultDTOS) {
        if (CollectionUtils.isEmpty(attendanceResultDTOS)) {
            return false;
        }

        return attendanceResultDTOS.stream().anyMatch(AttendanceConverter::judgeAttendanceIsAbnormal);
    }

    private static Boolean judgeAttendanceIsAbnormal(AttendanceResultStatisticsDTO attendanceResultStatisticsDTO) {
        return judgeAttendanceStatusIsAbnormal(attendanceResultStatisticsDTO.getStartWorkAttendanceStatus())
                || judgeAttendanceStatusIsAbnormal(attendanceResultStatisticsDTO.getEndWorkAttendanceStatus());
    }

    private static Boolean judgeAttendanceStatusIsAbnormal(Integer attendanceStatus) {
        return Objects.nonNull(attendanceStatus)
                && attendanceStatus != AttendanceResultStatusEnum.NORMAL.getStatus()
                && attendanceStatus != AttendanceResultStatusEnum.APPEAL_PASSED.getStatus()
                && attendanceStatus != AttendanceResultStatusEnum.OFF_WORK.getStatus()
                && attendanceStatus != AttendanceResultStatusEnum.PUBLIC_HOLIDAY.getStatus()
                && attendanceStatus != AttendanceResultStatusEnum.PERSONAL_HOLIDAY.getStatus()
                && attendanceStatus != AttendanceResultStatusEnum.UNSCHEDULED.getStatus();

    }

    private static List<LocationStoreVO> locationDtoToVo(List<ScheduleRuleLocationDTO> dtoList) {
        return Optional
                .ofNullable(dtoList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            LocationStoreVO vo = new LocationStoreVO();
                            vo.setStoreId(dto.getStoreId());
                            vo.setStoreName(dto.getStoreName());
                            vo.setLongitude(dto.getLongitude());
                            vo.setLatitude(dto.getLatitude());
                            vo.setDescription(dto.getDescription());
                            vo.setAllowCheckinRange(dto.getAllowCheckinRange());
                            return vo;
                        }
                ).collect(Collectors.toList());
    }
}
