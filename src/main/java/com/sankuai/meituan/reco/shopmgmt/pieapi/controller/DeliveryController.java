package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DeliveryChannelEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.RiderOperateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryChannelPreLaunchResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryPreLaunchResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/11
 */
@InterfaceDoc(
		displayName = "配送相关接口",
		type = "restful",
		scenarios = "包含配送相关的查询和操作接口",
		description = "包含配送相关的查询和操作接口",
		host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "配送服务")
@RestController
@RequestMapping("/pieapi/delivery")
public class DeliveryController {

	@Resource
	private TmsServiceWrapper tmsServiceWrapper;

	@MethodDoc(
			displayName = "预发配送",
			description = "查询可配送渠道+询价",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "预发配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/pre-launch-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@Auth
	@ApiOperation(value = "预发配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@ResponseBody
	@PostMapping("/pre-launch-delivery")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<DeliveryPreLaunchResponse> preLaunch(@Valid @RequestBody DeliveryPreLaunchRequest request) {
		return CommonResponse.success(new DeliveryPreLaunchResponse(
				tmsServiceWrapper.preLaunchDelivery(request.getStoreId(), request.getOrderId())
						.stream()
						.map(it -> new DeliveryChannelPreLaunchResponse(
								it.getDeliveryChannelId(),
								it.getDeliveryChannelName(),
								// 配送渠道图标
								Optional.of(DeliveryChannelEnum.findByChannelCode(it.getDeliveryChannelId()))
										.map(DeliveryChannelEnum::getChannelIcon).orElse(null),
								it.getServicePackage(),
								it.getAvailable(),
								it.getFailReason(),
								it.getEstimatedDeliveryFee(),
								it.getDiscountAmount()
						))
						.collect(Collectors.toList())
		));
	}

	@MethodDoc(
			displayName = "发起三方配送",
			description = "根据预发配送的结果，选择一个渠道，发起三方配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "发起三方配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/launch-third-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@Auth
	@ApiOperation(value = "发起三方配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@ResponseBody
	@PostMapping("/launch-third-delivery")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> launchDelivery(@Valid @RequestBody DeliveryLaunchRequest request) {
		tmsServiceWrapper.launchDelivery(
				request.getStoreId(),
				request.getOrderId(),
				request.getDeliveryChannelId(),
				request.getServicePackage(),
				request.getEstimatedDeliveryFee()
		);
		return CommonResponse.success(null);
	}

	@MethodDoc(
			displayName = "发送黑盒渠道三方配送",
			description = "发送黑盒渠道配送三方配送，重发三方配送等场景",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "发送黑盒渠道三方配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/launch-third-delivery-withoutchannel",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@Auth
	@ApiOperation(value = "发送黑盒渠道三方配送")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@ResponseBody
	@PostMapping("/launch-third-delivery-withoutchannel")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> launchDeliveryWithoutChannel(@Valid @RequestBody DeliveryLaunchWithoutChannelRequest request) {
		try {
			tmsServiceWrapper.launchWithOutChannelDelivery(
					request.getStoreId(),
					request.getOrderId()
			);
			return CommonResponse.success(null);
		} catch (CommonRuntimeException e) {
			log.warn("DeliveryController.launchDeliveryWithoutChannel fail.", e);
			return CommonResponse.fail(e.getResultCode(), e.getMessage());
		} catch (Exception e) {
			log.error("DeliveryController.launchDeliveryWithoutChannel error.", e);
			return CommonResponse.fail(ResultCode.FAIL);
		}
	}

	@MethodDoc(
			displayName = "发送海葵三方配送",
			description = "发送黑盒渠道配送三方配送，重发三方配送等场景",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "发送黑盒渠道三方配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/launch-hai-kui",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@Auth
	@ApiOperation(value = "发送海葵三方配送")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@ResponseBody
	@PostMapping("/launch-hai-kui")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> launchHaiKui(@Valid @RequestBody HaiKuiDeliveryLaunchRequest request) {
		tmsServiceWrapper.launchDelivery(
				request.getStoreId(),
				request.getOrderId(),
				900,
				StringUtils.EMPTY,
				0.0
		);
		return CommonResponse.success(null);
	}



	@MethodDoc(
			displayName = "平台配送转三方配送",
			description = "平台配送异常，用户手动发起转三方配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "平台配送转三方配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/turn-to-third-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@Auth
	@ApiOperation(value = "平台配送转三方配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@ResponseBody
	@PostMapping("/turn-to-third-delivery")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> turnToThirdDelivery(@Valid @RequestBody TurnToThirdDeliveryRequest request) {
		tmsServiceWrapper.turnToThirdDelivery(
				request.getStoreId(),
				request.getOrderId(),
				request.getDeliveryChannelId(),
				request.getServicePackage(),
				request.getEstimatedDeliveryFee()
		);
		return CommonResponse.success(null);
	}

	@MethodDoc(
			displayName = "平台配送转商家自配送",
			description = "平台配送异常，用户手动发起转商家自配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "平台配送转商家自配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/turn-to-merchant-self-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@Auth
	@ApiOperation(value = "平台配送转三方配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@ResponseBody
	@PostMapping("/turn-to-merchant-self-delivery")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> turnToMerchantSelfDelivery(@Valid @RequestBody TurnToMerchantSelfDeliveryRequest request) {
		tmsServiceWrapper.turnToMerchantSelfDelivery(request.getStoreId(), request.getOrderId());
		return CommonResponse.success(null);
	}

	@MethodDoc(
			displayName = "平台配送转商家自配送",
			description = "平台配送异常，用户手动发起转商家自配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "平台配送转商家自配送请求",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/turn-to-merchant-self-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@Auth
	@ApiOperation(value = "取消配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@ResponseBody
	@PostMapping("/cancel")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> cancelDelivery(@Valid @RequestBody CancelDeliveryRequest request) {
		tmsServiceWrapper.cancelDelivery(request.getStoreId(), request.getOrderId());
		return CommonResponse.success(null);
	}

	@MethodDoc(
			displayName = "转三方配送",
			description = "转三方配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "转三方配送",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/turn-to-agg-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@Auth
	@ApiOperation(value = "取消配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@ResponseBody
	@PostMapping("/turn-to-agg-delivery")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> turnToAggregationDelivery(@Valid @RequestBody TurnToAggDeliveryRequest request) {
		try {
			return tmsServiceWrapper.turnToAggregationDelivery(request.getStoreId(), request.getOrderId(),request.getPlatformId());
		} catch (Exception e) {
			log.error("turnToAggregationDelivery failed", e);
			return CommonResponse.fail(ResultCode.FAIL, "转单失败，请稍后再试");
		}
	}

	@MethodDoc(
			displayName = "手动发起平台配送",
			description = "手动发起平台配送",
			parameters = {
					@ParamDoc(
							name = "request",
							description = "手动发起平台配送",
							type = HttpServletRequest.class,
							paramType = ParamType.REQUEST_BODY,
							rule = "非空",
							requiredness = Requiredness.REQUIRED
					)

			},
			restExampleResponseData = "",
			restExampleUrl = "/pieapi/delivery/manual-launch-platform-delivery",
			extensions = {
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE",
							content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
					),
					@ExtensionDoc(
							name = "SECURITY_PRIVILEGE_VERIFY",
							content = "True"
					),
			}
	)
	@Auth
	@ApiOperation(value = "手动发配送接口")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
			@ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
	})
	@DataSecurity({
			@SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
	})
	@ResponseBody
	@PostMapping("/manual-launch-platform-delivery")
	@MethodLog(logResponse = true, logRequest = true)
	public CommonResponse<Void> manualLaunchPlatformDelivery(@Valid @RequestBody ManualLaunchPlatformDeliveryRequest request) {
		try {
			return tmsServiceWrapper.manualLaunchPlatformDelivery(request.getStoreId(), request.getOrderId());
		} catch (Exception e) {
			log.error("manualLaunchPlatformDelivery failed", e);
			return CommonResponse.fail(ResultCode.FAIL, "手动发平台配送失败，请稍后再试");
		}
	}

}
