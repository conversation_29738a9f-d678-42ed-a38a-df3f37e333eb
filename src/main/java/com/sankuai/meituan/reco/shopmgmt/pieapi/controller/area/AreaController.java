package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.area;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.area.request.QuerySingleLevelAreaRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.area.vo.SingleAreaVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.SingleAreaBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.area.AreaService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.meituan.linz.thrift.response.Status;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QuerySingleLevelAreaInfoRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QuerySingleLevelAreaResponse;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/20 11:28
 **/
@InterfaceDoc(
        displayName = "地区相关接口",
        type = "restful",
        scenarios = "地区相关接口",
        description = "地区相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "地区相关接口")
@RestController
@RequestMapping("/pieapi/common/area")
public class AreaController {

    @Autowired
    private AreaService areaService;


    @MethodDoc(
            displayName = "查询单一层级地区接口",
            description = "查询单一层级地区接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询单一层级地区请求",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "不需要鉴权")
            },
            returnValueDescription = "",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/pieapi/common/area/querySingleLevelArea",
            restExamplePostData = "选择上传文件",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功导入100个会员\"}"
    )
    @Auth
    @ApiOperation(value = "查询单一层级地区")
    @MethodLog(logResponse = true, logger = "http")
    @PostMapping(value = "/querySingleLevelArea")
    public CommonResponse<List<SingleAreaVo>> querySingleLevelArea(@RequestBody QuerySingleLevelAreaRequest request){
        List<SingleAreaBo> singleAreaBoList = areaService.querySingleLevelArea(request);
        return CommonResponse.success(SingleAreaVo.ofBO(singleAreaBoList));
    }
}
