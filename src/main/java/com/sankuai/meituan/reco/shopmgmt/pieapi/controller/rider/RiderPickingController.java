package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.alibaba.fastjson.JSON;
import com.meituan.image.client.pojo.ImageResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.image.vo.ImageUploadResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking.QueryPromotePickOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.RiderDeliveryOrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.PickConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.RiderPickFinishResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.PickImageServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.osw.OSWServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.dianping.cat.Cat;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking.QueryOrderPickingDetailsRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking.RiderFinishPickingRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.RiderPickingDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderPickingServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

/**
 * 骑手拣货 Controller.
 *
 * <AUTHOR>
 * @since 2021/11/12 14:23
 */
@InterfaceDoc(
        displayName = "拣配一体，骑手拣货相关接口",
        type = "restful",
        scenarios = "包含骑手查询拣货任务、操作拣货完成等接口",
        description = "包含骑手查询拣货任务、操作拣货完成等接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "拣配一体，骑手拣货相关接口")
@RestController
@RequestMapping("/pieapi/rider/picking")
public class RiderPickingController {

    /**
     * 歪马扫码拣货埋点 TYPE
     */
    private final static String DH_UPC_SCAN_PICK = "DH_UPC_SCAN_PICK";

    @Resource
    private RiderPickingServiceWrapper riderPickingService;

    @Resource
    private PickImageServiceWrapper pickImageServiceWrapper;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @MethodDoc(
            displayName = "查询骑手配送单的待拣货内容详情",
            description = "查询骑手配送单的待拣货内容详情",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询骑手已领取订单的待拣货内容的请求",
                            type = QueryOrderPickingDetailsRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/waitpickdetail",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询骑手配送单的待拣货内容详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/waitpickdetail", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<RiderPickingDetailVO> queryOrderWaitPickDetails(@Valid @RequestBody QueryOrderPickingDetailsRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), validateResult.get());
        }
        return riderPickingService.queryWaitPickDetail(request.getChannelId(), request.getChannelOrderId(), request.getDeliveryOrderId());
    }

    @MethodDoc(
            displayName = "骑手操作拣货完成",
            description = "骑手操作拣货完成",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手操作拣货完成的请求",
                            type = RiderFinishPickingRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/finish",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "骑手操作拣货完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/finish", method = {RequestMethod.POST})
    @ResponseBody
    @Deprecated
    public CommonResponse<Void> finishPicking(@RequestBody RiderFinishPickingRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            throw new CommonRuntimeException(validateResult.get(), ResultCode.PARAM_ERR);
        }
        try {
            riderPickingService.finishPicking(request);
            Cat.logEvent(DH_UPC_SCAN_PICK, "ORDER_NUM");
            if (request.getUpcScanErrorOccurred()) {
                // 埋点记录：拣货过程中出现 UPC 识别为非订单内商品的订单数
                Cat.logEvent(DH_UPC_SCAN_PICK, "SCAN_ERROR_NUM");
            }
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.finishPicking fail.", e);
            if (e.getResultCode() != null) {
                return CommonResponse.fail(e.getResultCode(), e.getMessage());
            } else {
                return CommonResponse.fail(ResultCode.FAIL, e.getMessage());
            }
        } catch (Exception e) {
            log.error("RiderDeliveryController.finishPicking error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }

    }

    @MethodDoc(
            displayName = "骑手操作拣货完成",
            description = "骑手操作拣货完成",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "骑手操作拣货完成的请求",
                            type = RiderFinishPickingRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/finishV2",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "骑手操作拣货完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/finishV2", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<RiderPickFinishResponse> finishPickingV2(@RequestBody RiderFinishPickingRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            throw new CommonRuntimeException(validateResult.get(), ResultCode.PARAM_ERR);
        }
        try {
            List<String> invalidSnCodes = riderPickingService.finishPickingV2(request);
            Cat.logEvent(DH_UPC_SCAN_PICK, "ORDER_NUM");
            if (request.getUpcScanErrorOccurred()) {
                // 埋点记录：拣货过程中出现 UPC 识别为非订单内商品的订单数
                Cat.logEvent(DH_UPC_SCAN_PICK, "SCAN_ERROR_NUM");
            }

            if (CollectionUtils.isNotEmpty(invalidSnCodes)) {
                return CommonResponse.fail(ResultCode.SN_CODE_NOT_VALID.getCode(), ResultCode.SN_CODE_NOT_VALID.getDefaultMessage(),
                        new RiderPickFinishResponse(invalidSnCodes));
            }

            return CommonResponse.success(null);
        } catch (CommonLogicException e) {
            log.warn("RiderDeliveryController.finishPicking logic error.", e);
            return CommonResponse.fail(e.getResultCode().getCode(), e.getMessage(), new RiderPickFinishResponse(Collections.emptyList()));
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.finishPicking fail.", e);
            if (e.getResultCode() != null) {
                return CommonResponse.fail(e.getResultCode().getCode(), e.getMessage(), new RiderPickFinishResponse(Collections.emptyList()));
            } else {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage(), new RiderPickFinishResponse(Collections.emptyList()));
            }
        } catch (Exception e) {
            log.error("RiderDeliveryController.finishPicking error.", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.FAIL.getDefaultMessage(),
                    new RiderPickFinishResponse(Collections.emptyList()));
        }
    }

    @MethodDoc(
            displayName = "上传拣货图片",
            description = "上传拣货图片，依赖集团内部的图片服务，内部有特殊的降级逻辑,勿复用",
            parameters = {
                    @ParamDoc(
                            name = "file",
                            description = "图片",
                            paramType = ParamType.REQUEST_BODY
                    )
            },
            returnValueDescription = "图片上传结果",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"data\": {\n" +
                    "    \"fileUrl\": \"图片url\"" +
                    "  },\n" +
                    "  \"msg\": \"\"\n" +
                    "}",
            restExampleUrl = "/pieapi/rider/picking/uploadPickImage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验token，判断当前用户是否登录，是否有权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "FALSE"
                    ),
            }
    )
    @RequestMapping(value = "/uploadPickImage", method = RequestMethod.POST)
    @MethodLog(logResponse = true)
    @Auth
    public CommonResponse<ImageUploadResultVO> uploadImage(@RequestParam("file") CommonsMultipartFile file) {
        try {
            //上传图片
            byte[] content = file.getBytes();
            String originalImageLink = pickImageServiceWrapper.uploadImage(content, file.getOriginalFilename());

            //对图片加水印
            if (MccConfigUtil.isWatermarkSwitchGrayStore(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId())) {
                String watermarkedImageLink = pickImageServiceWrapper.watermark(originalImageLink);
                return CommonResponse.success(new ImageUploadResultVO(watermarkedImageLink));
            }

            return CommonResponse.success(new ImageUploadResultVO(originalImageLink));
        } catch (BizException e) {
            log.error("上传图片错误", e);
            Cat.logEvent("UPLOAD_PICK_IMAGE", "BIZ_ERROR");
            return CommonResponse.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("上传图片错误", e);
            Cat.logEvent("UPLOAD_PICK_IMAGE", "SYS_ERROR");
            return CommonResponse.fail(ResultCode.FAIL.getCode(),"上传图片错误");
        }
    }


    @MethodDoc(
            displayName = "查询地推自提的订单",
            description = "查询地推自提的订单",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询地推自提的订单",
                            type = QueryOrderPickingDetailsRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/querypromoteorder",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询地推自提的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querypromoteorder", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<OrderListResponse> queryPromoteOrder(@Valid @RequestBody QueryPromotePickOrderRequest request) {
        Optional<String> validateResult = request.validate();
        if (validateResult.isPresent()) {
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), validateResult.get());
        }

        try {
            OrderListResponse orderListResponse = riderPickingService.queryRiderOfflinePromoteOrder(request);
            return CommonResponse.success(orderListResponse);
        } catch (CommonRuntimeException e) {
            log.error("invoke queryPromoteOrder error", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("invoke queryPromoteOrder error", e);
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(), ResultCode.INTERNAL_SERVER_ERROR.getDefaultMessage());
        }
    }


    @MethodDoc(
            displayName = "查询拣货配置",
            description = "查询拣货配置",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询拣货配置",
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/picking/config",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "查询拣货配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/config", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse<PickConfigVO> queryPickingConfig() {
        return CommonResponse.success(riderPickingService.queryPickingConfig());
    }
}
