package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
public interface ProjectConstants {

    String TOKEN = CommonConstant.AUTHORIZATION_HEADER;
    String E_TOKEN = CommonConstant.E_TOKEN_HEADER;
    String UUID = "uuid";
    String APPID = "appId";
    String OS = "os";
    String APPVERSION = "appVersion";
    String JSVERSION = "jsVersion";
    String STORE_ID = "storeId";
    String MRN_APP = "mrnApp";
    String MRN_VERSION = "mrnVersion";
    String APP_CHANNEL = "appChannel";
    String AUTH_ID = "authId";
    String SUB_AUTH_ID = "subAuthId";
    String ENTITY_TYPE = "entityType";

    /**
     * 全部门店时，Header 中 storeId 值为 -1
     */
    String FULL_STORE_ID = "-1";

    /**
     * 线下渠道
     */
    int OFFLINE_CHANNEL_ID = -1;

    /**
     * 重量500g
     */
    int WEIGHT_500G = 500;

    String SPU_GRAY_KEY = "SPU_GRAY";

    String SPU_GRAY_SWITCH_ON = "1";

    int CHANNEL_SUPPORT = ChannelTypeEnum.MEITUAN.getChannelId();

    String RETAIL_PRICE_MIGRATE_SWITCH = "retailPriceMigrateSwitch";
    String OFFLINE_PRICE_MIGRATE_SWITCH = "offlinePriceMigrateSwitch";
    String PREMIUM_RATE_MIGRATE_SWITCH = "premiumRateMigrateSwitch";
    String PRICE_INDEX_MIGRATE_SWITCH = "priceIndexMigrateSwitch";

    /**
     * 不一致统计Code
     */
    String UN_SALE_COUNT = "unsaleCnt";
    String INCONSISTENT_SPU_COUNT = "inconsistentSpuCnt";
    String STOP_SALE_COUNT = "stopSaleCnt";
    String AUDITING_COUNT = "auditingCnt";
    String PLATFORM_SOLD_OUT_COUNT = "platformSoldOutCnt";

    /**
     * 美团审核异常（含审核驳回和审核中）
     */
    String MT_AUDIT_ABNORMAL_COUNT = "mtAuditAbnormalCnt";
    /**
     * 美团平台下架（含平台下架和平台停售）
     */
    String MT_PLATFORM_UN_SALE_COUNT = "mtPlatformUnSaleCnt";
    /**
     * 京东平台下架（含平台下架和平台停售）
     */
    String JDDJ_PLATFORM_UN_SALE_COUNT = "jddjPlatformUnSaleCnt";

    /**
     * 同步异常
     */
    String MT_SYNC_ABNORMAL_COUNT = "mtSyncAbnormalCnt";

    String EBLS_SYNC_ABNORMAL_COUNT = "eblsSyncAbnormalCnt";

    String JDDJ_SYNC_ABNORMAL_COUNT = "jddjSyncAbnormalCnt";

    String YZ_SYNC_ABNORMAL_COUNT = "yzSyncAbnormalCnt";

    String TXD_SYNC_ABNORMAL_COUNT = "txdSyncAbnormalCnt";

    String DY_SYNC_ABNORMAL_COUNT = "dySyncAbnormalCnt";

    /**
     * 门店系统的APP TYPE定义为1，用于区分功能权限
     * */
    int APP_TYPE = 1;

    int BATCH_LIMIT_20_AMOUNT = 20;
}
