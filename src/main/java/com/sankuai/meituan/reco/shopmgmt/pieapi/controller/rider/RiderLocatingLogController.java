package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.QueryRiderWaitToGetOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating.BatchPostLocatingLogRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.RiderDeliveryOrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderDeliveryServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating.PostLocatingExceptionRequest;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 上报骑手定位日志 Controller
 * <AUTHOR>
 * @Date 2022/3/7
 */

@InterfaceDoc(
        displayName = "骑手定位日志上报相关接口",
        type = "restful",
        scenarios = "包含上报骑手定位异常、批量上报骑手定位日志等接口",
        description = "包含上报骑手定位异常、批量上报骑手定位日志等接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "上报骑手定位日志相关接口")
@RestController
@RequestMapping("/pieapi/rider/locatingLog")
public class RiderLocatingLogController {
    @Resource
    private RiderDeliveryServiceWrapper riderDeliveryService;


    @MethodDoc(
            displayName = "上报骑手定位异常",
            description = "上报骑手定位异常",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "上报骑手定位异常请求",
                            type = PostLocatingExceptionRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/locatingLog/postLocatingException",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "上报骑手定位异常")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/postLocatingException", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> postLocatingException(@Valid @RequestBody PostLocatingExceptionRequest request) {
        try {
            riderDeliveryService.riderLocatingException(request);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.postLocatingException fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.postLocatingException error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }


    @MethodDoc(
            displayName = "批量上报定位日志(正常日志+异常日志)",
            description = "批量上报定位日志(正常日志+异常日志)",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "批量上报定位日志请求",
                            type = BatchPostLocatingLogRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/rider/locatingLog/batchPostLocatingLog",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "False"
                    ),
            }
    )
    @ApiOperation(value = "批量上报定位日志（正常日志+异常日志）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/batchPostLocatingLog", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<Void> batchPostLocatingLog(@Valid @RequestBody BatchPostLocatingLogRequest request) {
        try {
            riderDeliveryService.batchPostRiderLocatingLog(request);
            return CommonResponse.success(null);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryController.batchPostLocatingLog fail.", e);
            return CommonResponse.fail(e.getResultCode(), e.getMessage());
        } catch (Exception e) {
            log.error("RiderDeliveryController.batchPostLocatingLog error.", e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

}
