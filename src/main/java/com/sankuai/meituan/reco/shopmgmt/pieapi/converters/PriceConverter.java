package com.sankuai.meituan.reco.shopmgmt.pieapi.converters;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PresentChannelPriceNotEqualSignPageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PresentChannelPriceNotEqualSignVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelStoreSkuKeyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.shangou.empower.price.client.response.present_price.QueryNotEqualSignResponse;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ROUND_HALF_UP;

public class PriceConverter {
    /**
     * 金额的小数点位数
     */
    private static final int PRICE_SCALE = 2;
    private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
    private static final BigDecimal ONE_THOUSAND = new BigDecimal("1000");

    public static CommonResponse<PresentChannelPriceNotEqualSignPageVO> convert2NotEqualSignPageVO(
            QueryNotEqualSignResponse response, Map<StoreSkuKey, StoreSkuBaseDetailVO> skuDetailMap) {
        PageInfoVO pageInfo = new PageInfoVO().buildPageInfoVO(response.getPageInfo());
        PresentChannelPriceNotEqualSignPageVO signPageVO = PresentChannelPriceNotEqualSignPageVO.builder()
                .pageInfo(pageInfo)
                .build();
        if (CollectionUtils.isEmpty(response.getPriceNotEqualSignSkuList())) {
            signPageVO.setNotEqualSignSkus(Lists.newArrayList());
            return CommonResponse.success(signPageVO);
        }

        List<PresentChannelPriceNotEqualSignVO> signVOS = response.getPriceNotEqualSignSkuList().parallelStream().map(dto -> {
            ChannelStoreSkuKeyVO skuKey = ChannelStoreSkuKeyVO.builder()
                    .channelId(dto.getSkuKey().getChannelId())
                    .storeId(dto.getSkuKey().getStoreId())
                    .skuId(dto.getSkuKey().getSkuId())
                    .build();

            PresentChannelPriceNotEqualSignVO vo = PresentChannelPriceNotEqualSignVO.builder()
                    .channelId(skuKey.getChannelId())
                    .storeId(skuKey.getStoreId())
                    .skuId(skuKey.getSkuId())
                    .atPromotion(dto.getAtPromotion())
                    .notEqualSignTime(dto.getPriceEqualSignUpdateTime())
                    .reviewingChannelPrice(smaller100Times(dto.getReviewingChannelPrice()))
                    .presentChannelPrice(smaller100Times(dto.getPresentPrice()))
                    .channelPrice(smaller100Times(dto.getOcmsOnlinePrice()))
                    .status(dto.getStatus())
                    .tagList(dto.getTagList())
                    .build();

            StoreSkuBaseDetailVO skuBaseDetailVO = skuDetailMap.get(vo.genStoreSkuKey());
            if (Objects.nonNull(skuBaseDetailVO)) {
                vo.setSpuId(skuBaseDetailVO.getSpuId());
                vo.setStoreName(skuBaseDetailVO.getStoreName());
                vo.setSkuName(skuBaseDetailVO.getSkuName());
                vo.setSpec(skuBaseDetailVO.getSpec());
                vo.setImages(skuBaseDetailVO.getImageList());
            }
            return vo;
        }).collect(Collectors.toList());
        signPageVO.setNotEqualSignSkus(signVOS);

        return CommonResponse.success(signPageVO);
    }

    public static String smaller100Times(Long value) {
        if (value == null) {
            return "";
        }
        return BigDecimal.valueOf(value).divide(new BigDecimal(100), 2, RoundingMode.DOWN).toString();
    }

    public static String formatMoney(double money) {
        return new DecimalFormat("0.00").format(money);
    }


    /**
     * BigDecimal厘转元工具
     */
    public static String calcDivide1000PriceValue(BigDecimal price) {
        return Optional.ofNullable(price).map(p -> p.divide(ONE_THOUSAND, PRICE_SCALE, ROUND_HALF_UP)
                .stripTrailingZeros().toPlainString()).orElse(BigDecimal.ZERO.toPlainString());
    }

    /**
     * BigDecimal分转元工具
     */
    public static String calcDivide100PriceValue(Long price) {
        return Optional.ofNullable(price).map(p -> BigDecimal.valueOf(price).divide(ONE_HUNDRED, PRICE_SCALE, ROUND_HALF_UP)
                .stripTrailingZeros().toPlainString()).orElse(BigDecimal.ZERO.toPlainString());
    }
}
