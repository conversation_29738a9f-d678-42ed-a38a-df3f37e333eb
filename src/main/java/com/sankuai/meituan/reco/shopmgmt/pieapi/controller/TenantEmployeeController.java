package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.cat.util.StringUtils;
import com.google.common.base.Splitter;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ServiceType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.AppAuthIdConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.TenantEmployeeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.AllStoreRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.StoreRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OuterEmployeeDeviceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.UserPushConfigWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@InterfaceDoc(
        displayName = "租户门店推送消息绑定",
        type = "restful",
        scenarios = "租户门店员工绑定/切换/解绑设备接口,当租户和设备绑定，用户和设备绑定时可以收到对应的推送信息，解绑后就不再收到对应信息",
        description = "租户门店员工绑定/切换/解绑设备接口,当消息推送时，用户和设备绑定时可以收到对应的推送信息",
        host = "https://pieapi-empower.meituan.com/"
)
@Api(value = "租户门店员工绑定/切换/解绑设备接口")
@RestController
@RequestMapping("/pieapi/employee/device")
public class TenantEmployeeController {

    @Resource
    private OuterEmployeeDeviceWrapper outerEmployeeDeviceWrapper;

    @Resource
    private UserPushConfigWrapper userPushConfigWrapper;

    @MethodDoc(
            description = "选择/切换门店并绑定设备（支持全部门店）",
            displayName = "选择/切换门店并绑定设备（支持全部门店）",
            parameters = {
                    @ParamDoc(
                            name = "store",
                            description = "门店请求",
                            type = StoreRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "成功",
            restExamplePostData = "{\n" +
                    "  \"storeId\": 1\n" +
                    "}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": null\n" +
                    "}",
            restExampleUrl = "/pieapi/employee/device/batch-confirm",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @Auth
    @PostMapping(value = "/batch-confirm")
    @ApiOperation(value = "选择/切换门店并绑定设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "authId", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<Void> batchStoreConfirm(@RequestBody @Valid AllStoreRequest store) {
        ServiceType serviceType = ServiceType.SHU_GUO_PAI;
        userPushConfigWrapper.bindUserDevice(String.valueOf(AppAuthIdConstants.SHU_GUO_PAI.val()),
                ApiMethodParamThreadLocal.getIdentityInfo().getUuid(),
                ApiMethodParamThreadLocal.getIdentityInfo().getUser());
        return outerEmployeeDeviceWrapper.batchSaveOrUpdateTenantEmployeeDevice(
                obtainRequest(store.getStoreId()), ApiMethodParamThreadLocal.getIdentityInfo().getUuid(), serviceType
        );
    }

    @MethodDoc(
            description = "解绑设备，根据header里面uuid解绑设备",
            displayName = "解绑设备",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "解绑设备",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            returnValueDescription = "{}",
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": null\n" +
                    "}",
            restExampleUrl = "/pieapi/employee/device/cancel",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
            }
    )
    @PostMapping("/cancel")
    @ApiOperation(value = "解绑设备")
    @ApiImplicitParams({
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "authId", required = true, paramType = "header", dataType = "string")
    })
    public CommonResponse<Void> unbindDevice(HttpServletRequest request) {
        ServiceType serviceType = ServiceType.SHU_GUO_PAI;
        userPushConfigWrapper.unbindUserDevice(String.valueOf(AppAuthIdConstants.SHU_GUO_PAI.val()), ApiMethodParamThreadLocal.getIdentityInfo().getUuid());
        return outerEmployeeDeviceWrapper.unbindDevice(request.getHeader("uuid"), serviceType);
    }

    private TenantEmployeeDto obtainRequest(long storeId) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TenantEmployeeDto request = new TenantEmployeeDto();
        request.setStoreId(storeId);
        request.setTenantId(user.getTenantId());
        request.setStaffId(String.valueOf(user.getAccountId()));
        return request;
    }

    @SuppressWarnings("UnstableApiUsage")
    private List<TenantEmployeeDto> obtainRequest(String storeIdStr) {
        if (StringUtils.isEmpty(storeIdStr)) {
            return Lists.newArrayList();
        }
        return Splitter.on(",").trimResults()
                .omitEmptyStrings().splitToList(storeIdStr)
                .stream()
                .map(id -> obtainRequest(Long.valueOf(id)))
                .collect(Collectors.toList());
    }
}
