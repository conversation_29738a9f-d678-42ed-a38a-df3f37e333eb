package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable;

import com.dianping.lion.client.Lion;
import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.ConsumableOutService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@InterfaceDoc(
        displayName = "耗材相关接口",
        type = "restful",
        scenarios = "耗材相关接口",
        description = "耗材相关接口",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "耗材相关接口")
@RestController
@RequestMapping("/pieapi/consumable/")
public class ConsumableController {

    @Resource
    private ConsumableOutService consumable;

    @MethodDoc(
            displayName = "耗材随单出库接口",
            description = "耗材随单出库接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "耗材随单出库请求",
                            type = ConsumableOutRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/consumable/outWarehouse",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "耗材随单出库接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/outWarehouse", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<Void> consumableOut(@Valid @RequestBody ConsumableOutRequest request) throws Exception {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        //严格管库存的，不走其他出入库
        Set<String> needManageStockConsumableSkus = MccConfigUtil.getNeedManageStockConsumableItem().stream().map(ConsumableItemDetail::getSkuId).collect(Collectors.toSet());
        List<ConsumableItem> filteredConsumableItems = request.getItems().stream().filter(consumableItem -> !needManageStockConsumableSkus.contains(consumableItem.getSkuId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredConsumableItems)) {
            return CommonResponse.success(null);
        }
        request.setItems(filteredConsumableItems);

        request.valid(consumable.queryConsumableList(identityInfo.getStoreId()).getItems().stream()
                .collect(Collectors.toMap(ConsumableItemDetail::getSkuId, Function.identity())));
        consumable.consumableOut(request, identityInfo);
        return CommonResponse.success(null);
    }


    @MethodDoc(
            displayName = "耗材列表接口",
            description = "耗材列表接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "耗材列表请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/consumable/list",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @Auth
    @ApiOperation(value = "耗材列表接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/list", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<ConsumableItemResponse> consumableList() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        return CommonResponse.success(consumable.queryConsumableList(identityInfo.getStoreId()));
    }
}
