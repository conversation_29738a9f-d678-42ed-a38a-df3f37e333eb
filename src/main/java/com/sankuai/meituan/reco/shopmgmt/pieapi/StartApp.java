package com.sankuai.meituan.reco.shopmgmt.pieapi;

import com.dianping.pigeon.remoting.common.config.annotation.PigeonConfiguration;
import com.meituan.shangou.saas.order.platform.common.types.EnableDynamicChannelType;
import com.meituan.xframe.boot.XframeApplication;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.httpclient.FeignConfig;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.bootstrap.PrivateCloudBootstrap;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.bootstrap.PublicCloudBootstrap;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.annotation.EnableMonitor;
import com.sankuai.shangou.logistics.delivery.gray.config.GrayConfigConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportResource;


@Slf4j
@SpringBootConfiguration
@Import({PublicCloudBootstrap.class, PrivateCloudBootstrap.class, FeignConfig.class, GrayConfigConfiguration.class})
@ImportResource("classpath:/applicationContext.xml")
@ComponentScan(excludeFilters = {
        @ComponentScan.Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
        @ComponentScan.Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class) })
@EnableMonitor
@PigeonConfiguration
@EnableDynamicChannelType
public class StartApp {
    public static void main(String[] args) {
        XframeApplication.run(StartApp.class, args);
    }
}
