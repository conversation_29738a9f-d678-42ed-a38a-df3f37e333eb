package com.sankuai.meituan.reco.shopmgmt.pieapi.controller.assistanttask;

import java.util.List;
import java.util.Objects;

import com.meituan.shangou.sac.infrastructure.version.SimpleVersion;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.*;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.munich.assistant.client.response.task.SpuTaskListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant.SpuTaskPageVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.munich.assistant.client.to.task.SpuTaskTo;
import com.meituan.shangou.munich.assistant.client.to.task.TaskSummaryTo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.assistant.IgnoreTaskRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.assistant.TaskListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant.SpuTaskVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant.TaskSummaryVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.auth.Auth;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AssistantTaskWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/1/25 13:58
 */
@InterfaceDoc(
        type = "restful",
        displayName = "店长助手任务相关接口",
        description = "店长助手任务相关接口",
        scenarios = "店长助手任务相关接口",
        authors = {"liwei101"}
)
@Slf4j
@RestController
@RequestMapping("/pieapi/assistant/task")
public class AssistantTaskController {


    @Autowired
    private AssistantTaskWrapper assistantTaskWrapper;


    @MethodDoc(
            description = "查询店长助手任务汇总信息",
            displayName = "查询店长助手任务汇总信息",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/assistant/task/taskSummary",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiOperation(value = "查询店长助手任务汇总信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/taskSummary", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<List<TaskSummaryVo>> getTaskSummary() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<TaskSummaryTo> taskSummary =
                assistantTaskWrapper.getTaskSummary(identityInfo.getUser().getTenantId(), identityInfo.getStoreId());

        return CommonResponse.success(Fun.map(Fun.filter(taskSummary, t -> filterSecurityDepositTask(t, identityInfo)),
                AssistantTaskConverter::convertToVo));
    }

    private boolean filterSecurityDepositTask(TaskSummaryTo t, IdentityInfo identityInfo) {
        return Objects.equals(2, t.getStatus())
                || (isSupportSecurityJsVersion(identityInfo.getJsVersion()) || t.getTaskType() < TaskTypeEnum.SECURITY_DEPOSIT_UNPAID.code());
    }

    private boolean isSupportSecurityJsVersion(String jsVersion) {
        try {
            // 只有2.0.0及以上的版本才展示新的任务类型
            return SimpleVersion.isStrictGte(jsVersion, "2.0.0");
        }
        catch (Exception e) {
            log.error("parse jsversion error, jsVersion:{}", jsVersion, e);
        }

        return true;
    }


    @MethodDoc(
            description = "忽略店长助手任务",
            displayName = "忽略店长助手任务",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/assistant/task/ignoreTask",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "忽略店长助手任务请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiOperation(value = "忽略店长助手任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/ignoreTask", method = RequestMethod.POST)
    public CommonResponse ignoreTask(@RequestBody IgnoreTaskRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        assistantTaskWrapper.ignoreTask(identityInfo.getUser().getTenantId(), request.getTaskIds());
        return CommonResponse.success(null);
    }



    @MethodDoc(
            description = "店长助手任务列表",
            displayName = "店长助手任务列表",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/assistant/task/taskList",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "店长助手任务列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiOperation(value = "店长助手任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/taskList", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<List<SpuTaskVo>> queryTaskList(@RequestBody TaskListQueryRequest request) {
        request.validate();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<SpuTaskTo> spuTaskTos = assistantTaskWrapper.querySpuTaskList(identityInfo.getUser().getTenantId(),
                identityInfo.getStoreId(), request.getTaskType());
        List<SpuTaskVo> spuTaskVoList = Fun.map(spuTaskTos, AssistantTaskConverter::convertToSpuTaskVo);
        return CommonResponse.success(spuTaskVoList);
    }


    @MethodDoc(
            description = "店长助手任务列表",
            displayName = "店长助手任务列表",
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/assistant/task/taskListWithPage",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "店长助手任务列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),
            }
    )
    @Auth
    @ApiOperation(value = "店长助手任务列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/taskListWithPage", method = RequestMethod.POST)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    public CommonResponse<SpuTaskPageVo> queryTaskListWithPage(@RequestBody TaskListQueryRequest request) {
        request.validate();
        Bssert.throwIfTrue(Objects.isNull(request.getPage()) || Objects.isNull(request.getPageSize()) || request.getPageSize().equals(0),
                "分页信息不能为NULL");
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        SpuTaskListResponse response = assistantTaskWrapper.querySpuTaskListWithPage(identityInfo.getUser().getTenantId(),
                identityInfo.getStoreId(), request.getTaskType(), request.getPage(), request.getPageSize());
        List<SpuTaskVo> spuTaskVoList = Fun.map(response.getSkuTaskToList(), AssistantTaskConverter::convertToSpuTaskVo);
        SpuTaskPageVo spuTaskPageVo = new SpuTaskPageVo();
        spuTaskPageVo.setTaskInfo(spuTaskVoList);
        spuTaskPageVo.setPageInfo(AssistantTaskConverter.convertPageInfoVo(response.getPageInfo()));
        return CommonResponse.success(spuTaskPageVo);
    }
}
