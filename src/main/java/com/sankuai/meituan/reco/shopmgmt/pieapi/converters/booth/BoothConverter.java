package com.sankuai.meituan.reco.shopmgmt.pieapi.converters.booth;

import com.meituan.shangou.saas.tenant.thrift.common.enums.BoothOperateSourceEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.BoothPickingTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.BoothSettlementTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoWithSettleAccountsDto;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothSettleAccountsDto;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.request.BoothAddRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.request.BoothUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.booth.BoothOperateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth.BoothMngVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/10/14 17:40
 * Description: 摊位相关参数转换
 */
public class BoothConverter {

    public static BoothAddRequest toBoothAddRequest(long tenantId, long optId, String optName, long parentDepId,
                                                    BoothOperateRequest request) {
        BoothAddRequest addThriftRequest = new BoothAddRequest();
        addThriftRequest.setTenantId(tenantId);
        addThriftRequest.setPoiId(request.getPoiId());
        addThriftRequest.setBoothName(request.getName());
        // 蔬果派创建摊位默认使用银行卡结算模式
        addThriftRequest.setSettlementType(BoothSettlementTypeEnum.BANK_CARD.getKey());
        addThriftRequest.setParentDepartmentId(parentDepId);
        addThriftRequest.setOptUser(optName);
        // 蔬果派创建摊位时备货属性默认使用外采模式
        addThriftRequest.setPickingType(BoothPickingTypeEnum.OUTSOURCED_GOODS.getKey());
        addThriftRequest.setQuickSearchCode(request.getQuickSearchCode());
        addThriftRequest.setOptId(optId);
        addThriftRequest.setOperateSource(BoothOperateSourceEnum.SGP_APP.getCode());
        // 摊位结算方式
        BoothSettleAccountsDto boothSettleAccountsDto = generateBoothSettleDto(request);
        addThriftRequest.setBoothSettleAccountsDto(boothSettleAccountsDto);

        return addThriftRequest;
    }

    public static BoothUpdateRequest toBoothUpdateRequest(long tenantId, long optId, String optName, Long parentDepId,
                                                          BoothOperateRequest request) {
        BoothUpdateRequest updateThriftRequest = new BoothUpdateRequest();
        updateThriftRequest.setTenantId(tenantId);
        updateThriftRequest.setPoiId(request.getPoiId());
        updateThriftRequest.setBoothId(request.getBoothId());
        updateThriftRequest.setBoothName(request.getName());
        // 蔬果派编辑摊位时不编辑结算模式
        updateThriftRequest.setParentDepartmentId(parentDepId);
        updateThriftRequest.setOptUser(optName);
        updateThriftRequest.setPickingType(request.getBoothPickingType());
        updateThriftRequest.setQuickSearchCode(request.getQuickSearchCode());
        updateThriftRequest.setOptId(optId);
        updateThriftRequest.setOperateSource(BoothOperateSourceEnum.SGP_APP.getCode());
        // 摊位结算方式
        BoothSettleAccountsDto boothSettleAccountsDto = generateBoothSettleDto(request);
        updateThriftRequest.setBoothSettleAccountsDto(boothSettleAccountsDto);

        return updateThriftRequest;
    }

    public static BoothMngVO buildBoothMngVO(BoothInfoWithSettleAccountsDto boothWithSettleDto) {
        if (Objects.isNull(boothWithSettleDto)) {
            return null;
        }
        BoothMngVO boothMngVO = new BoothMngVO();
        boothMngVO.setBoothId(boothWithSettleDto.getBoothId());
        boothMngVO.setBoothName(boothWithSettleDto.getBoothName());
        boothMngVO.setDepId(boothWithSettleDto.getDepId());
        boothMngVO.setPoiId(boothWithSettleDto.getPoiId());
        boothMngVO.setBoothPickingType(boothWithSettleDto.getPickingType());
        boothMngVO.setOnSiteAlipayAccount(boothWithSettleDto.getBoothSettleAccountsDto() == null ? null :
                boothWithSettleDto.getBoothSettleAccountsDto().getOnSiteAlipayAccount());
        boothMngVO.setOnSiteAlipayRealname(boothWithSettleDto.getBoothSettleAccountsDto() == null ? null :
                boothWithSettleDto.getBoothSettleAccountsDto().getOnSiteAlipayRealname());
        boothMngVO.setQuickSearchCode(boothWithSettleDto.getQuickSearchCode());
        boothMngVO.setSearchCodeBindTime(boothWithSettleDto.getSearchCodeBindTime() == null ? null :
                DateUtils.format(new Date(boothWithSettleDto.getSearchCodeBindTime()), DateUtils.YYYY_MM_DD_HH_MM_SS));
        return boothMngVO;
    }

    private static BoothSettleAccountsDto generateBoothSettleDto(BoothOperateRequest request) {
        BoothSettleAccountsDto accountsDto = new BoothSettleAccountsDto();
        // App端新建的摊位，默认折扣比例为100%，编辑摊位时该字段在下游被屏蔽
        accountsDto.setBoothSettleRatio(100d);
        accountsDto.setOnSiteAlipayAccount(request.getOnSiteAlipayAccount());
        accountsDto.setOnSiteAlipayRealname(request.getOnSiteAlipayRealname());
        return accountsDto;
    }
}
