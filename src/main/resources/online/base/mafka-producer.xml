<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


        <bean id="consumableOutProducer" class="com.meituan.mafka.client.bean.MafkaProducer" init-method="start"
              destroy-method="close">
            <property name="namespace" value="waimai"/>
            <property name="appkey" value="${consumable.producer.appKey}"/>
            <property name="topic" value="pick_select_consumable_items"/>
            <property name="otherProperties">
                <map>
                    <entry key="producer.send.mode" value="async"/>
                </map>
            </property>
        </bean>

</beans>
