<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:rhino="http://code.dianping.com/schema/rhino"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://code.dianping.com/schema/rhino
           http://code.dianping.com/schema/rhino/rhino-1.0.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/aop
           http://www.springframework.org/schema/aop/spring-aop.xsd
           http://www.springframework.org/schema/util
           http://www.springframework.org/schema/util/spring-util.xsd">

    <context:component-scan base-package="com.sankuai.meituan.reco.shopmgmt.pieapi">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <rhino:annotation package="com.sankuai.meituan.reco.shopmgmt.pieapi"/>

    <bean id="properties" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="locations">
            <list>
                <!-- 公司级框架统一属性配置 -->
                <value>classpath:META-INF/app.properties</value>
                <!-- 项目自定义属性 -->
            </list>
        </property>
    </bean>

    <!-- 将ConfigUtilAdapter中的配置全部导到spring里面 -->
    <bean id="configUtilAdapterPlaceholderConfigurer" class="com.meituan.xframe.util.ConfigUtilAdapterPlaceholderConfigurer">
        <!-- https://wiki.sankuai.com/pages/viewpage.action?pageId=1252888869 -->
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="order">
            <util:constant static-field="org.springframework.core.Ordered.HIGHEST_PRECEDENCE"/>
        </property>
    </bean>

    <context:annotation-config/>
    <aop:aspectj-autoproxy/>

    <bean id="hlb" class="com.sankuai.inf.octo.mns.hlb.HttpServerPublisher" init-method="publish"
          destroy-method="destroy">
        <property name="appKey" value="com.sankuai.sgshopmgmt.empower.pieapi"/>
        <property name="port" value="${server.port}"/>
        <property name="configStatus"> <!-- 可不配置，默认initStatus=“DEAD”-->
            <bean class="com.sankuai.sgagent.thrift.model.ConfigStatus">
                <property name="initStatus" value="DEAD"/> <!-- DEAD(未启动)/ALIVE(正常)/STOPPED（禁用） -->
            </bean>
        </property>
    </bean>

    <bean id="multipartResolver"
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8" />
        <!-- 指定所上传文件的总大小不能超过200KB。注意maxUploadSize属性的限制不是针对单个文件，而是所有文件的容量之和 -->
        <property name="maxUploadSize" value="524288000" />
        <property name="maxInMemorySize" value="4096"></property>
    </bean>

    <bean id="ssoSecret" class="com.meituan.service.inf.kms.value.KMSStringValue">
        <property name="appKey" value="${app.name}" />
        <property name="name" value="sso.secret" />
        <property name="retryCount" value="10" />
    </bean>

    <bean id="mtSSOFilter" class="com.sankuai.it.sso.sdk.spring.FilterFactoryBean">
        <!-- 必须配置，以下二者需先到开放平台(http://open.sankuai.com)申请接入SSO后颁发，
             对应企平开放平台的AppKey和AppSecret -->
        <property name="clientId" value="${sso.clientId}"/>
        <property name="secret" ref="ssoSecret"/>

        <!-- 需要 SSO 检查的 Url 配置, 多个以逗号分隔，允许换行
             单独配 includedUriList，includedUriList 以外的链接都不检查sso登录
             单独配 excludedUriList，excludedUriList 以外的链接都会检查sso登录
             includedUriList，excludedUriList 都有的时候，仅includedUriList有效，匹配路径{**} -->
        <property name="includedUriList" value="/goldengateway/empower/common/dataset/**"/>

    </bean>
    <import resource="applicationContext-thrift-client.xml"/>
    <import resource="mafka-producer.xml"/>
    <!--区域规划-->
    <import resource="classpath:reco_region_select_client.xml"/>
</beans>
