<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <!-- thrift客户端线程池配置 -->
    <bean id="mtThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_min-idle', '10')}"/>
        <property name="maxWait" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('freego_bapi_thrift_client_pool_max-wait', '1000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <!-- tms配置化thrift客户端线程池配置 -->
    <bean id="tmsThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="100"/>
        <property name="maxIdle" value="20"/>
        <property name="minIdle" value="1"/>
        <property name="maxWait" value="5000"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>

    <bean id="pickSelectWarehouseThriftPoolConfig" class="com.meituan.service.mobile.mtthrift.client.pool.MTThriftPoolConfig">
        <property name="maxActive" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('pick_warehouse_thrift_client_pool_max-active', '100')}"/>
        <property name="maxIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('pick_warehouse_thrift_client_pool_max-idle', '20')}"/>
        <property name="minIdle" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('pick_warehouse_thrift_client_pool_min-idle', '5')}"/>
        <property name="maxWait" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('pick_warehouse_thrift_client_pool_max-wait', '5000')}"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="false"/>
    </bean>



    <bean id="empowerLoginThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
        destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.management.thrift.LoginThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.storeempower"/>
        <property name="remoteServerPort" value="8415"/>
        <property name="timeout" value="${mthrift.timeout}"/>
        <property name="connTimeout" value="${mthrift.conntimeout}"/>
    </bean>


    <bean id="outerEmployeeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.OuterEmployeeThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="remoteServerPort" value="8419"/>
        <property name="timeout" value="${mthrift.timeout}"/>
        <property name="connTimeout" value="${mthrift.conntimeout}"/>
    </bean>




    <bean id="userPushConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.UserPushConfigThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="remoteServerPort" value="8412"/>
        <property name="timeout" value="${mthrift.timeout}"/>
        <property name="connTimeout" value="${mthrift.conntimeout}"/>
    </bean>

    <import resource="classpath:/reco-store-saas-auth-thrift-client.xml"/>
    <import resource="classpath:/wio-thrift-client.xml"/>





    <bean id="appModuleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.AppModuleThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8845"/>
    </bean>


    <bean id="channelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8081"/>
        <property name="remoteUniProto" value="true"/>
    </bean>

    <bean id="boothThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.BoothThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8861"/>
    </bean>

    <bean id="confirmLetterThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.tenant.thrift.ConfirmLetterThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8892"/>
    </bean>

    <bean id="configThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.ConfigThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8846"/>
    </bean>

    <bean id="bizModuleThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.BizModuleThriftService"/>
        <property name="timeout" value="1000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8886"/>
    </bean>

    <!--履约配置服务-->
    <bean id="storeFulfillConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService"/>
        <property name="timeout" value="3000"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="remoteServerPort" value="8421"/>
    </bean>

    <!--履约门店信息服务-->
    <bean id="storeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreThriftService"/>
        <property name="timeout" value="3000"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="remoteServerPort" value="8411"/>
    </bean>

    <!--履约门店信息服务-->
    <bean id="ebaseEmployeeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.EmployeeThriftService"/>
        <property name="timeout" value="3000"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="remoteServerPort" value="8414"/>
    </bean>

    <bean id="bizOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.o2o.service.BizOrderThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="remoteServerPort" value="9005"/>
    </bean>

    <bean id="ocmsOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('freego.product.thrift.service.timeout',5000)}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="remoteServerPort" value="9012"/>
    </bean>


    <bean id="openPickThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService"/>
        <property name="timeout" value="1500"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="remoteServerPort" value="8421"/>
    </bean>

    <bean id="fulfillThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.fulfill.FulfillThriftService"/>
        <property name="timeout" value="1500"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="remoteServerPort" value="8428"/>
    </bean>

    <bean id="queryFulfillThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.FulfillThriftService"/>
        <property name="timeout" value="1500"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="remoteServerPort" value="8428"/>
    </bean>

    <bean id="pickSelectPrintQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.logic.thrift.print.PickSelectPrintQueryThriftService"/>
        <property name="timeout" value="1500"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectlogic"/>
        <property name="remoteServerPort" value="8423"/>
    </bean>

    <bean id="warehouseStateService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.WarehouseStateService"/>
        <property name="timeout" value="5000"/>
        <property name="connTimeout" value="500"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/><!-- 单端口多服务需要开起nettyIO -->
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.inventory"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="businessConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.BusinessConfigThriftService"/>
        <property name="timeout" value="2000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8899"/>
    </bean>

    <bean id="warehouseThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="pickSelectWarehouseThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.wave.WarehouseThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="remoteServerPort" value="8439"/>
    </bean>

    <bean id="allotOutWarehouseThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.AllotOutWarehouseThriftService"/>
        <property name="timeout" value="2000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="remoteServerPort" value="8436"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="refundAndBreakSkuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.management.thrift.refundandbreak.RefundAndBreakSkuThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.storeempower"/>
        <property name="remoteServerPort" value="8424"/>
        <property name="timeout" value="2000"/>
        <property name="connTimeout" value="3000"/>
    </bean>

    <bean id="outWarehouseWorkOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.OutWarehouseWorkOrderThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="remoteServerPort" value="8457"/>
        <property name="timeout" value="2000"/>
        <property name="connTimeout" value="3000"/>
    </bean>

    <bean id="wmsOtherStockTaskThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.wms.thrift.otherstock.OtherStockTaskThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="remoteServerPort" value="8449"/>
    </bean>

    <bean id="wmsScmDeliveryThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.wms.thrift.scmdelivery.ScmDeliveryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="remoteServerPort" value="8444"/>
    </bean>

    <bean id="receivingQueryService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.logistics.wio.client.receiving.ReceivingQueryService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.wio"/>
        <property name="remoteServerPort" value="8421"/>
    </bean>

    <bean id="inventoryCountThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.wms.thrift.inventorycount.InventoryCountThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="auditCountService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.wms.thrift.count.audit.AuditCountService"/>
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="breakOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.wms.thrift.breakorder.BreakOrderThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="remoteServerPort" value="8454"/>
    </bean>

    <bean id="sacAccountSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="deliveryRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.management.thrift.delivery.DeliveryThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.storeempower"/>
        <property name="remoteServerPort" value="8417"/>
        <property name="timeout" value="2000"/>
        <property name="connTimeout" value="3000"/>
    </bean>

    <bean id="channelSkuForAppThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelSkuForAppThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocms_channel_sku_for_app_service_time_out','6000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
        <property name="remoteUniProto" value="true"/>
    </bean>

    <bean id="channelSkuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelSkuThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocms_channel_sku_service_time_out','6000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8083"/>
        <property name="remoteUniProto" value="true"/>
    </bean>
    <bean id="ocmsOrderOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('freego.product.thrift.service.timeout',5000)}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.orderbiz"/>
        <property name="remoteServerPort" value="9014"/>
    </bean>

    <bean id="boothHistorySettlementQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.order.management.client.service.revenue.BoothHistorySettlementQueryService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getInt('freego.product.thrift.service.timeout',5000)}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ordermng"/>
        <property name="remoteServerPort" value="8027"/>
    </bean>

    <bean id="wmOrderServiceClientAssembly" class="com.sankuai.meituan.waimai.service.order.WmOrderServiceClientAssembly">
        <property name="requiredList">
            <set>
                <util:constant static-field= "com.sankuai.meituan.waimai.service.order.common.constants.WmOrderServiceCons.WM_ORDER_QUERY_BY_USERID_THRIFT_SERVICE" />
            </set>
        </property>
    </bean>

    <bean id="priceAppChannelPriceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.ChannelPriceThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="priceAppPriceConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.PriceConfigThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <!--<property name="serverIpPorts" value="127.0.0.1:8082"/>-->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="priceTrendThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.crm.data.client.service.price.PriceTrendThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasdata"/>
        <property name="remoteServerPort" value="8426"/>
    </bean>


    <bean id="generalThemeIndicDataThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.crm.data.client.service.GeneralThemeIndicDataThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasdata"/>
        <property name="remoteServerPort" value="8427"/>
    </bean>


    <bean id="budgetThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.resource.management.thrift.BudgetThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8901"/>
        <property name="remoteUniProto" value="true"/>
    </bean>


    <bean id="gatheringThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.pay.services.gathering.GatheringThriftService"/> <!--leaf的 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>  <!-- leaf Appkey  -->
        <property name="remoteServerPort" value="9100"/>
        <property name="timeout" value="5000"/>  <!-- 超时时间为50ms  -->
    </bean>

    <bean id="settleTradeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.trade.services.SettleTradeThriftService"/> <!--leaf的 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>  <!-- leaf Appkey  -->
        <property name="remoteServerPort" value="9013"/>
        <property name="timeout" value="5000"/>  <!-- 超时时间为50ms  -->
    </bean>


    <bean id="tagThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.TagThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="remoteServerPort" value="8081"/>
    </bean>

    <bean id="retailPriceQuoteThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.ChannelPriceQuoteThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <!--<property name="serverIpPorts" value="127.0.0.1:8082"/>-->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="offlinePriceQuoteThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.OfflinePriceQuoteThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="appraiseThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.resource.management.thrift.AppraiseThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8904"/>
    </bean>

    <bean id="newAppraiseThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.oslo.appraise.client.thrift.AppraiseThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.oslo"/>
        <property name="remoteServerPort" value="8904"/>
    </bean>

    <bean id="bonusThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.resource.management.thrift.BonusThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8902"/>
    </bean>


    <bean id="newPriceIndexThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.PriceIndexThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="newPremiumRateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.PremiumRateThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>


    <bean id="priceEffectThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.PriceEffectThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <!-- 复合价格接口 -->
    <bean id="newComplexPriceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.ComplexPriceThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="loginThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
      destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
          value="com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService"/>
        <property name="timeout"
          value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_login_thrift_timeout','1500')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>
    <bean id="priceAppPriceStrategyThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.PriceStrategyThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="similarGoodsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productintelligent.thrift.service.SimilarGoodsThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.product.intelligent"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="9001"/>
    </bean>

    <bean id="itemDataThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.crm.data.third.client.service.ItemDataThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasdata"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('item_data_timeout','5000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="remoteServerPort" value="8425"/>
    </bean>
    <bean id="pickingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.thrift.picking.PickingThriftService"/>
        <property name="timeout" value="1500"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="remoteServerPort" value="8429"/>
    </bean>

    <bean id="queryPickingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.query.thrift.picking.PickingThriftService"/>
        <property name="timeout" value="1500"/>
        <!-- 本地 appkey, 改成自己服务的 appKey -->
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.fulfill.pickquery"/>
        <property name="remoteServerPort" value="8429"/>
    </bean>

    <bean id="storeBoothSkuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.ocms.thrift.service.StoreBoothSkuThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ocms_channel_store_sku_service_time_out','2000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocms"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8081"/>
        <property name="remoteUniProto" value="true"/>
    </bean>

    <bean id="presentChannelPriceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.PresentChannelPriceThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="priceChannelActivityThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.ChannelActivityThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="contrastStoreThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.price.client.service.ContrastStoreThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopcrm.price"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="realtimeThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.saas.crm.data.client.service.RealtimeThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('realtime_thriftService_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasdata"/>
        <property name="remoteServerPort" value="8413"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="orderFinanceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.saas.crm.data.client.service.OrderFinanceThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('orderFinance_thriftService_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasdata"/>
        <property name="remoteServerPort" value="8430"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <bean id="assistantTaskThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.meituan.shangou.munich.assistant.client.service.AssistantTaskThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('assistant_thriftService_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.munich"/>
        <property name="remoteServerPort" value="9001"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="deliveryChannelThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="tmsThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="queryDeliveryRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryOperationRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="deliveryConfigurationThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="riderOperateRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="riderQueryRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="riderPickingRpcService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8437"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="storeSpuBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreSpuBizThriftService"/>
        <property name="timeout" value="20000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>


    <bean id="tenantSpuBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantSpuBizThriftService"/>
        <property name="timeout" value="15000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="channelSpuBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.ChannelSpuBizThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="storeCategoryBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreCategoryBizThriftService"/>
        <property name="timeout" value="15000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>



    <!-- 渠道订单对接服务 -->
    <bean id="copOrderDockingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 牵牛花订单 -->
    <bean id="qnhOrderThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('qnh_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <!-- 微信拉新服务 -->
    <bean id="pullNewQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.saas.crm.promotion.service.pullnew.PullNewQueryThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.saaspromotion"/>
        <property name="remoteServerPort" value="8424"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 微信拉新服务(逐渐迁移到biz_management) -->
    <bean id="pullNewThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.PullNewThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_order_thrift_timeout','5000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.wxmall.bizmanagement"/>
        <property name="remoteServerPort" value="8001"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="settlementBoothRevenueQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.settlement.services.BoothRevenueQueryService"/> <!--leaf的 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 本地appkey，请自行配置 -->
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>  <!-- leaf Appkey  -->
        <property name="remoteServerPort" value="9266"/>
        <property name="timeout" value="5000"/>  <!-- 超时时间为50ms  -->
    </bean>

    <bean id="authenticateService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.authenticate.AuthenticateService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','3000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="sacAccountManageService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.manager.SacAccountManagerService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','3000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <!-- 结算服务 -->
    <bean id="boothHistorySettlementQueryFromSettlementService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.settlement.services.BoothHistorySettlementQueryService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>
        <property name="remoteServerPort" value="9299"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="qnhBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.QnhBizThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="merchantStoreCategoryBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" primary="true">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.MerchantStoreCategoryBizThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="merchantStoreCategorySpuBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" primary="true">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.MerchantStoreCategorySpuBizThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8082"/>
    </bean>

    <bean id="empowerPoiComposeSkuThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerPoiComposeSkuThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" ref="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.platform.emproduct"/>
        <property name="remoteServerPort" value="9030"/>
    </bean>

    <bean id="supplyRelationQueryThriftServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.SupplyRelationQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.purchase"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="purchasePriceHistoryQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.sgfulfillment.scm.monitor.api.thrift.pricehistory.PurchasePriceHistoryQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.monitor"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="supplyRelationOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.SupplyRelationOperateThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.purchase"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="purchaseSkuOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.PurchaseSkuOperateThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.purchase"/>
        <property name="nettyIO" value="true"/>
        <property name="remoteServerPort" value="8421"/>
    </bean>

    <bean id="purchaseOrderMerchandiseThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.OrderMerchandiseThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.purchase"/>
        <property name="remoteServerPort" value="8421"/>
    </bean>

    <bean id="batchStockQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.management.stock.biz.comprehensive.query.stockquery.BatchStockQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.stockbiz"/> <!-- 目标 Server Appkey  -->
        <property name="remoteServerPort" value="8437"/><!-- 这里指明端口号 -->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="merchantStoreCategoryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="thriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.platform.empower.product.client.service.MerchantStoreCategoryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" ref="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.platform.emproduct"/>
        <property name="remoteServerPort" value="9035"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="queryStockThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.QueryStockThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.stockbiz"/> <!-- 目标 Server Appkey  -->
        <property name="remoteServerPort" value="8415"/><!-- 这里指明端口号 -->
        <property name="nettyIO" value="true"/>
    </bean>

<!--    <bean id="s3StorageService" class="com.sankuai.meituan.shangou.saas.common.storage.mss.MtCloudS3StorageService"-->
<!--          init-method="init">-->
<!--        <property name="accessKey" value="${mts3.accessKey}"/>-->
<!--        <property name="secretKey" value="${mts3.secretKey}"/>-->
<!--        <property name="bucketName" value="${mts3.bucketName}"/>-->
<!--        <property name="expireSeconds" value="${mts3.expireSeconds}"/>-->
<!--        <property name="url" value="${mts3.url}"/>-->
<!--    </bean>-->

    <bean id="healthForecastThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.store.wms.thrift.health.HealthForecastThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="remoteServerPort" value="8453"/>
    </bean>

    <bean id="riderDeliveryStatisticThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="rpcUserRetrieveService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface" value="com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService"/>
        <property name="remoteAppkey" value="com.sankuai.wpt.user.retrieve"/>
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="200"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="warehouseOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="pickSelectWarehouseThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.thrift.wave.WarehouseOperateThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8438"/>
    </bean>


    <bean id="storePickConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.pickselect.ebase.thrift.store.StorePickConfigThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectebase"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8427"/>
    </bean>

    <bean id="channelPoiManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="remoteServerPort" value="8453"/>
    </bean>

    <bean id="openapiQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.waimai.dws.protocol.openapi.OpenapiQueryService"/>
        <property name="timeout" value="20000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.powerapi.openapi"/>
        <property name="remoteServerPort" value="9001"/>
    </bean>

    <bean id="attendanceApprovalProviderClientProxy" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.approval.AttendanceApprovalThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8423" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="attendanceProviderClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.AttendanceThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8421" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="scheduleProviderClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.ScheduleThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8422" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="fdcAddressingThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.FdcAddressingThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.regionselection"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="tPunishService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.waima.support.api.service.punish.TPunishService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.waima.support"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="stallService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.waima.support.api.service.stall.TStallService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.waima.support"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/><!-- 开启 Netty IO  -->
    </bean>

 <bean id="equipmentService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.waima.support.api.service.equipment.TEquipmentService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.waima.support"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="resignProviderClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.ResignThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8425" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="laborHireApprovalProviderClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.LaborHireApprovalThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8424" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="laborApprovalProviderClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.LaborApprovalThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8426" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="laborHireClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.LaborHireThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8406" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="orderPlatformDeliveryOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OrderPlatformDeliveryOperateThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.tms"/> <!-- 目标 Server Appkey -->
        <property name="filterByServiceName" value="true"/>
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="employeePointProviderClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.drunkhorsemgmt.labor.thrift.EmployeePointThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.labor.mng"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8427" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="taskQueryService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.shangou.logistics.warehouse.task.TaskQueryServiceV2"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.warehousetask"/>
        <property name="nettyIO" value="true"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="accountManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountManageThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','3000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>


    <bean id="loginSelectService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.biz.LoginSelectService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','2000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="sacMenuSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.search.SacMenuSearchThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','2000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="pushMessageService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.xm.pubapi.thrift.PushMessageServiceI"/> <!-- 接口名 -->
        <property name="appKey" value="${app.name}"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.xm.pubapi"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="8820" />
        <property name="timeout" value="5000"/><!--超时时间ms-->
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="selfDeliveryPoiConfigThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>


    <bean id="deliveryQuestionnaireThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.delivery.questionnaire.DeliveryQuestionnaireThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dmp"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <!--  租户加盟模式  -->
    <bean id="marketChainRelationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.saas.tenant.thrift.MarketChainRelationThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.tenant"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="8892"/>
    </bean>

    <bean id="storeServiceThriftService"
          class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.meituan.shangou.qnh.tenant.biz.client.thrift.commercial.StoreServiceThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfnqnh.tenant.biz"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="tenantBillManageThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.tenantbill.services.TenantBillManageThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="9095"/>
    </bean>

    <bean id="tenantBillQueryThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.tenantbill.services.TenantBillQueryThriftService"/>
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.empower.settlement"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="remoteServerPort" value="9091"/>
    </bean>

    <bean id="combinationRuleThriftService"
          class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="authThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.meituan.shangou.sac.thrift.rule.CombinationRuleThriftService"/>
        <property name="timeout"
                  value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('saas_auth_thrift_timeout','3000')}"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.saasauth"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <bean id="abnOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.AbnOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="depotGoodsThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService"/>
        <property name="timeout" value="2000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.goodscenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tradeShippingOrderService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="privateNumberBillQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.service.PrivateNumberBillQueryThriftService"/>

        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.c.privacy"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="oswPoiService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.poi.TPoiService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="5000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tradeLocationRecommendService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeLocationRecommendService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="pickingProcessService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.reco.pickselect.dh.PickingProcessService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.sc.pickselectservice"/>
        <property name="nettyIO" value="true"/>        <!-- 开启 Netty IO  -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="fulfillmentOrderSearchThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ofc_ofw_order_search_thrift_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="fulfillmentOperateThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.qnh.ofc.ofw.client.thrift.service.operate.FulfillmentOperateThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('ofc_ofw_order_search_thrift_timeout','2000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.qnh.ofc.ofw"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="tradeShippingGrayService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.TradeShippingGrayService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.oio"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>


    <bean id="invoiceQueryThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.sgfnqnh.finance.tax.thrift.service.InvoiceQueryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteServerPort" value="8094"/>
        <property name="remoteAppkey" value="com.sankuai.sgfnqnh.finance.tax"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
    </bean>

    <bean id="tOnboardTrainingService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.bizmng.labor.api.training.TOnboardTrainingService"/>
        <property name="timeout" value="3000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.bizmng.labor"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
    </bean>

    <bean id="tagThriftApi" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.sgxsupply.product.management"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="admittingTaskFrontendQueryService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.dutycenter.api.admitting.service.AdmittingTaskFrontendQueryService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dutycenter"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="dutyProcessQueryThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.dhprocess.api.judge.DutyProcessQueryThriftService"/>
        <property name="timeout" value="3000"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.dhprocess"/>
        <property name="appKey" value="${app.name}"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="sealContainerLogService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.hucenter"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="scFulfillOrderQueryThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.reco.store.wms.thrift.sc.ScFulfillOrderQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.drunkhorsemgmt.wms"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="asnScFulfillOrderQueryThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.sc.fulfillment.client.asn.AsnScFulfillOrderQueryThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.sc.fulfillment"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="oswWarehouseService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="3000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="supplyRelationThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.SupplyRelationThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.purchase"/>
        <property name="timeout" value="10000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="remoteServerPort" value="8411"/>
        <property name="nettyIO" value="true"/><!-- 开启 Netty IO  -->
    </bean>

    <!-- 供应链采购 加盟主的加盟商配销价查询 -->
    <bean id="franchisorFranchiseeDistributionPriceThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface"
                  value="com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.distribution.distributionprice.franchisor.franchisee.FranchisorFranchiseeDistributionPriceThriftService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgfulfillment.scm.purchase"/>
        <property name="timeout" value="10000"/>
        <!--    通过名称调用服务，无需指定端口    -->
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="tEmployeeService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.infra.osw.api.org.TEmployeeService"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.infra.osw"/>
        <property name="timeout" value="3000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="inventoryQueryService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.warehouse.InventoryQueryService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.inventory"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <bean id="productReviewBizThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface"
                  value="com.sankuai.meituan.shangou.empower.productbiz.client.service.ProductReviewBizThriftService"/>
        <!-- 超时时间设置大一点，审核的逻辑链路很长 -->
        <property name="timeout" value="10000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.sgshopmgmt.productbiz"/>
        <property name="nettyIO" value="true"/>
        <property name="remoteServerPort" value="8082"/>
        <property name="filterByServiceName" value="true"/>
    </bean>

    <bean id="channelBaseMsgThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="commonThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelBaseMsgThriftService"/>
        <property name="timeout" value="#{T(com.sankuai.meituan.util.ConfigUtilAdapter).getString('channel_base_thrift_timeout','10000')}"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.empower.ocmschannel"/>
        <property name="remoteServerPort" value="8090"/>
        <property name="nettyIO" value="true"/>
    </bean>

    <bean id="productLibOriginThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.meituan.waimai.thrift.service.ProductLibOriginThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="serverDynamicWeight" value="true"/>
        <property name="clusterManager" value="OCTO"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.waimai.e.productlib"/>
        <property name="filterByServiceName" value="true" />
    </bean>

    <bean id="verifyTaskThriftService" class="com.sankuai.shangou.commons.thrift.augment.RichTypeThriftClientProxy"
          destroy-method="destroy">
        <property name="mtThriftPoolConfig" ref="mtThriftPoolConfig"/>
        <property name="serviceInterface" value="com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService"/>
        <property name="timeout" value="5000"/>
        <property name="appKey" value="${app.name}"/>
        <property name="remoteAppkey" value="com.sankuai.shangou.logistics.sdms"/>
        <property name="filterByServiceName" value="true"/>
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <import resource="classpath:waimai_service_order_clientassembly_center.xml"/>
    <import resource="classpath:saas-shangou-tenant-thrift-client.xml"/>
    <import resource="classpath:reco_store_saas_mms-thrift-client.xml"/>
    <import resource="classpath:reco_shopmgmt_ocms_service-thrift-client.xml"/>
    <import resource="classpath:shangou_empower_product_client.xml"/>
    <import resource="classpath:waimai_service_poisearch_client.xml"/>
    <import resource="classpath:/store-management-receipt-service-thrift-client.xml"/>
    <import resource="classpath:/pick-select-service-thrift-client.xml"/>
    <import resource="classpath:/reco_store_saas_order_mng_thrift_client.xml"/>
    <import resource="classpath:/empower-task-service-thrift-client.xml"/>
    <import resource="classpath:/reco_store_saas_task-thrift-client.xml"/>
    <import resource="classpath:reco_shopmgmt_productplatform-thrift-client.xml"/>
    <import resource="classpath:reco_store_saas_product_biz-client.xml"/>
    <import resource="classpath:data-query-api-thrift-client.xml"/>
    <import resource="classpath:stock-operate-center-thrift-client.xml"/>
</beans>
