# 服务端口号
server.port: 8412


# 监控模块
management:
  security:
    enabled: false
endpoints:
  enabled: false
  health:
    enabled: true
    sensitive: false

mts3:
  accessKey: 689e9e56b7de41f9951b8e42e2a5ba07
  url: https://s3plus.vip.sankuai.com
  onlineUrl: https://s3plus.meituan.net
  bucketName: drunkhorse
  expireSeconds: 3600

---
cloud:
  environment: public-cloud

mthrift:
  timeout: 5000
  conntimeout: 500

# 默认情况下，在所有环境都不启动swagger。如果某个环境需要启动，在对应环境配置
# 本地启动（local profile）如果需要使用swagger，在idea configuration中配置--spring.profiles.active = dev

consumable:
  producer:
    appKey: com.sankuai.waimai.sc.pickselectservice

---

spring:
  profiles: dev
mts3:
  accessKey: 592719b40b2f46d7a413ecfabdfe2efb
  url: http://msstest.vip.sankuai.com
  onlineUrl: https://msstest.sankuai.com
  bucketName: drunkhorse
  expireSeconds: 3600
sso:
  clientId: b996447f09

---

spring:
  profiles: test
mts3:
  accessKey: 592719b40b2f46d7a413ecfabdfe2efb
  url: http://msstest.vip.sankuai.com
  onlineUrl: https://msstest.sankuai.com
  bucketName: drunkhorse
  expireSeconds: 3600
urls:
  goodsDetailPrifix: https://i.waimai.test.sankuai.com/external/food/

sso:
  clientId: b996447f09

squirrel:
  clusterName: redis-sg-drunkhorse_qa
  fnCluster: redis-sg-common_qa
---

spring:
  profiles: beta
sso:
  clientId: b996447f09
---

spring:
  profiles: staging
sso:
  clientId: 05b21b97ac
urls:
  goodsDetailPrifix: http://i.waimai.meituan.com/external/food/

consumable:
  producer:
    appKey: com.sankuai.drunkhorsemgmt.wms

squirrel:
  clusterName: redis-sg-drunkhorse_stage
  fnCluster: redis-sg-common_product
---

spring:
  profiles: prod
sso:
  clientId: 05b21b97ac
urls:
  goodsDetailPrifix: http://i.waimai.meituan.com/external/food/

consumable:
  producer:
    appKey: com.sankuai.drunkhorsemgmt.wms

squirrel:
  clusterName: redis-sg-drunkhorse_product
  fnCluster: redis-sg-common_product