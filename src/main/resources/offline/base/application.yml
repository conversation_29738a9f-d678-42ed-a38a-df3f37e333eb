# 服务端口号
server.port: 8412


app:
  name: com.sankuai.sgshopmgmt.empower.pieapi

spring:
  application:
    name: ${app.name}

cloud:
  environment: private-cloud

mthrift:
  timeout: 4000
  conntimeout: 5000

# 配置注册中心地址
eureka:
  client:
    enabled: true
    serviceUrl:
      defaultZone: http://localhost:8410/eureka/

consumable:
  producer:
    appKey: com.sankuai.waimai.sc.pickselectservice

---

spring:
  profiles: dev


---

spring:
  profiles: test

---

spring:
  profiles: beta

---

spring:
  profiles: staging

consumable:
  producer:
    appKey: com.sankuai.drunkhorsemgmt.wms

---

spring:
  profiles: prod

consumable:
  producer:
    appKey: com.sankuai.drunkhorsemgmt.wms
