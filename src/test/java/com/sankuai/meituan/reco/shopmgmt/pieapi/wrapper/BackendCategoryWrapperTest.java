package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.backendcategory.BackendCategoryTreeNodeVO;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.exception.EmpowerProductException;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerProductFrontCategoryThriftService;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

import static org.hamcrest.Matchers.lessThan;
import static org.mockito.Matchers.isNull;

/**
 * <AUTHOR>
 * @createTime 2021/06/02
 */
//@RunWith(PowerMockRunner.class)
@RunWith(SpringRunner.class)
@SpringBootTest
//@PrepareForTest({ConfigUtilAdapter.class, ApiMethodParamThreadLocal.class})
public class BackendCategoryWrapperTest {

    //    @InjectMocks
    @Resource
    private BackendCategoryWrapper backendCategoryWrapper = new BackendCategoryWrapper();

    @Mock
    EmpowerProductFrontCategoryThriftService.Iface tenantSkuSortThriftService;

    @Test
    public void categoryTreeTest() throws TException, EmpowerProductException {

//        BackendCategoryVO vo1 = new BackendCategoryVO();
//        SkuSortInfoResult res = new SkuSortInfoResult();
//        SkuSortInfo si1 = new SkuSortInfo();
//        si1.setParentSortCode("0");
//        si1.setSortCode("1");
//
//        SkuSortInfo si2 = new SkuSortInfo();
//        si2.setParentSortCode("1");
//        si2.setSortCode("2");
//
//        SkuSortInfo si3 = new SkuSortInfo();
//        si3.setParentSortCode("2");
//        si3.setSortCode("3");
//
//        res.setSkuSortInfoList(Lists.newArrayList(si1,si2,si3));
//        res.setStatus(new Status(0,"success"));
//
//        Mockito.when(tenantSkuSortThriftService.querySkuSortInfo(Mockito.anyLong(), (Set<String>) isNull())).thenReturn(res);

        List<BackendCategoryTreeNodeVO> voList = backendCategoryWrapper.getBackendCategoryList(1000011L);
        System.out.println(voList);
        Assert.assertNotNull(voList);
        for (BackendCategoryTreeNodeVO vo : voList) {
            Assert.assertThat(judgeDepth(vo), lessThan(3));
        }
    }

    private int judgeDepth(BackendCategoryTreeNodeVO vo) {
        int depth = 1;
        if (!CollectionUtils.isEmpty(vo.getChildren())) {
            for (BackendCategoryTreeNodeVO child : vo.getChildren()) {
                depth = Math.max(depth, judgeDepth(child) + 1);
            }
        }
        return depth;
    }

}
