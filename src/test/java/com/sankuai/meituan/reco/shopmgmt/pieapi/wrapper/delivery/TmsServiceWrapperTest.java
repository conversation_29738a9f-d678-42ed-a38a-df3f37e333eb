package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.AggDeliveryPlatformInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryManagementUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryManagementConfigResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.ConfigCommonResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TmsServiceWrapperTest {

    @Resource
    private TmsServiceWrapper tmsServiceWrapper;

    @Test
    public void queryDeliveryInfoByOrderIds() {
        ArrayList<QueryOrderDeliveryInfoKey> empowerOrderIds = Lists.newArrayList();
        QueryOrderDeliveryInfoKey key = new QueryOrderDeliveryInfoKey();
        key.empowerOrderId = 1351149059779330116L;
        //key1.empowerOrderId = 1349601731578691594L;
        key.selfDeliveryMark = 1;
        key.deliveryMethod = 1;
        key.storeId = 4986523L;
        key.orderSource = 30;
        key.tenantId = 1000078L;
        key.orderStatus = 13;
        List<TDeliveryDetail> tDeliveryDetails = tmsServiceWrapper.queryDeliveryInfoByOrderIds(empowerOrderIds);
        assertTrue( tDeliveryDetails != null);
    }

    @Test
    public void queryStoreConfigTest() {
        long accountId = 0;
        long tenantId = 1000011L;
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setAccountId(accountId);
        sessionInfo.setTenantId(tenantId);
        SessionContext.init(sessionInfo);

        ApiMethodParamThreadLocal.setUserInfo(new User("zengping02", tenantId, 0L, accountId, 0, "zengping02", 0, ""));
        DeliveryManagementConfigResponse configResponse = tmsServiceWrapper.deliveryStoreConfigSearch(23424354L);
        log.info("store config->{}", configResponse);
        assertTrue( configResponse.getAggDeliveryPlatformConfig() != null);
    }

    @Test
    public void updateStoreConfigTest() {
        long accountId = 0;
        long tenantId = 1000011L;
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setAccountId(accountId);
        sessionInfo.setTenantId(tenantId);
        SessionContext.init(sessionInfo);
        ApiMethodParamThreadLocal.setUserInfo(new User("zengping02", tenantId, 0L, accountId, 0, "zengping02", 0, ""));
        DeliveryManagementUpdateRequest updateRequest = new DeliveryManagementUpdateRequest();
        updateRequest.setStoreId(23424354L);
        AggDeliveryPlatformInfoRequest platformInfoRequest = new AggDeliveryPlatformInfoRequest();
        platformInfoRequest.setPlatformCode(2);
        platformInfoRequest.setStatus(0);
        updateRequest.setAggDeliveryPlatformInfo(platformInfoRequest);
        ConfigCommonResponse commonResponse = tmsServiceWrapper.deliveryStoreConfigUpdate(updateRequest);
        log.info("store config->{}", commonResponse);
        assertTrue(commonResponse.getStatus().code == 0);
    }
}