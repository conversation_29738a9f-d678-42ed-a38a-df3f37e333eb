package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.dto.resource.module.AppModuleDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import lombok.extern.log4j.Log4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Create by yujing10 on 2018/11/6.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Log4j
public class TenantWrapperTest {

    @Resource
    TenantWrapper tenantWrapper;


    @Test
    public void queryAppModules() {
        List<AppModuleDto> moduleDtos = tenantWrapper.queryAppModules(Lists.newArrayList(0L), Lists.newArrayList("270"));
        Assert.assertEquals(1, moduleDtos.size());
    }

    @Test
    public void getEmployeeName() {
        String employeeName = tenantWrapper.getEmployeeName(1000011L, 10000947L);
        Assert.assertEquals("王宏", employeeName);
    }

    @Test
    public void getEmployeeNameWithEmptyStringReturn() {
        String employeeName = tenantWrapper.getEmployeeName(1000011L, 1l);
        Assert.assertEquals("", employeeName);
    }

}
