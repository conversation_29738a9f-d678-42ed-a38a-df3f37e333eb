package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantBusinessConfigWrapper;
import lombok.extern.log4j.Log4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @createTime 2019/12/2
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Log4j
public class TenantBusinessConfigWrapperTest {

    @Resource
    TenantBusinessConfigWrapper tenantBusinessConfigWrapper;

    @Test
    public void isManualTakeOrderTest() {
        boolean isManualTakeOrder = tenantBusinessConfigWrapper.isManualTakeOrder(1000011L, 1000033L);
        Assert.assertFalse(isManualTakeOrder);
    }
}
