package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.google.common.collect.ImmutableMap;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.StoreSpuController;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.UpdateStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/***
 * author : <EMAIL>
 * data : 2022/1/25
 * time : 上午11:53
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class StoreSpuControllerTest {

    @Resource
    private StoreSpuController storeSpuController;


    @Test
    public void updateStockTest() throws Exception {
        initApp();
        UpdateStockRequest request = new UpdateStockRequest();
        request.setStoreId(4987269L);
        request.setSpuId("1481098313053773854");
        request.setComment(null);

        List<StoreSkuStockVO> skuStockList = new ArrayList<>();
        StoreSkuStockVO storeSkuStockVO = new StoreSkuStockVO();
        storeSkuStockVO.setStock(40L);
        storeSkuStockVO.setSkuId("1481098313057968141");
        storeSkuStockVO.setCustomizeStockFlag(1);
        storeSkuStockVO.setAutoResumeInfiniteStock(0);
        skuStockList.add(storeSkuStockVO);

        request.setSkuStockList(skuStockList);



        CommonResponse result = storeSpuController.updateStock(request);

        log.info("返回结果:{}", result);
    }

    @Test
    public void initApp(){
        IdentityInfo identityInfo = new IdentityInfo();
        User user = new User("杨柳青测试账号",1000094L,1,52098,1, "ylq_94_Test01",113396070L, "13678956701");
        identityInfo.setUser(user);
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);
    }
}
