package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/6/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class RiderDeliveryServiceWrapperTest {

    @Test
    public void accept() {
    }

    @Test
    public void takeAway() {
    }

    @Test
    public void complete() {
    }
}