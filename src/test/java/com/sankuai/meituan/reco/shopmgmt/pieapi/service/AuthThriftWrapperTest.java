package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.SpringTestBase;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.LoginWrapper;
import com.sankuai.meituan.reco.store.management.thrift.order.OrderTaskQueryThriftService;
import com.sankuai.meituan.reco.store.management.thrift.order.PendingTask;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.LoginRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.LoginResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-03-22 Time: 15:48
 */
public class AuthThriftWrapperTest extends SpringTestBase {

    @Resource
    private AuthThriftWrapper authWrapper;
    @Resource
    private LoginThriftService.Iface loginThriftService;
    @Resource
    private LoginWrapper loginWrapper;
    @Resource
    private AuthThriftService.Iface authThriftService;

    @MockBean
    private OrderTaskQueryThriftService.Iface orderTaskQueryThriftService;

//    @MockBean
//    private AppModuleThriftService appModuleThriftService;
//
    @Before
    public void before() throws Exception {
//        AppModuleListResponse resp = new AppModuleListResponse();
//        resp.setAppModuleList(build());
//        when(appModuleThriftService.queryAppModuleListByAuthCodes(any())).thenReturn(resp);
        List<PendingTask> resp = build2();
        when(orderTaskQueryThriftService.queryPendingTask(any())).thenReturn(resp);
    }

    private List<PendingTask> build2() {
        PendingTask task1 = new PendingTask();
        task1.setTaskId(20);
        task1.setPendingTaskCount(2);
        PendingTask task2 = new PendingTask();
        task2.setTaskId(30);
        task2.setPendingTaskCount(5);
        return Lists.newArrayList(task1, task2);
    }
//
//    private List<AppModuleDto> build() {
//        AppModuleDto d1 = new AppModuleDto();
//        d1.setModuleKey("a63ccba5-d260-4243-a845-cda770690e75");
//        d1.setModuleName("apple");
//        d1.setIconUrl("http://baidu.com");
//        d1.setJsName("testpicassoci/Apple-bundle.js");
//        d1.setJsType(1);
//        d1.setSort(0);
//        d1.setAuthCode("140");
//
//        AppModuleDto d2 = new AppModuleDto();
//        d2.setModuleKey("a63cc-d260-4243-a845-cda770690e76");
//        d2.setModuleName("apple1");
//        d2.setIconUrl("http://baidu.com");
//        d2.setJsName("testpicassoci/Apple-bundle.js");
//        d2.setJsType(2);
//        d2.setSort(0);
//        d2.setAuthCode("150");
//
//        AppModuleDto d3 = new AppModuleDto();
//        d3.setModuleKey("08e5c750-4ee3-11e9-bc7a-4505fc643cbf");
//        d3.setModuleName("apple");
//        d3.setIconUrl("http://baidu.com");
//        d3.setJsName("supermarket|my-mrn-project|mrnproject");
//        d3.setJsType(1);
//        d3.setSort(0);
//        d3.setAuthCode("160");
//
//        AppModuleDto d4 = new AppModuleDto();
//        d4.setModuleKey("08e5c750-4ee3-11e9-bc7a-4505fc123cbf");
//        d4.setModuleName("apple54");
//        d4.setIconUrl("http://baidu.com");
//        d4.setJsName("supermarket|my-mrnject|mroject");
//        d4.setJsType(1);
//        d4.setSort(0);
//        d4.setAuthCode("170");
//
//        AppModuleDto d5 = new AppModuleDto();
//        d5.setModuleKey("08e5c750-4ee3-0923-bc7a-4505fc123cbf");
//        d5.setModuleName("apple54");
//        d5.setIconUrl("http://baidu.com");
//        d5.setJsName("supermark-mrnject|mroject");
//        d5.setJsType(1);
//        d5.setSort(0);
//        d5.setAuthCode("220");
//
//        return Lists.newArrayList(d1, d2, d3, d4, d5);
//    }

    @Test
    public void login() {
        String account = "yhdev001";
        String password = DigestUtils.md5Hex("yhdev001" + "000000");
        for (int i = 0; i < 100; i++) {
            try {
                LoginRequest req = new LoginRequest();
                req.setAccountName(account);
                req.setPassword(password);
                req.setAppId(1);
                req.setTenantId(1000012);
                LoginResponse resp = loginThriftService.login(req);
                System.out.println(resp);
            } catch (TException e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void clearAuthCache() throws Exception {
        Result result = authThriftService.updateRoleStatus(1595, 1);
        System.out.println(result);
    }

    @Test
    public void appAuth() throws Exception {
        // TODO SHF
//        String account = "test_qzc006";         //  test环境账号
//        String password = DigestUtils.md5Hex(account + "123456");

   /*     String account = "dev_yh001";         //  dev环境账号
        String password = DigestUtils.md5Hex("dev_yh001" + "123456");

        LoginRequest req = new LoginRequest();
        req.setAccountName(account);
        req.setPassword(password);
        req.setAppId(1);
        LoginResponse resp = loginThriftService.login(req);
        System.out.println("*************************************************       " + resp.getToken());

        AppModuleResult result = authWrapper.appModule(initSession(resp.getToken(), false));
        System.out.println(JSON.toJSONString(result, true));
        Assert.assertNotNull(result);

        result = authWrapper.appModule(initSession(resp.getToken(), true));
        System.out.println(JSON.toJSONString(result, true));
        Assert.assertNotNull(result);
    }

    private AppModuleReq initSession(String token, boolean fullStore) throws Exception {
        QuerySessionInfoResponse response = this.loginThriftService.querySessionInfo(token);

        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTenantId(response.getTenantId());
        sessionInfo.setAccountId(response.getAccountId());
        sessionInfo.setAppId(response.getAppId());
        sessionInfo.setStaffId(response.getStaffId());
        sessionInfo.setLoginTokenId(response.getLoginTokenId());
        sessionInfo.setCreateTime(response.getCreateTime());
        sessionInfo.setExpireTime(response.getExpireTime());
        SessionContext.init(sessionInfo);
        System.out.println(response);

        UserInfo userInfo = loginWrapper.obtainUserInfo(
                sessionInfo.getTenantId(), token, sessionInfo.getAccountId(),
                sessionInfo.getStaffId(), sessionInfo.getAppId(), true
        );

        Assert.assertNotNull(userInfo);
        Assert.assertNotNull(userInfo.getStorePowerList());

        ApiMethodParamThreadLocal.setUserInfo(new User(userInfo, sessionInfo.getStaffId()));
        ApiMethodParamThreadLocal.setMrnVersion("1.7.21");
        ApiMethodParamThreadLocal.setMrnApp("fulfillment_ios");
        ApiMethodParamThreadLocal.setAppId("152");
        ApiMethodParamThreadLocal.setAppVersion("3.0.2");
        ApiMethodParamThreadLocal.setOs("iOS");

        AppModuleReq req = new AppModuleReq();
        req.setStoreId(fullStore ? -1 : userInfo.getStorePowerList().get(0).getStoreId());
        req.setEntityId(5);
        req.setEntityType(1);
        req.setTenantId(1000012L);
        return req;*/
    }
}