/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import org.junit.Test;

import java.lang.reflect.Method;

/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-04-11 Time: 18:48
 */
public class AuthThriftWrapperNoSpringTest {

    @Test
    public void checkRootModuleTest() throws Exception {
        Method method = AuthThriftWrapper.class.getDeclaredMethod("checkRootModule", AppModuleResult.class);
        method.setAccessible(true);
        AppModuleResult result = (AppModuleResult) method.invoke(new AuthThriftWrapper(), JSON.parseObject(json(), AppModuleResult.class));
        System.out.println(result);
    }

    private String json() {
        return "{\n" +
                "  \"modules\": [\n" +
                "    {\n" +
                "      \"code\": \"500\",\n" +
                "      \"hasAvailLeaf\": true,\n" +
                "      \"sort\": 0,\n" +
                "      \"subModules\": [\n" +
                "        {\n" +
                "          \"code\": \"250\",\n" +
                "          \"hasAvailLeaf\": false,\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"270\",\n" +
                "          \"hasAvailLeaf\": true,\n" +
                "          \"icon\": \"http://s3plus.sankuai.com/v1/mss_153b457701454a72819426f3f37a0c85/appicon/mendianzhushou/home/<USER>",\n" +
                "          \"jsName\": \"pickselecttasklist/TaskList-bundle.js\",\n" +
                "          \"jsType\": 2,\n" +
                "          \"name\": \"拣货助手\",\n" +
                "          \"parent\": \"500\",\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"510\",\n" +
                "      \"hasAvailLeaf\": true,\n" +
                "      \"sort\": 0,\n" +
                "      \"subModules\": [\n" +
                "        {\n" +
                "          \"code\": \"240\",\n" +
                "          \"hasAvailLeaf\": false,\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"230\",\n" +
                "          \"hasAvailLeaf\": true,\n" +
                "          \"icon\": \"http://apple.png\",\n" +
                "          \"jsName\": \"testpicassoci/Apple-bundle.js\",\n" +
                "          \"jsType\": 2,\n" +
                "          \"name\": \"补货任务\",\n" +
                "          \"parent\": \"510\",\n" +
                "          \"sort\": 2,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"220\",\n" +
                "          \"hasAvailLeaf\": true,\n" +
                "          \"icon\": \"http://s3plus.sankuai.com/v1/mss_153b457701454a72819426f3f37a0c85/appicon/mendianzhushou/home/<USER>",\n" +
                "          \"jsName\": \"shopsoap-yc/location-bundle.js\",\n" +
                "          \"jsType\": 2,\n" +
                "          \"name\": \"库位设置\",\n" +
                "          \"parent\": \"510\",\n" +
                "          \"sort\": 3,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"520\",\n" +
                "      \"hasAvailLeaf\": true,\n" +
                "      \"icon\": \"http://s3plus.sankuai.com/v1/mss_153b457701454a72819426f3f37a0c85/appicon/mendianzhushou/home/<USER>",\n" +
                "      \"jsName\": \"pickselecttasklist/TaskList-bundle.js\",\n" +
                "      \"jsType\": 2,\n" +
                "      \"name\": \"基础设置\",\n" +
                "      \"parent\": \"\",\n" +
                "      \"sort\": 0,\n" +
                "      \"subModules\": [\n" +
                "        {\n" +
                "          \"code\": \"280\",\n" +
                "          \"hasAvailLeaf\": true,\n" +
                "          \"icon\": \"http://examplzcve1.png\",\n" +
                "          \"jsName\": \"testpicassoci/Banana-bundle.js\",\n" +
                "          \"jsType\": 2,\n" +
                "          \"name\": \"个人设置\",\n" +
                "          \"parent\": \"520\",\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            {\n" +
                "              \"pendingTaskCount\": null,\n" +
                "              \"code\": \"tuihuo\",\n" +
                "              \"hasAvailLeaf\": false,\n" +
                "              \"name\": null,\n" +
                "              \"jsName\": null,\n" +
                "              \"jsType\": null,\n" +
                "              \"icon\": null,\n" +
                "              \"subModules\": [\n" +
                "                \n" +
                "              ]\n" +
                "            },\n" +
                "            {\n" +
                "              \"pendingTaskCount\": null,\n" +
                "              \"code\": \"baosun\",\n" +
                "              \"hasAvailLeaf\": true,\n" +
                "              \"name\": \"测试测试测试2222\",\n" +
                "              \"jsName\": null,\n" +
                "              \"jsType\": null,\n" +
                "              \"icon\": null,\n" +
                "              \"subModules\": [\n" +
                "                {\n" +
                "                  \"pendingTaskCount\": null,\n" +
                "                  \"code\": \"370\",\n" +
                "                  \"hasAvailLeaf\": true,\n" +
                "                  \"name\": \"拣货助手222\",\n" +
                "                  \"jsName\": \"pickselecttasklist/TaskList-bundle.js\",\n" +
                "                  \"jsType\": 2,\n" +
                "                  \"icon\": \"http://s3plus.sankuai.com/v1/mss_153b457701454a72819426f3f37a0c85/appicon/mendianzhushou/home/<USER>",\n" +
                "                  \"subModules\": [\n" +
                "                    {\n" +
                "                      \"pendingTaskCount\": null,\n" +
                "                      \"code\": \"baosunxxxc234234\",\n" +
                "                      \"hasAvailLeaf\": true,\n" +
                "                      \"name\": null,\n" +
                "                      \"jsName\": null,\n" +
                "                      \"jsType\": null,\n" +
                "                      \"icon\": null,\n" +
                "                      \"subModules\": [\n" +
                "                        {\n" +
                "                          \"pendingTaskCount\": null,\n" +
                "                          \"code\": \"370\",\n" +
                "                          \"name\": \"拣货助手222121212\",\n" +
                "                          \"jsName\": \"pickselecttasklist/TaskList-bundle.js\",\n" +
                "                          \"jsType\": 2,\n" +
                "                          \"icon\": \"http://s3plus.sankuai.com/v1/mss_153b457701454a72819426f3f37a0c85/appicon/mendianzhushou/home/<USER>",\n" +
                "                          \"subModules\": [\n" +
                "                            \n" +
                "                          ]\n" +
                "                        }\n" +
                "                      ]\n" +
                "                    }\n" +
                "                  ]\n" +
                "                }\n" +
                "              ]\n" +
                "            }\n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"code\": \"huopingguanli\",\n" +
                "      \"hasAvailLeaf\": true,\n" +
                "      \"sort\": 0,\n" +
                "      \"subModules\": [\n" +
                "        {\n" +
                "          \"code\": \"peihuoshouhuo\",\n" +
                "          \"hasAvailLeaf\": true,\n" +
                "          \"icon\": \"http://s3plus.sankuai.com/v1/mss_153b457701454a72819426f3f37a0c85/appicon/mendianzhushou/home/<USER>",\n" +
                "          \"jsName\": \"shopsoap-yc/delivery-bundle.js\",\n" +
                "          \"jsType\": 2,\n" +
                "          \"name\": \"配货收货\",\n" +
                "          \"parent\": \"huopingguanli\",\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"wudanshouhuo\",\n" +
                "          \"hasAvailLeaf\": false,\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"tuihuo\",\n" +
                "          \"hasAvailLeaf\": false,\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"baosun\",\n" +
                "          \"hasAvailLeaf\": false,\n" +
                "          \"sort\": 0,\n" +
                "          \"subModules\": [\n" +
                "            \n" +
                "          ]\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }
}
