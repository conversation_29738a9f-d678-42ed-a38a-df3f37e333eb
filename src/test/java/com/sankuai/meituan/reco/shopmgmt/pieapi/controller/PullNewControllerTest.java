package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.SpringTestBase;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.wechatpullnew.WeChatPullNewController;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewRecordRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewRecordVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class PullNewControllerTest extends SpringTestBase {

    @Resource
    WeChatPullNewController weChatPullNewController;

    @Test
    public void testRecord() throws ParseException {
        IdentityInfo identityInfo = new IdentityInfo();
        Tracer.setSwimlane("waimai-eci-220607-163649-741");
        User user = new User("typename",1001197L,1,58840,1, "",1L, "");
        identityInfo.setUser(user);
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);
        PullNewRecordRequest pullNewRecordRequest = new PullNewRecordRequest();
        pullNewRecordRequest.setMarkId(0L);
        pullNewRecordRequest.setStoreId(49867236L);
        String str="2022-05-25 12:00:25.040";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.ms");
        Date date1 = (Date) sdf.parse(str);
        Date date2 = new Date();
        pullNewRecordRequest.setStartTime(date1.getTime());
        pullNewRecordRequest.setEndTime(date2.getTime()+100000);
        CommonResponse<PullNewRecordVo> commonResponse =weChatPullNewController.record(pullNewRecordRequest);
        log.info("response {}",commonResponse);
    }
}
