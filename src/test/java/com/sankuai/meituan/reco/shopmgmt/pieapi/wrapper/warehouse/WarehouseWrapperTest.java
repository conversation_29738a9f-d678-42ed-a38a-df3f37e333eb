package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.warehouse;

import com.dianping.pigeon.remoting.common.config.annotation.PigeonConfiguration;
import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickCollectionQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickQueryCountRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickTabNumRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehousePickCollectionQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehousePickQueryCountResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehousePickTabNumResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.WarehouseWrapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * AppModelControllerTest
 *
 * <AUTHOR>
 * @date 2023/2/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@PigeonConfiguration
@ActiveProfiles("test")
public class WarehouseWrapperTest {

    @Resource
    private WarehouseWrapper warehouseWrapper;

    @Test
    public void testWaitCollection() {
        WarehousePickCollectionQueryRequest request = new WarehousePickCollectionQueryRequest();
        request.setPageNum(1L);
        request.setPageSize(20);
        request.setPoiList(Lists.newArrayList(49873441L));
        request.setOrderTag("1");
        User user = new User("", 1000094L, 0,0,0,"",0,"");
        ApiMethodParamThreadLocal.getIdentityInfo().setUser(user);
        ApiMethodParamThreadLocal.getIdentityInfo().setStoreIds("49873442");

        CommonResponse<WarehousePickCollectionQueryResponse> resp = warehouseWrapper.queryWarehouseCollection(request);
        System.out.println(JacksonUtils.toJson(resp));
    }

    @Test
    public void testCollectionAggsCount() {
        WarehousePickQueryCountRequest request = new WarehousePickQueryCountRequest();
        request.setOutWarehouseOrderNo(Lists.newArrayList("***********-00002", "***********-00067", "***********-00054"));
        request.setOrderTag("1");
        User user = new User("", 1000094L, 0,0,0,"",0,"");
        ApiMethodParamThreadLocal.getIdentityInfo().setUser(user);
        ApiMethodParamThreadLocal.getIdentityInfo().setStoreIds("49873442");
        ApiMethodParamThreadLocal.getIdentityInfo().setAuthId(5);

        CommonResponse<WarehousePickQueryCountResponse> resp = warehouseWrapper.queryWarehouseCount(request);
        System.out.println(JacksonUtils.toJson(resp));
    }

    @Test
    public void testWaitCollectionCount() {
        WarehousePickTabNumRequest request = new WarehousePickTabNumRequest();
        request.setTabTypeList(Lists.newArrayList(0, 1, 2, 3));
        User user = new User("", 1000078L, 0,1234,0,"",0,"");
        ApiMethodParamThreadLocal.getIdentityInfo().setUser(user);
        ApiMethodParamThreadLocal.getIdentityInfo().setStoreIds("49875975");
        ApiMethodParamThreadLocal.getIdentityInfo().setAuthId(5);

        CommonResponse<WarehousePickTabNumResponse> resp = warehouseWrapper.queryWarehousePickTabNum(request);
        System.out.println(JacksonUtils.toJson(resp));
    }
}
