/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi;

import com.dianping.pigeon.remoting.common.config.annotation.PigeonConfiguration;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Test base class with Spring Runner
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-03-22 Time: 15:51
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@PigeonConfiguration
public abstract class SpringTestBase {
}
