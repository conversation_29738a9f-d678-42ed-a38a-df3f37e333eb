package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 2022/8/22
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserRetrieveWrapperTest extends TestCase {

    @Resource
    private UserRetrieveWrapper userRetrieveWrapper;

    public void testGetUserNameById() {
        String v = userRetrieveWrapper.getUserNameById(345345L);
        System.out.println(v);
    }
}