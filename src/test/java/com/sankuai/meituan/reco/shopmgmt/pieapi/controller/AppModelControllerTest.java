package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.pigeon.remoting.common.config.annotation.PigeonConfiguration;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.WorkbenchTaskModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * AppModelControllerTest
 *
 * <AUTHOR>
 * @date 2023/2/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@PigeonConfiguration
@ActiveProfiles("test")
public class AppModelControllerTest {

    @Resource
    private AppModelController appModelController;

    @Resource
    private WorkbenchTaskModelMenuService workbenchTaskModelMenuService;

    @Test
    public void testHomePage() {
        User user = new User("maolei_3", 1000094L, 10048434L, 83356L, 1,
                "maolei_test2", 0L, "15810912596");
        ApiMethodParamThreadLocal.setUserInfo(user);
        ApiMethodParamThreadLocal.setAuthId(5);
        ApiMethodParamThreadLocal.setStoreIds("49875057");

        QueryMenuInfoRequest request = new QueryMenuInfoRequest();
        request.setMenuCode("WP_PROCESSING_TASK");
        CommonResponse<List<AppModelMenuInfo>> resp = appModelController.queryMenuInfoList(request);
        System.out.println(JacksonUtils.toJson(resp));
    }
}
