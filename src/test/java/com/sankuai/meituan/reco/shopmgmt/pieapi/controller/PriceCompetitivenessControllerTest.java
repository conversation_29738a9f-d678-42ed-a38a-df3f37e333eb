package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.ContrastCategoryBetweenStoresRequestVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.ContrastCategoryBetweenStoresVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.ContrastStoreServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.LoginFilter;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.LoginFilterFactoryBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.annotation.Resource;
import javax.servlet.Filter;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-07 14:24
 * @Mail: <EMAIL>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PriceCompetitivenessControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Resource(name = "loginFilter")
    private FilterRegistrationBean filterRegistrationBean;




    @Before
    public void setUp() throws Exception {
        Filter filter = filterRegistrationBean.getFilter();
//        Map<String, FilterRegistrationBean> beansOfType =
//                webApplicationContext.getBeansOfType(FilterRegistrationBean.class);
//        List<Filter> collect = beansOfType.values().stream().map(FilterRegistrationBean::getFilter).collect(Collectors.toList());
//        Filter[] filters = collect.toArray(new Filter[collect.size()]);


        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .addFilter(filter)
                .build();
    }

    @Test
    public void queryContrastCategoryBetweenStores() throws Exception {
        ContrastCategoryBetweenStoresRequestVO requestVO = new ContrastCategoryBetweenStoresRequestVO();
        requestVO.setStoreId(4987267l);
        requestVO.setType("MONTH_SALE");
        String s = JSON.toJSONString(requestVO);

        String contentAsString = mockMvc.perform(MockMvcRequestBuilders
                .post("/pieapi/price/competitiveness/query-category-indexes")
                .contentType(MediaType.APPLICATION_JSON)
                .header("storeId", 4987267)
                .header("tenantId", 1000094)
                .header("authorization", "Bearer ZQmdFDyaybAcaChFfEACbJBEedYlwtu11SIY3zkgcJ4AVD_wKmkgN2Qk3bG6ThQKI6KuLfQhqmq8EYIQFv_yQA")
                .header("appid", 272)
                .header("uuid", "000000000000042B6026A897346349034B9ADE166A821A157297279982910070")
                .header("os", "iOS")
                .header("appVersion", "1.0.8")
                .header("jsVersion", "1.0.0")
                .header("authid", 5)
                .content(s)

        ).andExpect(status().isOk())
                .andDo(print())
                .andReturn().getResponse().getContentAsString();
        CommonResponse commonResponse = JSON.parseObject(contentAsString, CommonResponse.class);
        Assert.assertEquals(commonResponse.getCode(), ResultCode.CHECK_PARAM_ERR.getCode());

        System.out.println(contentAsString);

    }

    @Test
    public void queryContrastSpuBetweenStores() {
    }
}