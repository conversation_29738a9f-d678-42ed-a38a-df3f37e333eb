package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.RevenueOverviewVO;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @createTime 2020/9/18
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class BoothCurrentlyRevenueWrapperTest {

    @Resource
    private BoothCurrentlyRevenueWrapper boothCurrentlyRevenueWrapper;

    @Test
    public void overviewTest() {
        RevenueOverviewVO vo = boothCurrentlyRevenueWrapper.overview(1000094L, Lists.newArrayList(4987269L), null);
        Assert.assertNotNull(vo);
    }

    @Test
    public void overviewOfflinePriceTest() {
        RevenueOverviewVO vo = boothCurrentlyRevenueWrapper.overviewOfflinePriceRevenue(1000094L, Lists.newArrayList(4987269L), null);
        Assert.assertNotNull(vo);
    }
}
