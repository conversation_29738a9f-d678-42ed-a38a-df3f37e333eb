package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.SpringTestBase;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.AppModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.ManagementReversionHomepageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ManagementWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PendingTaskWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoResponse;

import org.apache.thrift.TException;
import org.assertj.core.util.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.Map;

/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-03-22 Time: 15:48
 */
public class PendingTaskWrapperTest extends SpringTestBase {

    @Resource
    private PendingTaskWrapper pendingTaskWrapper;

    @Resource
    private ManagementWrapper managementWrapper;

    @Autowired
    private AuthThriftService.Iface authThriftService;

    @Test
    public void queryPendingTaskTest() {
        Map<String, PendingTaskResult> pendingTaskResultMap = pendingTaskWrapper.queryPendingTask(1000011L, Lists.newArrayList(1000033L), 3,1000033L, Sets.newLinkedHashSet("ORDER_WAIT_TO_PICK"));
        Assert.assertNotNull(pendingTaskResultMap);
    }

    @Test
    public void test() throws TException {
//        AppModuleRequest req = new AppModuleRequest();
//        req.setEntityId(4987269L);
//        req.setEntityType(3);
//        CommonResponse<ManagementReversionHomepageResponse> response = managementWrapper.revisionHomepage(1000094L, Arrays.asList(4987269L), req);
//        System.out.println(response);
        QueryAccountInfoRequest request = new QueryAccountInfoRequest();
        request.setTenantId(1000094L);
        request.setAccountId(52101L);
        QueryAccountInfoResponse response1 = authThriftService.queryAccountInfoByAccountIdAndAppId(request);
        System.out.println(response1);
    }
}