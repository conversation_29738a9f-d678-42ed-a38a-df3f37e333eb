package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuDetailApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuPageQueryApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.StoreSpuSpecListUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class OcmsProductServiceWrapperTest {
    @Resource
    private OCMSProductServiceWrapper ocmsProductServiceWrapper;

    @Test
    public void testUpdateStoreSpuSpecList() {
        Tracer.serverRecv();
        StoreSpuSpecListUpdateRequest request = new StoreSpuSpecListUpdateRequest();
        request.setTenantId(1000011L);
        request.setStoreId(1000017L);
        request.setInfiniteInventory(true);
        request.setSpuId("1293911076613664778");
        List<StoreSkuCreateVO> orgSkuList = Lists.newArrayList();
        request.setOrgSkuList(orgSkuList);
        StoreSkuCreateVO org1 = new StoreSkuCreateVO();
        org1.setMonthSaleAmount(200);
        //org1.setOrgSkuId("1293911076693356573");
        org1.setSkuId("1293911076693356573");
        org1.setSpec("10g");
        org1.setWeight(10);
        orgSkuList.add(org1);

        StoreSkuCreateVO org2 = new StoreSkuCreateVO();
        org2.setMonthSaleAmount(100);
        //org2.setOrgSkuId("1293911076697550907");
        org2.setSkuId("1293911076697550907");
        org2.setSpec("20g");
        org2.setWeight(20);
        orgSkuList.add(org2);


        List<StoreSkuCreateVO> skuList = Lists.newArrayList();
        request.setSkuList(skuList);
        StoreSkuCreateVO sku1 = new StoreSkuCreateVO();
//        sku1.setMonthSaleAmount(100);
        //sku1.setOrgSkuId("1293911076697550908");
        sku1.setSkuId("1293911076697550908");
        sku1.setSpec("50g");
        sku1.setWeight(50);
        skuList.add(sku1);

        User user = new User("zjtest", 1000011L, 123L, 123L, 1, "",1L,"");
        log.info("traceId:{} updateStoreSpuSpecList:{}", Tracer.id(), request);
        CommonResponse<SaveStoreSpuPartSuccessResponseVO> response = ocmsProductServiceWrapper.updateStoreSpuSpecList(request, user);
        log.info("updateStoreSpuSpecList response:{}", JSON.toJSONString(response));
        Assert.assertTrue(response != null && response.getCode() == 0);
    }

    @Test
    public void testPageQueryStoreSpu(){
        User user = new User("18743214321", 1000094l, 1l, 1l, 1, "18743214321", 1l,"");
        StoreSpuPageQueryApiRequest request = new StoreSpuPageQueryApiRequest();
        request.setStoreId(4987269l);
        Map<Integer, String> map = Maps.newHashMap();
        map.put(100, "297786");
        request.setChannelStoreCategoryMap(map);
        request.setPage(1);
        request.setSize(20);
        CommonResponse<StoreSpuPageQueryResponseVO> response = ocmsProductServiceWrapper.pageQueryStoreSpu(request, user, null);
        System.out.println(response);
    }

    @Test
    public void testQueryStoreSpuDetail(){
        User user = new User("18743214321", 1000094l, 1l, 1l, 1, "18743214321", 1l,"");
        ApiMethodParamThreadLocal.setUserInfo(user);
        StoreSpuDetailApiRequest request = new StoreSpuDetailApiRequest();
        request.setStoreId(4987269l);
        request.setSpuId("1275275586213773374");
        CommonResponse<StoreSpuVO> response = ocmsProductServiceWrapper.detailStoreSpu(request);
        StoreSpuVO storeSpuVO = response.getData();
        ChannelSpuVO channelSpuVO = storeSpuVO.getChannelSpuList().iterator().next();
        Map<String, ChannelSkuVO> skuVOMap = channelSpuVO.getChannelSkuList().stream().collect(Collectors.toMap(ChannelSkuVO::getSkuId, Function.identity()));
        Assert.assertTrue(skuVOMap.get("1319121340053061660").getPriceEqualSign());
        Assert.assertTrue(skuVOMap.get("1319121340053061660").getAtPromotion());
        Assert.assertFalse(skuVOMap.get("1275275748168433673").getPriceEqualSign());
        Assert.assertTrue(skuVOMap.get("1275275748168433673").getAtPromotion());
        Assert.assertEquals("8.81" ,skuVOMap.get("1275275748168433673").getPresentPrice());

    }


    @Test
    public void testPriceVo(){

        StoreSkuVO storeSkuVO = new StoreSkuVO();
        storeSkuVO.setSkuId("1");

        StoreSkuVO storeSkuVO2 = new StoreSkuVO();
        storeSkuVO2.setSkuId("2");

        List<StoreSkuVO> storeSkuList = Lists.newArrayList(storeSkuVO,storeSkuVO2);

        //测试美团，京东，饿了么
        //美团渠道
        ChannelSpuVO  channelSpuVO = new ChannelSpuVO();
        channelSpuVO.setChannelId(100);

        ChannelSkuVO channelSkuVO = new ChannelSkuVO();
        channelSkuVO.setPrice(100L);
        channelSkuVO.setSkuId("1");

        ChannelSkuVO channelSkuVO2 = new ChannelSkuVO();
        channelSkuVO2.setPrice(100L);
        channelSkuVO2.setSkuId("2");
        channelSpuVO.setChannelSkuList(Lists.newArrayList(channelSkuVO,channelSkuVO2));

        //饿了么渠道
        ChannelSpuVO  eleChannelSpuVO = new ChannelSpuVO();
        eleChannelSpuVO.setChannelId(200);

        ChannelSkuVO eleChannelSkuVO = new ChannelSkuVO();
        eleChannelSkuVO.setPrice(100L);
        eleChannelSkuVO.setSkuId("1");

        ChannelSkuVO eleChannelSkuVO2 = new ChannelSkuVO();
        eleChannelSkuVO2.setPrice(102L);
        eleChannelSkuVO2.setSkuId("2");
        eleChannelSpuVO.setChannelSkuList(Lists.newArrayList(eleChannelSkuVO,eleChannelSkuVO2));

        //京东渠道
        ChannelSpuVO  jdChannelSpuVO = new ChannelSpuVO();
        jdChannelSpuVO.setChannelId(300);

        ChannelSkuVO jdChannelSkuVO = new ChannelSkuVO();
        jdChannelSkuVO.setPrice(100L);
        jdChannelSkuVO.setSkuId("1");

        ChannelSkuVO jdChannelSkuVO2 = new ChannelSkuVO();
        jdChannelSkuVO2.setPrice(100L);
        jdChannelSkuVO2.setSkuId("2");
        jdChannelSpuVO.setChannelSkuList(Lists.newArrayList(jdChannelSkuVO,jdChannelSkuVO2));


        List<ChannelSpuVO> channelSpuList = Lists.newArrayList(channelSpuVO,eleChannelSpuVO,jdChannelSpuVO);

        fillMultiChannelPriceType(storeSkuList,channelSpuList);


        Assert.assertTrue(storeSkuList.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelPriceType() == 1);
        Assert.assertTrue(storeSkuList.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelOnlinePriceList().size() == 3);

        Assert.assertTrue(storeSkuList.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelPriceType() == 2);
        Assert.assertTrue(storeSkuList.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelOnlinePriceList().size() == 3);

        //测试美团，饿了么

        List<StoreSkuVO> storeSkuList2 = Lists.newArrayList(storeSkuVO,storeSkuVO2);
        List<ChannelSpuVO> channelSpuList2 = Lists.newArrayList(channelSpuVO,eleChannelSpuVO);
        fillMultiChannelPriceType(storeSkuList2,channelSpuList2);

        Assert.assertTrue(storeSkuList2.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelPriceType() == 1);
        Assert.assertTrue(storeSkuList2.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelOnlinePriceList().size() == 2);

        Assert.assertTrue(storeSkuList2.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelPriceType() == 2);
        Assert.assertTrue(storeSkuList2.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelOnlinePriceList().size() == 2);

        //测试美团，京东

        ChannelSpuVO  channelSpuVONew = new ChannelSpuVO();
        channelSpuVONew.setChannelId(100);

        ChannelSkuVO channelSkuVONew = new ChannelSkuVO();
        channelSkuVONew.setPrice(100L);
        channelSkuVONew.setSkuId("1");

        ChannelSkuVO channelSkuVO2New  = new ChannelSkuVO();
        channelSkuVO2New.setPrice(100L);
        channelSkuVO2New.setSkuId("3");
        channelSpuVONew.setChannelSkuList(Lists.newArrayList(channelSkuVONew,channelSkuVO2New));

        List<StoreSkuVO> storeSkuList3 = Lists.newArrayList(storeSkuVO,storeSkuVO2);
        List<ChannelSpuVO> channelSpuList3 = Lists.newArrayList(channelSpuVONew,jdChannelSpuVO);
        fillMultiChannelPriceType(storeSkuList3,channelSpuList3);


        Assert.assertTrue(storeSkuList3.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelPriceType() == 1);
        Assert.assertTrue(storeSkuList3.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelOnlinePriceList().size() == 2);

        Assert.assertTrue(storeSkuList.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelPriceType() == 2);
        Assert.assertTrue(storeSkuList.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelOnlinePriceList().size() == 2);



        //测试美团，京东

        ChannelSpuVO  channelSpuVONewNull = new ChannelSpuVO();
        channelSpuVONewNull.setChannelId(100);

        channelSpuVONewNull.setChannelSkuList(Lists.newArrayList());

        ChannelSpuVO  jdChannelSpuVONull = new ChannelSpuVO();
        jdChannelSpuVONull.setChannelId(200);

        jdChannelSpuVONull.setChannelSkuList(Lists.newArrayList());

        List<StoreSkuVO> storeSkuList4 = Lists.newArrayList(storeSkuVO,storeSkuVO2);
        List<ChannelSpuVO> channelSpuList4 = Lists.newArrayList(channelSpuVONewNull,jdChannelSpuVONull);
        fillMultiChannelPriceType(storeSkuList4,channelSpuList4);


        Assert.assertTrue(storeSkuList4.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelPriceType() == 1);
        Assert.assertTrue(storeSkuList4.stream().filter(sku->sku.getSkuId().equals("1")).findFirst().get().getChannelOnlinePriceList().size() == 2);

        Assert.assertTrue(storeSkuList4.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelPriceType() == 1);
        Assert.assertTrue(storeSkuList4.stream().filter(sku->sku.getSkuId().equals("2")).findFirst().get().getChannelOnlinePriceList().size() == 2);
    }

    private  void fillMultiChannelPriceType(List<StoreSkuVO> storeSkuList, List<ChannelSpuVO> channelSpuList) {

        Map<Integer, ChannelSpuVO> channelSpuVOMap = ConverterUtils.listStreamMapToMap(channelSpuList, ChannelSpuVO::getChannelId, channelSpuVO -> channelSpuVO);

        for (StoreSkuVO storeSkuVO : storeSkuList) {

            List<ChannelPriceVO> channelOnlinePriceList = Lists.newArrayList();
            List<ChannelSkuVO> channelSkuVOs = Lists.newArrayList();
            channelSpuVOMap.forEach((channelId, channelSpu) -> {
                ChannelSkuVO channelSkuVO = parseChannelSkuVO2(storeSkuVO.getSkuId(), channelId, channelSpuList);
                channelSkuVOs.add(channelSkuVO);
                setChannelPriceVO(channelOnlinePriceList, channelSkuVO, channelId);
            });

            List<ChannelSkuVO> priceSkus = channelSkuVOs.stream().filter(Objects::nonNull).collect(Collectors.toList());
            List<ChannelSkuVO> nullSkus = channelSkuVOs.stream().filter(Objects::isNull).collect(Collectors.toList());

            if (priceSkus.size() == channelSkuVOs.size()) {
                List<Long> priceList = channelSkuVOs.stream().map(ChannelSkuVO::getPrice).collect(Collectors.toList());
                Set<Long> prices = new HashSet<>(priceList);
                if (prices.size() == 1) {
                    storeSkuVO.setChannelPriceType(1);
                    storeSkuVO.setChannelOnlinePrice(MoneyUtils.centToYuan(priceList.get(0)).toString());
                } else {
                    storeSkuVO.setChannelPriceType(2);
                }
            } else if (nullSkus.size() == channelSkuVOs.size()) {
                storeSkuVO.setChannelPriceType(1);
                storeSkuVO.setChannelOnlinePrice("0");
            } else {
                storeSkuVO.setChannelPriceType(2);
            }

            storeSkuVO.setChannelOnlinePriceList(channelOnlinePriceList);

        }
    }

    private  ChannelSkuVO parseChannelSkuVO2(String skuId, Integer channelId, List<ChannelSpuVO> channelSpuList) {
        ChannelSpuVO channelSpuVO = channelSpuList.stream().filter(spu -> spu.getChannelId() == channelId)
                .findFirst().orElse(null);
        if (channelSpuVO == null) {
            return null;
        }
        return channelSpuVO.getChannelSkuList().stream().filter(sku -> Objects.equals(sku.getSkuId(), skuId)).findFirst()
                .orElse(null);
    }

    private void setChannelPriceVO(List<ChannelPriceVO> channelOnlinePriceList, ChannelSkuVO channelSkuVO,Integer channelId){
        if (channelSkuVO != null) {
            channelOnlinePriceList.add(new ChannelPriceVO(channelId, MoneyUtils.centToYuan
                    (channelSkuVO.getPrice()).toString()));
        } else {
            channelOnlinePriceList.add(new ChannelPriceVO(channelId, "0"));
        }

    }



}
