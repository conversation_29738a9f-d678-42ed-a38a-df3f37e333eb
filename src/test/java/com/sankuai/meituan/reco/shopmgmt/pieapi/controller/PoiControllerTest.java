package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.dianping.pigeon.remoting.common.config.annotation.PigeonConfiguration;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.PoiController;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request.GetPoiOperationModeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.GetPoiOperationModeResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import org.apache.shiro.util.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/06/13 14:11:56
 * @email <EMAIL>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@PigeonConfiguration
@ActiveProfiles("test")
public class PoiControllerTest {

    @Resource
    private PoiController poiController;

    @Test
    public void testGetPoiOperationMode() {
        User user = new User("maolei_3", 1001197L, 10048434L, 83356L, 1,
                "maolei_test2", 0L, "15810912596");

        GetPoiOperationModeRequest request = new GetPoiOperationModeRequest(49873526L);
        CommonResponse<GetPoiOperationModeResponse> response = poiController.getPoiOperationMode(request);

        Assert.notNull(response.getData().getOperationMode());
    }
}
