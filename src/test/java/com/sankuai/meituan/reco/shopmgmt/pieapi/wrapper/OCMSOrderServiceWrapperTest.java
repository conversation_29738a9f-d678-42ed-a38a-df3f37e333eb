package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.ImmutableMap;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

import static org.junit.Assert.*;

/***
 * author : <EMAIL> 
 * data : 2020/7/22 
 * time : 下午9:45
 **/
@RunWith(PowerMockRunner.class)
@PrepareForTest({ConfigUtilAdapter.class, ApiMethodParamThreadLocal.class})
public class OCMSOrderServiceWrapperTest {

    @InjectMocks
    private OCMSOrderServiceWrapper ocmsOrderServiceWrapper = new OCMSOrderServiceWrapper();

    @Mock
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Mock
    private AuthThriftWrapper authThriftWrapper;

    int channelType = 100;

    @Test
    public void setCouldOperateItems() throws TException {
        List<Integer> couldOperate = Lists.newArrayList(OrderCouldOperateItem.ACCEPT_ORDER.getValue(), OrderCouldOperateItem.PART_ORDER_REFUND.getValue(), OrderCouldOperateItem.PRINT_RECEIPT.getValue(), OrderCouldOperateItem.FULL_ORDER_REFUND.getValue());
        OrderVO orderVO = new OrderVO();
        orderVO.setChannelOrderId("test");
        orderVO.setChannelId(channelType);
        OCMSOperateCheckResponse ocmsOperateCheckResponse = new OCMSOperateCheckResponse();
        ocmsOperateCheckResponse.setCouldOperateItems(Maps.newHashMap(OCMSOrderKey.builder().channelOrderId("test").channelType(channelType).build(), couldOperate));
        Mockito.when(ocmsOrderOperateThriftService.checkOrderCouldOperateItems(Mockito.any()))
                .thenReturn(ocmsOperateCheckResponse);

        Mockito.when(authThriftWrapper.authPermissionAndDataAuth(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyList()))
            .thenReturn(ImmutableMap.of("ORDER_REFUND", false, "ACCEPT_ORDER", true, "PART_ORDER_REFUND", false));

        mockApiMethodParam();

        mockAuthConfig();

        //ocmsOrderServiceWrapper.setCouldOperateItems(Lists.newArrayList(orderVO), null, OCMSOrderServiceWrapper.TO_CHECK_ITEMS);


        Assert.assertTrue(!orderVO.getOrderCouldOperateItems().contains(OrderCouldOperateItem.FULL_ORDER_REFUND.getValue()));
        Assert.assertTrue(!orderVO.getOrderCouldOperateItems().contains(OrderCouldOperateItem.PART_ORDER_REFUND.getValue()));
        Assert.assertTrue(orderVO.getOrderCouldOperateItems().contains(OrderCouldOperateItem.ACCEPT_ORDER.getValue()));
    }

    private void mockAuthConfig() {
        PowerMockito.mockStatic(ConfigUtilAdapter.class);
        PowerMockito.when(ConfigUtilAdapter.getString(Mockito.any()))
                .thenReturn("ORDER_REFUND,40;PART_ORDER_REFUND,50;ACCEPT_ORDER,10");
    }


    private void mockApiMethodParam() {
        IdentityInfo identityInfo = new IdentityInfo();
        identityInfo.setStoreIds("100000");
        identityInfo.setUser(new User("", 0L, 0L, 0L, 0, "",1L, "13433334444"));
        PowerMockito.mockStatic(ApiMethodParamThreadLocal.class);
        PowerMockito.when(ApiMethodParamThreadLocal.getIdentityInfo())
                .thenReturn(identityInfo);
    }
}