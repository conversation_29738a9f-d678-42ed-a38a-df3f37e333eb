package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @author: hezhengyu
 * @create: 2023-11-17 15:10
 */
@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
class PunishServiceWrapperTest {
    @Resource
    private PunishServiceWrapper punishServiceWrapper;

    @Test
    public void test_countWaitingAppealTicket(){
        CommonResponse<Integer> count = punishServiceWrapper.countWaitingAppealTicket(MccConfigUtil.getWaimaTenantId(), "waima_new");
        log.info("count :{}", JSON.toJSONString(count));
    }

}