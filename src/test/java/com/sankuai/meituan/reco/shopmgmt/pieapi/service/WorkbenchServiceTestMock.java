package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import static org.mockito.Matchers.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.munich.assistant.client.to.task.TaskSummaryTo;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.meituan.shangou.sac.dto.model.SacMenuNodeWithChild;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.RevenueOverviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.AbstractAppModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AssistantTaskWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaasCrmDataWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SettlementCurrentRevenueWrapper;
import com.sankuai.sgdata.query.thrift.DataQueryThriftService;
import com.sankuai.sgdata.query.thrift.response.QueryResponse;

/**
 * 工作台测试类
 *
 * <AUTHOR>
 * @since 2021/7/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WorkbenchServiceTestMock {

    @MockBean
    private SacWrapper sacWrapper;

    @MockBean
    private SaasCrmDataWrapper saasCrmDataWrapper;

    @MockBean
    private SettlementCurrentRevenueWrapper settlementCurrentRevenueWrapper;

    @MockBean
    private AssistantTaskWrapper assistantTaskWrapper;

    @MockBean
    private DataQueryThriftService dataQueryThriftService;

    @Test
    public void workbenchDataModeTest() throws TException {

        IdentityInfo identityInfo = new IdentityInfo();
        User user = new User("typename",1000011L,1,999,1, "",1L, "");
        identityInfo.setUser(user);
        identityInfo.setStoreIds("1000033");
        identityInfo.setAuthId(1);
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);

        mockDataModelMenu();
        mockRevenue();
        dataQueryMock();

        MenuCodeEnum authCodeEnum = MenuCodeEnum.WORKBENCH_DATA;
        AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(authCodeEnum, null, null);
        Assert.assertTrue(appModelMenuInfo != null);
    }

    private void mockDataModelMenu() {
        SacMenuNodeWithChild sacMenuNodeWithChild = new SacMenuNodeWithChild();
        SacMenuNodeDto menuNodeDto = new SacMenuNodeDto();
        menuNodeDto.setSacMenuCode(MenuCodeEnum.WORKBENCH_DATA.getCode());
        menuNodeDto.setSacMenuName(MenuCodeEnum.WORKBENCH_DATA.getDesc());
        menuNodeDto.setHasAuth(true);
        sacMenuNodeWithChild.setSacMenuNodeDto(menuNodeDto);

        List<SacMenuNodeDto> childMenuNodes = new ArrayList<>();
        SacMenuNodeDto child1 = new SacMenuNodeDto();
        child1.setSacMenuCode(MenuCodeEnum.TOTAL_TURNOVER.getCode());
        child1.setSacMenuName(MenuCodeEnum.TOTAL_TURNOVER.getDesc());
        child1.setRank(1);
        child1.setHasAuth(true);
        Map<String, Object> metaDataMap1 = new HashMap<>();
        metaDataMap1.put("desc", "总营业额提示信息");
        child1.setMetaData(JacksonUtils.toJson(metaDataMap1));

        SacMenuNodeDto child2 = new SacMenuNodeDto();
        child2.setSacMenuCode(MenuCodeEnum.EXPECTED_REVENUE.getCode());
        child2.setSacMenuName(MenuCodeEnum.EXPECTED_REVENUE.getDesc());
        child2.setRank(2);
        child2.setHasAuth(true);
        Map<String, Object> metaDataMap2 = new HashMap<>();
        metaDataMap2.put("desc", "预计收入提示信息");
        child2.setMetaData(JacksonUtils.toJson(metaDataMap2));

        // 有效订单无权限
        SacMenuNodeDto child3 = new SacMenuNodeDto();
        child3.setSacMenuCode(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode());
        child3.setSacMenuName(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getDesc());
        child3.setRank(3);
        child3.setHasAuth(true);
        Map<String, Object> metaDataMap3 = new HashMap<>();
        metaDataMap3.put("desc", "有效订单数量提示信息");
        child3.setMetaData(JacksonUtils.toJson(metaDataMap3));

        SacMenuNodeDto child5 = new SacMenuNodeDto();
        child5.setSacMenuCode(MenuCodeEnum.PICK_OVERTIME_RATE.getCode());
        child5.setSacMenuName(MenuCodeEnum.PICK_OVERTIME_RATE.getDesc());
        child5.setRank(4);
        child5.setHasAuth(true);
        Map<String, Object> metaDataMap5 = new HashMap<>();
        metaDataMap5.put("desc", "履约超时率提示信息");
        child5.setMetaData(JacksonUtils.toJson(metaDataMap5));

        SacMenuNodeDto child4 = new SacMenuNodeDto();
        child4.setSacMenuCode(MenuCodeEnum.WORKBENCH_DATA_JUMP.getCode());
        child4.setSacMenuName(MenuCodeEnum.WORKBENCH_DATA_JUMP.getDesc());
        child4.setRank(5);
        child4.setHasAuth(true);
        child4.setRouteUrl("url");

        childMenuNodes.add(child1);
        childMenuNodes.add(child2);
        childMenuNodes.add(child3);
        childMenuNodes.add(child5);
        childMenuNodes.add(child4);

        sacMenuNodeWithChild.setChildSacMenuNodeDtos(childMenuNodes);
        Map<String, SacMenuNodeWithChild> response = new HashMap<>();
        response.put("WP_TODAY_DATA", sacMenuNodeWithChild);

        Mockito.when(sacWrapper.getAccountMenusByMenuCodes(eq(999L), eq(1), Mockito.anyList()))
                .thenReturn(response);
    }

    private void mockTotalRevenueSrcData() {
        Map<Integer, Double> dataMap = new HashMap<>();
        dataMap.put(130, 123.45);
        Mockito.when(saasCrmDataWrapper.getRealTimeBoxedIndicatorData(eq(1000011L), eq(1000033L), eq("real_biz")))
                .thenReturn(dataMap);
    }

    private void mockPickTimeOutSrcData() {
        Map<Integer, Double> dataMap = new HashMap<>();
        dataMap.put(3, 0.6512);
        Mockito.when(saasCrmDataWrapper.getRealTimeBoxedIndicatorData(eq(1000011L), eq(1000033L), eq("real_pick")))
                .thenReturn(dataMap);
    }

    private void mockRevenue() {
        RevenueOverviewVO overviewVO = new RevenueOverviewVO();
        overviewVO.setRevenueTips("预计收入提示：线下价格");
        overviewVO.setTotalRevenue(9999L);
        overviewVO.setTotalRevenueName("预计收入");
        overviewVO.setCompleteOrderCount(99);
        overviewVO.setTotalRefund(1);
        overviewVO.setOtherRevenueCount(10);
        Mockito.when(settlementCurrentRevenueWrapper.overview(eq(1000011L), eq(1000033L), anyLong()))
                .thenReturn(overviewVO);
    }


    @Test
    public void workbenchTaskModeTest() {
        IdentityInfo identityInfo = new IdentityInfo();
        User user = new User("typename",1000011L,1,999,1, "",1L, "");
        identityInfo.setUser(user);
        identityInfo.setStoreIds("1000033");
        identityInfo.setAuthId(1);
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);

        mockTaskModelMenu();
        mockTaskSummary();

        MenuCodeEnum authCodeEnum = MenuCodeEnum.WORKBENCH_PENDING_TASK;
        AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(authCodeEnum, null, null);
        Assert.assertTrue(appModelMenuInfo != null);
    }

    private void mockTaskModelMenu() {
        SacMenuNodeWithChild sacMenuNodeWithChild = new SacMenuNodeWithChild();
        SacMenuNodeDto menuNodeDto = new SacMenuNodeDto();
        menuNodeDto.setSacMenuCode(MenuCodeEnum.WORKBENCH_PENDING_TASK.getCode());
        menuNodeDto.setSacMenuName(MenuCodeEnum.WORKBENCH_PENDING_TASK.getDesc());
        menuNodeDto.setHasAuth(true);
        sacMenuNodeWithChild.setSacMenuNodeDto(menuNodeDto);

        List<SacMenuNodeDto> childMenuNodes = new ArrayList<>();
        SacMenuNodeDto child1 = new SacMenuNodeDto();
        child1.setSacMenuCode(MenuCodeEnum.UN_PICKED.getCode());
        child1.setSacMenuName(MenuCodeEnum.UN_PICKED.getDesc());
        child1.setRank(1);
        child1.setHasAuth(true);

        SacMenuNodeDto child2 = new SacMenuNodeDto();
        child2.setSacMenuCode(MenuCodeEnum.UN_PICK_SETTLED.getCode());
        child2.setSacMenuName(MenuCodeEnum.UN_PICK_SETTLED.getDesc());
        child2.setRank(2);
        child2.setHasAuth(true);

        SacMenuNodeDto child3 = new SacMenuNodeDto();
        child3.setSacMenuCode(MenuCodeEnum.UN_REVIEWED.getCode());
        child3.setSacMenuName(MenuCodeEnum.UN_REVIEWED.getDesc());
        child3.setRank(3);
        child3.setHasAuth(true);

        SacMenuNodeDto child4 = new SacMenuNodeDto();
        child4.setSacMenuCode(MenuCodeEnum.DELIVERY_ERROR.getCode());
        child4.setSacMenuName(MenuCodeEnum.DELIVERY_ERROR.getDesc());
        child4.setRank(4);
        child4.setHasAuth(true);

        SacMenuNodeDto child5 = new SacMenuNodeDto();
        child5.setSacMenuCode(MenuCodeEnum.UN_RECEIVED.getCode());
        child5.setSacMenuName(MenuCodeEnum.UN_RECEIVED.getDesc());
        child5.setRank(5);
        child5.setHasAuth(true);

        childMenuNodes.add(child1);
        childMenuNodes.add(child2);
        childMenuNodes.add(child3);
        childMenuNodes.add(child4);
        childMenuNodes.add(child5);

        sacMenuNodeWithChild.setChildSacMenuNodeDtos(childMenuNodes);
        Map<String, SacMenuNodeWithChild> response = new HashMap<>();
        response.put("WP_PROCESSING_TASK", sacMenuNodeWithChild);

        Mockito.when(sacWrapper.getAccountMenusByMenuCodes(eq(999L), eq(1), Mockito.anyList()))
                .thenReturn(response);
    }

    private void mockTaskSummary() {

        List<TaskSummaryTo> summaries = new ArrayList<>();
        TaskSummaryTo summary1 = new TaskSummaryTo();
        summary1.setTaskType(TaskTypeEnum.UN_PICKED.getCode());
        summary1.setTaskTypeName(TaskTypeEnum.UN_PICKED.getName());
        summary1.setTaskCount(100);
        Map<String, String> extraInfoMap1 = new HashMap<>();
        extraInfoMap1.put("timeOutTaskCount", "99");
        summary1.setExtraInfoMap(extraInfoMap1);

        TaskSummaryTo summary2 = new TaskSummaryTo();
        summary2.setTaskType(TaskTypeEnum.UN_PICK_SETTLED.getCode());
        summary2.setTaskTypeName(TaskTypeEnum.UN_PICK_SETTLED.getName());
        summary2.setTaskCount(200);
        Map<String, String> extraInfoMap2 = new HashMap<>();
        extraInfoMap2.put("timeOutTaskCount", "0");
        summary2.setExtraInfoMap(extraInfoMap2);

        TaskSummaryTo summary3 = new TaskSummaryTo();
        summary3.setTaskType(TaskTypeEnum.LACK_PROCESS.getCode());
        summary3.setTaskTypeName(TaskTypeEnum.LACK_PROCESS.getName());
        summary3.setTaskCount(300);

        TaskSummaryTo summary4 = new TaskSummaryTo();
        summary4.setTaskType(TaskTypeEnum.UN_RECEIVED.getCode());
        summary4.setTaskTypeName(TaskTypeEnum.UN_RECEIVED.getName());
        summary4.setTaskCount(400);

        TaskSummaryTo summary5 = new TaskSummaryTo();
        summary5.setTaskType(TaskTypeEnum.DELIVERY_ERROR.getCode());
        summary5.setTaskTypeName(TaskTypeEnum.DELIVERY_ERROR.getName());
        summary5.setTaskCount(500);

        summaries.add(summary1);
        summaries.add(summary2);
        summaries.add(summary3);
        summaries.add(summary4);
        summaries.add(summary5);

        Mockito.when(assistantTaskWrapper.getTaskSummaryByType(eq(1000011L), eq(1000033L), eq(999L), anyList(), null).getRight())
                .thenReturn(summaries);
    }


    private void dataQueryMock() throws TException {
        String response = "{\"code\":0,\"msg\":null,\"data\":{\"dataList\":[{\"finOrdNum\":\"51\",\"poiDiscAmtChnrate\":\"\"," +
                "\"ordAvgDlvrSettleFee\":\"5.8867924528301883\",\"openAmtGtv\":\"3466.94\",\"toPredictIncomeChnrate\":\"\",\"poiDiscRateBp\":\"\",\"exposeShopCvrBp\":\"\",\"performanceOvertimeRateBp\":\"\",\"poiDiscAmt\":\"522.11\",\"selloutSkuNum\":\"14\",\"exposeNopChnrate\":\"\",\"shopOrdCvrBp\":\"\",\"dt\":\"20210906\",\"stockoutRefundRate\":\"\",\"finOrdNumChnrate\":\"\",\"openAmtGtvChnrate\":\"\",\"exposeNop\":\"1337\",\"selloutSkuNumChnrate\":\"\",\"performanceOvertimeRate\":\"0.15686274509803921\",\"ordAvgDlvrSettleFeeChnrate\":\"\",\"toPredictIncome\":\"2202.62\",\"poiDiscRate\":\"0.18503384484530605\",\"pickEffcChnrate\":\"\",\"stockoutRefundRateBp\":\"\",\"pickEffc\":\"513.27572663000785\",\"exposeShopCvr\":\"0.12266267763649963\",\"shopOrdCvr\":\"0.31707317073170732\"}],\"headList\":[{\"key\":\"dt\",\"name\":\"日\",\"desc\":\"自然日，统计或汇总按业务发生日期进行归属，如描述订单一般采用下单日期，描述商家一般采用上线日期等\",\"type\":\"long\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"finOrdNum\",\"name\":\"finOrdNum\",\"desc\":\"finOrdNum\",\"type\":\"long\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"finOrdNumChnrate\",\"name\":\"finOrdNumChnrate\",\"desc\":\"finOrdNumChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"openAmtGtv\",\"name\":\"openAmtGtv\",\"desc\":\"openAmtGtv\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"openAmtGtvChnrate\",\"name\":\"openAmtGtvChnrate\",\"desc\":\"openAmtGtvChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"poiDiscAmt\",\"name\":\"poiDiscAmt\",\"desc\":\"poiDiscAmt\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"poiDiscAmtChnrate\",\"name\":\"poiDiscAmtChnrate\",\"desc\":\"poiDiscAmtChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"poiDiscRate\",\"name\":\"poiDiscRate\",\"desc\":\"poiDiscRate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"poiDiscRateBp\",\"name\":\"poiDiscRateBp\",\"desc\":\"poiDiscRateBp\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"ordAvgDlvrSettleFee\",\"name\":\"ordAvgDlvrSettleFee\",\"desc\":\"ordAvgDlvrSettleFee\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"ordAvgDlvrSettleFeeChnrate\",\"name\":\"ordAvgDlvrSettleFeeChnrate\",\"desc\":\"ordAvgDlvrSettleFeeChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"toPredictIncome\",\"name\":\"toPredictIncome\",\"desc\":\"toPredictIncome\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"toPredictIncomeChnrate\",\"name\":\"toPredictIncomeChnrate\",\"desc\":\"toPredictIncomeChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"exposeNop\",\"name\":\"exposeNop\",\"desc\":\"exposeNop\",\"type\":\"long\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"exposeNopChnrate\",\"name\":\"exposeNopChnrate\",\"desc\":\"exposeNopChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"exposeShopCvr\",\"name\":\"exposeShopCvr\",\"desc\":\"exposeShopCvr\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"exposeShopCvrBp\",\"name\":\"exposeShopCvrBp\",\"desc\":\"exposeShopCvrBp\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"shopOrdCvr\",\"name\":\"shopOrdCvr\",\"desc\":\"shopOrdCvr\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"shopOrdCvrBp\",\"name\":\"shopOrdCvrBp\",\"desc\":\"shopOrdCvrBp\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"stockoutRefundRate\",\"name\":\"stockoutRefundRate\",\"desc\":\"stockoutRefundRate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"stockoutRefundRateBp\",\"name\":\"stockoutRefundRateBp\",\"desc\":\"stockoutRefundRateBp\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"selloutSkuNum\",\"name\":\"selloutSkuNum\",\"desc\":\"selloutSkuNum\",\"type\":\"long\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"selloutSkuNumChnrate\",\"name\":\"selloutSkuNumChnrate\",\"desc\":\"selloutSkuNumChnrate\",\"type\":\"string\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"performanceOvertimeRate\",\"name\":\"performanceOvertimeRate\",\"desc\":\"performanceOvertimeRate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"performanceOvertimeRateBp\",\"name\":\"performanceOvertimeRateBp\",\"desc\":\"performanceOvertimeRateBp\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"pickEffc\",\"name\":\"pickEffc\",\"desc\":\"pickEffc\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null},{\"key\":\"pickEffcChnrate\",\"name\":\"pickEffcChnrate\",\"desc\":\"pickEffcChnrate\",\"type\":\"double\",\"showType\":null,\"unit\":null,\"alias\":null,\"metaType\":null}],\"pagination\":null},\"detailMsg\":null}";
        Mockito.when(dataQueryThriftService.query(any()))
                .thenReturn(com.meituan.linz.boot.util.JacksonUtils.parse(response, QueryResponse.class));
    }

}

