package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.reco.shopmgmt.pieapi.config.WmExtMenuInfoConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PoiAccountInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.WMTaskModelMenuHelper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.HttpUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: hezhengyu
 * @create: 2024-03-25 17:23
 */

@Slf4j
@RunWith(SpringRunner.class)
@ActiveProfiles("test")
@SpringBootTest
public class WaiMaTest {
    @Configuration
    @Import(value = {
            TenantWrapper.class,
            WMTaskModelMenuHelper.class
    }) // 导入要测试的Bean与其依赖
    static class Config {
    }

    @Resource
    private WMTaskModelMenuHelper wmTaskModelMenuHelper;

    @Resource
    private TenantWrapper tenantWrapper;

    private static final Long tenantId = 1001197L;

    private static final Long empId = 10058691L;

    @Test
    public void test_queryTaskDetail(){
        Map<String, Object> request = new HashMap<>();
        request.put("tenantId", tenantId);
        request.put("empId", empId);
//        String s = HttpUtils.doPost("https://selftest-240308-104350-920-sl-baichuan.shangou.test.meituan.com/support/poi-preparation/decoration/query-unfinished-task", MediaType.APPLICATION_JSON,
//                request, null);
//        System.out.println(s);
    }

}
