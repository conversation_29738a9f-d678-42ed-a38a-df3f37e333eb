package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import java.time.LocalDate;
import java.util.Date;

import javax.annotation.Resource;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.common.enums.SettlePeriodEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.SettlePeriodDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementDailySummaryReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementDailySummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.BoothHistorySettlementServiceFromSettlementWrapper;

/**
 * <AUTHOR>
 * @since 2021/07/12
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BoothHistorySettlementQueryFromSettlementWrapperTest {

    @Resource
    private BoothHistorySettlementServiceFromSettlementWrapper boothHistorySettlementServiceFromSettlementWrapper;

    @Test
    public void calculateNextAutoSettleDayTest() {
        String nextSettleDay;
        Long createTime = 1580546395000L;

        System.out.println("创建日：" + DateFormatUtils.format(new Date(createTime), "yyyy-MM-dd"));
        System.out.println("当前日：" + LocalDate.now().toString());

        //隔几天
        SettlePeriodDTO settlePeriodDTO1 = new SettlePeriodDTO();
        settlePeriodDTO1.setType(SettlePeriodEnum.DAYS.getCode());
        settlePeriodDTO1.setValues(Lists.newArrayList(1));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO1, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO1) + ":" + nextSettleDay);
        settlePeriodDTO1.setValues(Lists.newArrayList(3));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO1, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO1) + ":" + nextSettleDay);
        settlePeriodDTO1.setValues(Lists.newArrayList(5));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO1, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO1) + ":" + nextSettleDay);

        //按周
        SettlePeriodDTO settlePeriodDTO2 = new SettlePeriodDTO();
        settlePeriodDTO2.setType(SettlePeriodEnum.WEEK.getCode());
        settlePeriodDTO2.setValues(Lists.newArrayList(1));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO2, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO2) + ":" + nextSettleDay);

        settlePeriodDTO2.setValues(Lists.newArrayList(1, 3, 5));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO2, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO2) + ":" + nextSettleDay);

        //按月
        SettlePeriodDTO settlePeriodDTO3 = new SettlePeriodDTO();
        settlePeriodDTO3.setType(SettlePeriodEnum.MONTH.getCode());
        settlePeriodDTO3.setValues(Lists.newArrayList(1));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO3, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO3) + ":" + nextSettleDay);
        settlePeriodDTO3.setValues(Lists.newArrayList(1, 10, 20));
        nextSettleDay = boothHistorySettlementServiceFromSettlementWrapper.calculateNextAutoSettleDay(settlePeriodDTO3, createTime);
        System.out.println(boothHistorySettlementServiceFromSettlementWrapper.buildSettlePeriod(settlePeriodDTO3) + ":" + nextSettleDay);
    }

    @Test
    public void dailySummaryTest() {
        BoothHistorySettlementDailySummaryReq req = new BoothHistorySettlementDailySummaryReq();
        //req.setTenantId(1000011L);
        req.setStoreId(1000033L);
        req.setStartDate("2020-02-01");
        req.setEndDate("2020-03-01");
        CommonResponse<BoothHistorySettlementDailySummaryVO> summaryVO = boothHistorySettlementServiceFromSettlementWrapper.dailySummary(req);
        System.out.println(summaryVO);
    }

}
