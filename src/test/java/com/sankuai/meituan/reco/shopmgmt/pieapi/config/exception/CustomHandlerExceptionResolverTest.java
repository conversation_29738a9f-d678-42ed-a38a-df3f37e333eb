package com.sankuai.meituan.reco.shopmgmt.pieapi.config.exception;

import com.sankuai.meituan.util.ConfigUtilAdapter;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest(ConfigUtilAdapter.class)
@PowerMockIgnore( {"javax.management.*"})
public class CustomHandlerExceptionResolverTest {
	@Before
	public void init() {
		PowerMockito.mockStatic(ConfigUtilAdapter.class);
		PowerMockito.when(ConfigUtilAdapter.getString("test", "test")).thenReturn("test");
		PowerMockito.when(ConfigUtilAdapter.getString("test", "default")).thenReturn("default");

		PowerMockito.when(ConfigUtilAdapter.getString("test1", "test1")).thenReturn("test_value");
		PowerMockito.when(ConfigUtilAdapter.getString("test1", "default")).thenReturn("test_value");
	}

	@Test
	public void test1() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${");
		Assert.assertEquals("${", msg);
	}

	@Test
	public void test2() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("}");
		Assert.assertEquals("}", msg);
	}

	@Test
	public void test3() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${}");
		Assert.assertEquals("", msg);
	}

	@Test
	public void test4() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${:}");
		Assert.assertEquals("", msg);
	}

	@Test
	public void test5() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${test}");
		Assert.assertEquals("test", msg);
	}

	@Test
	public void test6() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${test:}");
		Assert.assertEquals("test", msg);
	}

	@Test
	public void test7() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${test:default}");
		Assert.assertEquals("default", msg);
	}

	@Test
	public void test8() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("${test1} ");
		Assert.assertEquals("test_value", msg);
	}

	@Test
	public void test9() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg(" ${test1:}");
		Assert.assertEquals("test_value", msg);
	}

	@Test
	public void test10() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg(" ${test1:default} ");
		Assert.assertEquals("test_value", msg);
	}

	@Test
	public void test11() {
		String msg = CustomHandlerExceptionResolver.resolveParamCheckErrorMsg("test1");
		Assert.assertEquals("test1", msg);
	}

	@Test
	public void testNotCustom1() {
		Assert.assertFalse(CustomHandlerExceptionResolver.isCustomErrorMsg("test1"));
	}

	@Test
	public void testNotCustom2() {
		Assert.assertFalse(CustomHandlerExceptionResolver.isCustomErrorMsg("${"));
	}

	@Test
	public void testNotCustom3() {
		Assert.assertFalse(CustomHandlerExceptionResolver.isCustomErrorMsg("}"));
	}

	@Test
	public void testIsCustom1() {
		Assert.assertTrue(CustomHandlerExceptionResolver.isCustomErrorMsg("${}"));
	}

	@Test
	public void testIsCustom2() {
		Assert.assertTrue(CustomHandlerExceptionResolver.isCustomErrorMsg("${:}"));
	}

	@Test
	public void testIsCustom3() {
		Assert.assertTrue(CustomHandlerExceptionResolver.isCustomErrorMsg("${test}"));
	}
}
