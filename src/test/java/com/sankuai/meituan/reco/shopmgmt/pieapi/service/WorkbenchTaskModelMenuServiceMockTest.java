package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.WorkbenchTaskModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import org.apache.shiro.util.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;

/**
 * @Author: shipeixin03
 * @Date: 2023-11-14  16:45
 */


@RunWith(MockitoJUnitRunner.class)
public class WorkbenchTaskModelMenuServiceMockTest {


    @Mock
    private TenantWrapper tenantWrapper;


    @InjectMocks
    private WorkbenchTaskModelMenuService workbenchTaskModelMenuService;


    @Test
    public void testFilterTaskByPoiType() throws Exception {
        long poiId = 1L;
        int entityType = PoiEntityTypeEnum.REGIONAL_WAREHOUSE.code();

        Map<Long, Integer> map = new HashMap<>();
        map.put(poiId, entityType);
        Mockito.when(tenantWrapper.queryEntityTypeMapByPoiIds(any(), any())).thenReturn(map);

        IdentityInfo identityInfo = new IdentityInfo();
        identityInfo.setStoreIds("1");
        User user = new User("dsq",1L,1L,1L,1,"dsq",1L, "158****4075");
        identityInfo.setUser(user);

        Class<?> clazz = Class.forName("com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.WorkbenchTaskModelMenuService");

        Method method = clazz.getDeclaredMethod("filterTaskByPoiType", List.class, IdentityInfo.class);

        method.setAccessible(true);

        List<TaskTypeEnum> taskTypeEnumList = new ArrayList<>();
        taskTypeEnumList.add(TaskTypeEnum.UN_PICKED);
        taskTypeEnumList.add(TaskTypeEnum.UN_MERGE);
        taskTypeEnumList.add(TaskTypeEnum.UN_PICK_SETTLED);

        List<TaskTypeEnum> res = (List<TaskTypeEnum>) method.invoke(workbenchTaskModelMenuService, taskTypeEnumList, identityInfo);

        System.out.println(res);
        Assert.isTrue(TaskTypeEnum.UN_MERGE.isSupportStorehouse());
        Assert.isTrue(res.size() == 2);
        Assert.isTrue(res.contains(TaskTypeEnum.UN_PICKED));
        Assert.isTrue(res.contains(TaskTypeEnum.UN_MERGE));
        Assert.isTrue(!res.contains(TaskTypeEnum.UN_PICK_SETTLED));

    }

}
