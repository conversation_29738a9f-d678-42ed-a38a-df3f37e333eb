package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PoiSgRealTimeDataDto;

import lombok.extern.slf4j.Slf4j;

import java.util.Collections;

/**
 * 数仓单侧
 *
 * <AUTHOR>
 * @since 2021/9/3
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SgDataWrapperTest {

    @Resource
    private SgDataWrapper sgDataWrapper;

    @Test
    public void queryRealTimeDataTest() {
        PoiSgRealTimeDataDto realTimeDataDto = sgDataWrapper.queryRealTimeData(Collections.singletonList(1002719L));
        System.out.println(realTimeDataDto);
        Assert.assertTrue(realTimeDataDto != null);
    }


}