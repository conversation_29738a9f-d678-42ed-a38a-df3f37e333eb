package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceEffectIndexReferencePriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.ReferencePriceDataVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuAdjustStrategyAndReferencePriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.PriceEffectWrapper;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuHitSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum;
import com.sankuai.meituan.shangou.empower.price.client.request.strategy.QuerySkuHitSyncStrategyDetailRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.strategy.QueryHitPriceStrategyResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.PriceStrategyThriftService;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-30 21:16
 * @Mail: <EMAIL>
 */
@RunWith(MockitoJUnitRunner.class)
public class SaasPriceServiceWrapperTest {
    @InjectMocks
    SaasPriceServiceWrapper saasPriceServiceWrapper = new SaasPriceServiceWrapper();
    @Mock
    StoreSpuThriftService storeSpuThriftService;
    @Mock
    PriceStrategyThriftService priceStrategyThriftService;
    @Mock
    PriceEffectWrapper priceEffectWrapper;

    User user = new User("", 1000094l, 1l, 1l, 1, "", 1,"");
    Long storeId = 4987271l;
    StoreSpuDTO spu;

    @Before
    public void setUp(){
        spu = new StoreSpuDTO();
        spu.setSpuId("1");
        StoreDTO storeDTO = new StoreDTO(1l,110l,"测试门店", 1);
        spu.setStore(storeDTO);
    }

    @Test(expected = CommonLogicException.class)
    public void storeSpuNoChannelSpu() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(200);
        spu.setStoreSkuList(Lists.newArrayList(sku));
        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        saasPriceServiceWrapper.querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", null);
    }


    @Test
    public void storeSpuNoMatchedStoreSkuWithChannelSku() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(200);
        spu.setStoreSkuList(Lists.newArrayList(sku));

        ChannelSkuDTO channelSku = new ChannelSkuDTO();
        channelSku.setSkuId("1-2");

        ChannelSpuDTO channelSpu = new ChannelSpuDTO();
        channelSpu.setSpuId("1");
        channelSpu.setChannelId(100);
        channelSpu.setChannelSkuList(Lists.newArrayList(channelSku));
        spu.setChannelSpuList(Lists.newArrayList(channelSpu));


        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        CommonResponse<SkuAdjustStrategyAndReferencePriceVO> commonResponse = saasPriceServiceWrapper
                .querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", null);
        Assert.assertEquals("没有可以改价的商品", commonResponse.getMessage());
    }
    @Test
    public void storeSpuSpecifySkuNotFind() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(200);
        spu.setStoreSkuList(Lists.newArrayList(sku));

        ChannelSkuDTO channelSku = new ChannelSkuDTO();
        channelSku.setSkuId("1-1");

        ChannelSpuDTO channelSpu = new ChannelSpuDTO();
        channelSpu.setSpuId("1");
        channelSpu.setChannelId(100);
        channelSpu.setChannelSkuList(Lists.newArrayList(channelSku));
        spu.setChannelSpuList(Lists.newArrayList(channelSpu));


        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        CommonResponse<SkuAdjustStrategyAndReferencePriceVO> commonResponse = saasPriceServiceWrapper
                .querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", "1-2");
        Assert.assertEquals("找不到传入sku对应的商品", commonResponse.getMessage());
    }

    @Test
    public void storeSpuMultiSpecNoWeight() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(0);

        StoreSkuDTO sku1 = new StoreSkuDTO();
        sku1.setSkuId("1-2");
        sku1.setStorePrice(3.5);
        sku1.setWeight(null);
        spu.setStoreSkuList(Lists.newArrayList(sku, sku1));

        ChannelSkuDTO channelSku = new ChannelSkuDTO();
        channelSku.setSkuId("1-1");
        ChannelSkuDTO channelSku1 = new ChannelSkuDTO();
        channelSku1.setSkuId("1-2");

        ChannelSpuDTO channelSpu = new ChannelSpuDTO();
        channelSpu.setSpuId("1");
        channelSpu.setChannelId(100);
        channelSpu.setChannelSkuList(Lists.newArrayList(channelSku, channelSku1));
        spu.setChannelSpuList(Lists.newArrayList(channelSpu));


        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        CommonResponse<SkuAdjustStrategyAndReferencePriceVO> commonResponse = saasPriceServiceWrapper
                .querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", null);
        Assert.assertEquals("当前商品信息异常，商品重量为空，请维护后再改价", commonResponse.getMessage());
    }


    @Test(expected = CommonLogicException.class)
    public void storeSpuGetSkuSyncStrategyError() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(200);
        spu.setStoreSkuList(Lists.newArrayList(sku));

        ChannelSkuDTO channelSku = new ChannelSkuDTO();
        channelSku.setSkuId("1-1");

        ChannelSpuDTO channelSpu = new ChannelSpuDTO();
        channelSpu.setSpuId("1");
        channelSpu.setChannelId(100);
        channelSpu.setChannelSkuList(Lists.newArrayList(channelSku));
        spu.setChannelSpuList(Lists.newArrayList(channelSpu));


        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        QueryHitPriceStrategyResponse hitResponse = new QueryHitPriceStrategyResponse();
        hitResponse.setCode(1);
        hitResponse.setMsg("mock异常消息");
        Mockito.when(priceStrategyThriftService.querySyncStrategyDetails(any(QuerySkuHitSyncStrategyDetailRequest.class)))
                .thenReturn(hitResponse);
        saasPriceServiceWrapper.querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", null);
    }

    @Test
    public void storeSpuGetReferencePriceError() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(200);
        spu.setStoreSkuList(Lists.newArrayList(sku));

        ChannelSkuDTO channelSku = new ChannelSkuDTO();
        channelSku.setSkuId("1-1");

        ChannelSpuDTO channelSpu = new ChannelSpuDTO();
        channelSpu.setSpuId("1");
        channelSpu.setChannelId(100);
        channelSpu.setChannelSkuList(Lists.newArrayList(channelSku));
        spu.setChannelSpuList(Lists.newArrayList(channelSpu));


        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        QueryHitPriceStrategyResponse hitResponse = new QueryHitPriceStrategyResponse();
        hitResponse.setCode(0);
        SkuHitSyncStrategyDTO skuHitSyncStrategyDTO = new SkuHitSyncStrategyDTO();
        skuHitSyncStrategyDTO.setChannelId(100);
        skuHitSyncStrategyDTO.setSkuId("1-1");
        skuHitSyncStrategyDTO.setFixedPrice(1000l);
        skuHitSyncStrategyDTO.setSyncStrategyType(SyncStrategyTypeEnum.FIX_PRICE);
        skuHitSyncStrategyDTO.setStoreId(110l);
        hitResponse.setSkuHitSyncStrategyDTOS(Lists.newArrayList(skuHitSyncStrategyDTO));
        Mockito.when(priceStrategyThriftService.querySyncStrategyDetails(any(QuerySkuHitSyncStrategyDetailRequest.class)))
                .thenReturn(hitResponse);
        Mockito.when(priceEffectWrapper.querySkuPriceEffectIndexReferencePrice(user, 110L, Lists.newArrayList("1-1")))
                .thenReturn(CommonResponse.fail(1, "测试mock错误"));
        CommonResponse<SkuAdjustStrategyAndReferencePriceVO> result = saasPriceServiceWrapper.querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", null);
        Assert.assertEquals(0, result.getCode());
        Assert.assertNotNull(result.getData());
        SkuAdjustStrategyAndReferencePriceVO data = result.getData();
        Assert.assertEquals("1-1",data.getStoreSkuAdjustPriceDetail().getSkuId());
        Assert.assertEquals(Integer.valueOf(200), data.getStoreSkuAdjustPriceDetail().getWeight());
    }

    @Test
    public void storeSpuAndReferenceCorrect() throws TException {
        StoreSkuDTO sku = new StoreSkuDTO();
        sku.setSkuId("1-1");
        sku.setStorePrice(2.5);
        sku.setWeight(200);
        spu.setStoreSkuList(Lists.newArrayList(sku));

        ChannelSkuDTO channelSku = new ChannelSkuDTO();
        channelSku.setSkuId("1-1");

        ChannelSpuDTO channelSpu = new ChannelSpuDTO();
        channelSpu.setSpuId("1");
        channelSpu.setChannelId(100);
        channelSpu.setChannelSkuList(Lists.newArrayList(channelSku));
        spu.setChannelSpuList(Lists.newArrayList(channelSpu));


        StoreSpuDetailResponse response = new StoreSpuDetailResponse(0, null, spu);
        Mockito.when(storeSpuThriftService.findStoreSpuDetail(any(StoreSpuDetailRequest.class)))
                .thenReturn(response);
        QueryHitPriceStrategyResponse hitResponse = new QueryHitPriceStrategyResponse();
        hitResponse.setCode(0);
        SkuHitSyncStrategyDTO skuHitSyncStrategyDTO = new SkuHitSyncStrategyDTO();
        skuHitSyncStrategyDTO.setChannelId(100);
        skuHitSyncStrategyDTO.setSkuId("1-1");
        skuHitSyncStrategyDTO.setFixedPrice(1000l);
        skuHitSyncStrategyDTO.setSyncStrategyType(SyncStrategyTypeEnum.FIX_PRICE);
        skuHitSyncStrategyDTO.setStoreId(110l);
        hitResponse.setSkuHitSyncStrategyDTOS(Lists.newArrayList(skuHitSyncStrategyDTO));
        Mockito.when(priceStrategyThriftService.querySyncStrategyDetails(any(QuerySkuHitSyncStrategyDetailRequest.class)))
                .thenReturn(hitResponse);

        //skuid -》list
        Map<String, List<PriceEffectIndexReferencePriceVO>> map = Maps.newHashMap();
        PriceEffectIndexReferencePriceVO referencePriceVO = new PriceEffectIndexReferencePriceVO();
        referencePriceVO.setPriceEffectIndexType("premiumRate");
        ReferencePriceDataVO referencePriceDataVO = new ReferencePriceDataVO("mrPrice", "市调价", BigDecimal.ONE);
        ReferencePriceDataVO referencePriceDataVO1 = new ReferencePriceDataVO("premiumRateThresholdPrice", "溢价率红线价", BigDecimal.TEN);
        referencePriceVO.setReferencePriceVOList(Lists.newArrayList(referencePriceDataVO, referencePriceDataVO1));
        map.put("1-1", Lists.newArrayList(referencePriceVO));
        CommonResponse<Map<String, List<PriceEffectIndexReferencePriceVO>>> success = CommonResponse.success(map);
        Mockito.when(priceEffectWrapper.querySkuPriceEffectIndexReferencePrice(any(User.class), anyLong(), anyList()))
                .thenReturn(success);
        CommonResponse<SkuAdjustStrategyAndReferencePriceVO> result = saasPriceServiceWrapper.querySingleSkuAdjustStrategyAndReferencePrice(user, storeId, "1", null);
        Assert.assertEquals(0, result.getCode());
        Assert.assertNotNull(result.getData());
        SkuAdjustStrategyAndReferencePriceVO data = result.getData();
        Assert.assertEquals("1-1",data.getStoreSkuAdjustPriceDetail().getSkuId());
        Assert.assertEquals(Integer.valueOf(200), data.getStoreSkuAdjustPriceDetail().getWeight());
        Assert.assertNotNull(result.getData().getReferencePriceIndexMap());
        Map<String, List<ReferencePriceDataVO>> referencePriceIndexMap = result.getData().getReferencePriceIndexMap();
        Assert.assertTrue(referencePriceIndexMap.containsKey("premiumRate"));
    }
}