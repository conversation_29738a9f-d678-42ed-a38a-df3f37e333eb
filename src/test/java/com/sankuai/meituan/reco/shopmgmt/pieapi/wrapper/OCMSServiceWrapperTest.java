package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FindTenantSimilarSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuPreviewVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-12-07 17:46
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class OCMSServiceWrapperTest {
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;


    @Test
    public void findSimilarSkuTest(){
        FindTenantSimilarSkuRequest request=new FindTenantSimilarSkuRequest();
        request.setSpuId("1270301666750496794");
        request.setMaxWeight("1000");
        request.setSize(3);
        CommonResponse<List<TenantSkuVO>> response= ocmsServiceWrapper.findSimilarSpec(1000094L,request);

    }

    @Test
    public void tenantSpuPreviewTest(){
        List<Integer> typeList= Lists.newArrayList(1);
        CommonResponse<TenantSpuPreviewVO> response= ocmsServiceWrapper.tenantSpuPreview(1000094L,"1270301666750496794",typeList);

    }
}
