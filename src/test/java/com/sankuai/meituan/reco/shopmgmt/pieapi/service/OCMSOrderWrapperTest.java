package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.ConfirmOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.QueryOrderDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.QueryWaitToDeliveryOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.RefundApplyAuditRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuWithChannelInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaveCdqStoreSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.PageQueryOffSaleAndUnquotedRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryOnSaleAndOffSaleCountRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.UpdateStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.RefundApplyListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickHomePageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SaveCdqStoreSkuPartitionSuccessResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.QueryOnSaleAndOffSaleCountResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PickWrapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Create by yujing10 on 2018/11/6.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class OCMSOrderWrapperTest {

    @Resource
    private OCMSOrderServiceWrapper ocmsOrderServiceWrapper;

    @Resource
    private PickWrapper pickWrapper;

    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;







    @Test
    public void queryWaitAuditRefundTest() {
        CommonResponse<RefundApplyListResponse> response = ocmsOrderServiceWrapper.queryWaitAuditRefund(1000011L, Lists.newArrayList(1000033L), 1, 20, PoiEntityTypeEnum.STORE.code());
        System.out.println(response.getCode() + "," + response.getMessage() + "," + response.getData());
    }





    @Test
    public void homePageTest() {
        CommonResponse<PickHomePageResponse> response = pickWrapper.homepage();
        System.out.println(response.getCode() + "," + response.getMessage() + "," + response.getData());
    }




    @Test
    public void testdeleteToReviewQuotesIgnoreError()
    {
        SaveCdqStoreSkuRequest saveCdqStoreSkuRequest = new SaveCdqStoreSkuRequest();
        CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo = new CdqStoreSkuWithChannelInfoVo();
        CdqStoreSkuVo cdqStoreSkuVo = new CdqStoreSkuVo();
        cdqStoreSkuVo.setSkuId("kasflajslkfa");
        cdqStoreSkuWithChannelInfoVo.setCdqStoreSkuVo(cdqStoreSkuVo);
        saveCdqStoreSkuRequest.setCdqStoreSkuWithChannelInfoVo(cdqStoreSkuWithChannelInfoVo);
        saveCdqStoreSkuRequest.setStoreId(1000034L);
        saveCdqStoreSkuRequest.setSaveType(2);
        saveCdqStoreSkuRequest.setDeleteToReviewQuote(true);
        User user = new User("typename",1,1,1,1, "",1L,"");
        long storeId = 1000034;
        String skuId = "";
        //ocmsServiceWrapper(user,storeId,skuId);
        CommonResponse<SaveCdqStoreSkuPartitionSuccessResponse> result = ocmsServiceWrapper.saveStoreOnlineSku(saveCdqStoreSkuRequest);
    }
    @Test
    public void updateStockTest(){
        UpdateStockRequest updateStockRequest = new UpdateStockRequest();
        long storeId = 4987269L;
        updateStockRequest.setStoreId(storeId);
        updateStockRequest.setSpuId("4987263");
        List<StoreSkuStockVO> skuStockList = Lists.newArrayList();
        StoreSkuStockVO storeSkuStockVO = new StoreSkuStockVO();
        storeSkuStockVO.setStock((long)999);
        storeSkuStockVO.setAutoResumeInfiniteStock(1);
        storeSkuStockVO.setCustomizeStockFlag(1);
        storeSkuStockVO.setSkuId("1254296364364333064");
        skuStockList.add(storeSkuStockVO);
        updateStockRequest.setSkuStockList(skuStockList);
        User user = new User("laozhang",(long) 1000094,(long) 1000079,(long) 100079,1, "",1L,"");

        CommonResponse commonResponse = ocmsServiceWrapper.updateStock(updateStockRequest,user);
    }

    @Test
    public void queryOnSaleAndOffSaleCountTest(){
        QueryOnSaleAndOffSaleCountRequest queryOnSaleAndOffSaleCountRequest = new QueryOnSaleAndOffSaleCountRequest();
        queryOnSaleAndOffSaleCountRequest.setStoreId(4987269L);
        //queryOnSaleAndOffSaleCountRequest.setChannelIds();
        User user = new User("laozhang",(long) 1000094,(long) 1000079,(long) 100079,1, "",1L,"");
        CommonResponse<QueryOnSaleAndOffSaleCountResponseVO> response = ocmsServiceWrapper.queryOnSaleAndOffSaleCount(queryOnSaleAndOffSaleCountRequest,user, null);
    }

    @Test
    public void pageQueryUnquotedTest(){
        PageQueryOffSaleAndUnquotedRequest request = new PageQueryOffSaleAndUnquotedRequest();
        request.setStoreId(4987269L);
        request.setQueryType(1);
        request.setHasStoreCategory(1);
        User user = new User("laozhang",(long) 1000094,(long) 1000079,(long) 100079,1, "",1L,"");
        CommonResponse<StoreSpuPageQueryResponseVO> response = ocmsServiceWrapper.pageQueryUnquoted(request,user);
        CommonResponse<StoreSpuPageQueryResponseVO> last = response;
    }
}

