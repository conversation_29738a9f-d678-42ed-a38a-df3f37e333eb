package com.sankuai.meituan.reco.shopmgmt.pieapi;

import com.alibaba.fastjson.JSON;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public class LocalTest {
    public static void main(String[] args) {
//        List<String> a = new ArrayList<>();
//        Map<String, String> map = a.stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
//        System.out.println(JSON.toJSONString(map));

        Long s = 1L;
        System.out.println(BigDecimal.valueOf(Optional.ofNullable(s).orElse(0L), 2)
                .divide(new BigDecimal(100), RoundingMode.HALF_UP).toPlainString());

        System.out.println("#########################");
        System.out.println(BigDecimal.valueOf(Optional.ofNullable(s).orElse(0L), 2).toPlainString());
    }
}
