package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import com.sankuai.meituan.shangou.empower.rider.client.common.Status;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.DurationsStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DurationsStatisticsResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.stereotype.Repository;
import org.springframework.test.context.junit4.SpringRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/2 05:02
 **/
public class RiderDeliveryStatisticWrapperTest {

    @Test
    public void getMonthDurationTest() throws ClassNotFoundException, NoSuchMethodException, IllegalAccessException,
            InstantiationException, InvocationTargetException {
        Class<?> aClass = Class.forName("com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderDeliveryStatisticWrapper");
        Method method = aClass.getDeclaredMethod("getMonthDuration", LocalDate.class, int.class);
        method.setAccessible(true);
        Object instance = aClass.newInstance();
        LocalDate yesterday = LocalDate.now().minusDays(1);
        List<DurationsStatisticsRequest.TDuration> durations;

        durations = Arrays.asList((DurationsStatisticsRequest.TDuration)method.invoke(instance,yesterday, 0),
                (DurationsStatisticsRequest.TDuration)method.invoke(instance,yesterday, -1),
                (DurationsStatisticsRequest.TDuration)method.invoke(instance,yesterday, -2));
        System.out.println(durations.get(0).beginDate + " " + durations.get(0).endDate);
        System.out.println(durations.get(1).beginDate + " " + durations.get(1).endDate);
        System.out.println(durations.get(2).beginDate + " " + durations.get(2).endDate);
    }

}
