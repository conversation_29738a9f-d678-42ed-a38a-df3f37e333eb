package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickTaskDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.RiderPickTaskInfoVO;
import com.sankuai.meituan.reco.stock.operate.center.thrift.dto.RecommendLocationBatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.codehaus.groovy.runtime.DefaultGroovyMethods.collect;

/**
 * <AUTHOR>
 * @Date 2022/7/21
 **/

@Slf4j
public class RiderPickingServiceWrapperUnitTest {

    @InjectMocks
    RiderPickingServiceWrapper riderPickingServiceWrapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void fillLocationBatchInfoTest1() {

        RiderPickTaskDTO taskDto = new RiderPickTaskDTO();
        taskDto.setTemperatureAttribute("常温");
        taskDto.setStoreSkuId("123");

        RiderPickTaskInfoVO taskVo = new RiderPickTaskInfoVO();

        //测试推荐温区！= 需要温区
        RecommendLocationBatch.SkuInfo skuInfo = new RecommendLocationBatch.SkuInfo("123", 1);
        RecommendLocationBatch.LocationInfo locationInfo = new RecommendLocationBatch.LocationInfo();
        locationInfo.setTemperatureType(2);
        locationInfo.setLocationId(21565L);
        locationInfo.setLocationCode("01-01-01-01");

        //构造skuRecommendLocationBatchMap
        RecommendLocationBatch recommendLocationBatch1 = new RecommendLocationBatch();
        recommendLocationBatch1.setBatch(new RecommendLocationBatch.BatchInfo("24514256","2022-07-11"));
        recommendLocationBatch1.setSku(skuInfo);
        recommendLocationBatch1.setLocation(locationInfo);

        HashMap< RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap = new HashMap<>();
        skuRecommendLocationBatchMap.put(skuInfo, Arrays.asList(recommendLocationBatch1));

        ReflectionTestUtils.invokeMethod(riderPickingServiceWrapper, "fillLocationBatchInfo", taskDto, taskVo, skuRecommendLocationBatchMap);

        Assert.assertEquals(taskVo.getRequiredTemperatureAreaStockIsEnough(), false);
        Assert.assertNotNull(taskVo.getLocationInfos());
        Assert.assertNotNull(taskVo.getBatchInfos());
    }

    @Test
    public void fillLocationBatchInfoTest2() {

        RiderPickTaskDTO taskDto = new RiderPickTaskDTO();
        taskDto.setTemperatureAttribute("自定义：备注");
        taskDto.setTemperatureAttribute("");
        taskDto.setStoreSkuId("123");

        RiderPickTaskInfoVO taskVo = new RiderPickTaskInfoVO();

        //测试 需要温区为自定义
        RecommendLocationBatch.SkuInfo skuInfo = new RecommendLocationBatch.SkuInfo("123", 0);
        RecommendLocationBatch.LocationInfo locationInfo = new RecommendLocationBatch.LocationInfo();
        locationInfo.setTemperatureType(1);
        locationInfo.setLocationId(21565L);
        locationInfo.setLocationCode("01-01-01-01");

        //构造skuRecommendLocationBatchMap
        RecommendLocationBatch recommendLocationBatch1 = new RecommendLocationBatch();
        recommendLocationBatch1.setBatch(new RecommendLocationBatch.BatchInfo("24514256","2022-07-11"));
        recommendLocationBatch1.setSku(skuInfo);
        recommendLocationBatch1.setLocation(locationInfo);

        HashMap< RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap = new HashMap<>();
        skuRecommendLocationBatchMap.put(skuInfo, Arrays.asList(recommendLocationBatch1));

        ReflectionTestUtils.invokeMethod(riderPickingServiceWrapper, "fillLocationBatchInfo", taskDto, taskVo, skuRecommendLocationBatchMap);

        Assert.assertEquals(true, taskVo.getRequiredTemperatureAreaStockIsEnough());
        Assert.assertNotNull(taskVo.getLocationInfos());
        Assert.assertNotNull(taskVo.getBatchInfos());
    }

    @Test
    public void fillLocationBatchInfoTestBatchInfoValid1() {

        RiderPickTaskDTO taskDto = new RiderPickTaskDTO();
        taskDto.setTemperatureAttribute("常温");
        taskDto.setStoreSkuId("123");

        RiderPickTaskInfoVO taskVo = new RiderPickTaskInfoVO();

        //构造skuRecommendLocationBatchMap
        HashMap< RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap = new HashMap<>();
        RecommendLocationBatch recommendLocationBatch1 = new RecommendLocationBatch();
        RecommendLocationBatch.SkuInfo skuInfo = new RecommendLocationBatch.SkuInfo("123", 1);
        RecommendLocationBatch.LocationInfo locationInfo = new RecommendLocationBatch.LocationInfo();
        locationInfo.setTemperatureType(2);
        locationInfo.setLocationId(21565L);
        locationInfo.setLocationCode("01-01-01-01");

        recommendLocationBatch1.setBatch(new RecommendLocationBatch.BatchInfo("24514256","2022-07-11"));
        recommendLocationBatch1.setSku(skuInfo);
        recommendLocationBatch1.setLocation(locationInfo);
        //异常情况1：map为空
        //skuRecommendLocationBatchMap.put(skuInfo, Arrays.asList(recommendLocationBatch1));

        ReflectionTestUtils.invokeMethod(riderPickingServiceWrapper, "fillLocationBatchInfo", taskDto, taskVo, skuRecommendLocationBatchMap);

        Assert.assertEquals(taskVo.getRequiredTemperatureAreaStockIsEnough(),true);
        Assert.assertNotNull(taskVo.getLocationInfos());
        Assert.assertNotNull(taskVo.getBatchInfos());
    }


    @Test
    public void fillLocationBatchInfoTestBatchInfoValid2() {

        RiderPickTaskDTO taskDto = new RiderPickTaskDTO();
        taskDto.setTemperatureAttribute("常温");
        taskDto.setStoreSkuId("123");

        RiderPickTaskInfoVO taskVo = new RiderPickTaskInfoVO();

        //构造skuRecommendLocationBatchMap
        HashMap< RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap = new HashMap<>();
        RecommendLocationBatch recommendLocationBatch1 = new RecommendLocationBatch();
        RecommendLocationBatch.SkuInfo skuInfo = new RecommendLocationBatch.SkuInfo("123", 1);
        RecommendLocationBatch.LocationInfo locationInfo = new RecommendLocationBatch.LocationInfo();
        locationInfo.setTemperatureType(2);
        locationInfo.setLocationId(21565L);
        locationInfo.setLocationCode("01-01-01-01");

        recommendLocationBatch1.setBatch(new RecommendLocationBatch.BatchInfo("24514256","2022-07-11"));
        recommendLocationBatch1.setSku(skuInfo);
        //异常2 推荐库位为空
        //recommendLocationBatch1.setLocation(locationInfo);

        skuRecommendLocationBatchMap.put(skuInfo, Arrays.asList(recommendLocationBatch1));

        ReflectionTestUtils.invokeMethod(riderPickingServiceWrapper, "fillLocationBatchInfo", taskDto, taskVo, skuRecommendLocationBatchMap);

        Assert.assertEquals(true, taskVo.getRequiredTemperatureAreaStockIsEnough());
        Assert.assertNotNull(taskVo.getLocationInfos());
        Assert.assertNotNull(taskVo.getBatchInfos());
    }


    @Test
    public void compareByTemperatureAttribute() throws NoSuchMethodException, ClassNotFoundException, IllegalAccessException, InstantiationException, InvocationTargetException {

        RiderPickTaskInfoVO riderPickTaskInfoVO1 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO1.setTemperatureAttributeType("常温");

        RiderPickTaskInfoVO riderPickTaskInfoVO2 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO2.setTemperatureAttributeType("常温");

        RiderPickTaskInfoVO riderPickTaskInfoVO3 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO3.setTemperatureAttributeType("冷藏");

        RiderPickTaskInfoVO riderPickTaskInfoVO4 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO4.setTemperatureAttributeType("冷藏");

        RiderPickTaskInfoVO riderPickTaskInfoVO5 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO5.setTemperatureAttributeType("自定义：备注");

        RiderPickTaskInfoVO riderPickTaskInfoVO6 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO6.setTemperatureAttributeType("自定义：备注");

        RiderPickTaskInfoVO riderPickTaskInfoVO7 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO7.setTemperatureAttributeType(null);

        RiderPickTaskInfoVO riderPickTaskInfoVO8 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO8.setTemperatureAttributeType("");

        RiderPickTaskInfoVO riderPickTaskInfoVO9 = new RiderPickTaskInfoVO();
        riderPickTaskInfoVO9.setTemperatureAttributeType("冷藏");


        Class<?> clazz = Class.forName("com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider.RiderPickingServiceWrapper");
        Object o = clazz.newInstance();
        Method method = clazz.getDeclaredMethod("compareByTemperatureAttribute", RiderPickTaskInfoVO.class, RiderPickTaskInfoVO.class);

        method.setAccessible(true);

        List<RiderPickTaskInfoVO> riderPickTaskInfoVOS = Arrays.asList(riderPickTaskInfoVO3, riderPickTaskInfoVO9,
                riderPickTaskInfoVO6, riderPickTaskInfoVO1, riderPickTaskInfoVO8, riderPickTaskInfoVO2,
                riderPickTaskInfoVO5, riderPickTaskInfoVO7, riderPickTaskInfoVO4);
        List<RiderPickTaskInfoVO> result = riderPickTaskInfoVOS.stream()
                .sorted((o1,o2) -> {
                    try {
                        return (int) method.invoke(o, o1, o2);
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        e.printStackTrace();
                        return 0;
                    }
                })
                .collect(Collectors.toList());

        Assert.assertEquals("冷藏",result.get(0).getTemperatureAttributeType());
        Assert.assertEquals("冷藏",result.get(1).getTemperatureAttributeType());
        Assert.assertEquals("冷藏",result.get(2).getTemperatureAttributeType());
        Assert.assertEquals("常温",result.get(3).getTemperatureAttributeType());
        Assert.assertEquals("常温",result.get(4).getTemperatureAttributeType());
        Assert.assertEquals("自定义：备注",result.get(5).getTemperatureAttributeType());
        Assert.assertEquals("自定义：备注",result.get(6).getTemperatureAttributeType());
        Assert.assertTrue(StringUtils.isBlank(result.get(7).getTemperatureAttributeType()));
        Assert.assertTrue(StringUtils.isBlank(result.get(8).getTemperatureAttributeType()));


    }

}
