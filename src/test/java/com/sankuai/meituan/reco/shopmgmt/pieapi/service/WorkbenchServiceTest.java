package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.AbstractAppModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;

/**
 * 工作台测试类
 *
 * <AUTHOR>
 * @since 2021/7/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WorkbenchServiceTest {

    @Test
    public void workbenchDataModeTest() {

        IdentityInfo identityInfo = new IdentityInfo();
        User user = new User("typename",1000011L,1,52103,2, "",1L, "");
        identityInfo.setUser(user);
        identityInfo.setStoreIds("1000033");
        identityInfo.setAuthId(5);
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);

        MenuCodeEnum authCodeEnum = MenuCodeEnum.WORKBENCH_DATA;
        AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(authCodeEnum, null, null);
        Assert.assertTrue(appModelMenuInfo != null);
    }

    @Test
    public void workbenchTaskModeTest() {
        IdentityInfo identityInfo = new IdentityInfo();
        User user = new User("typename",1000011L,1,52103,2, "",1L, "");
        identityInfo.setUser(user);
        identityInfo.setStoreIds("1000033");
        identityInfo.setAuthId(5);
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);

        MenuCodeEnum authCodeEnum = MenuCodeEnum.WORKBENCH_PENDING_TASK;
        AppModelMenuInfo appModelMenuInfo = AbstractAppModelMenuService.queryMenuInfoByCode(authCodeEnum, null, null);
        Assert.assertTrue(appModelMenuInfo != null);
    }

}

