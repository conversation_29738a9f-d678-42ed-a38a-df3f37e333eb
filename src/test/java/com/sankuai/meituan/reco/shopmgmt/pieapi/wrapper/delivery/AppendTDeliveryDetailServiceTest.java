package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/7
 */
public class AppendTDeliveryDetailServiceTest {

    @Mock
    private AppendDeliveryInfoService appendDeliveryInfoService;

    @Test
    public void appendDeliveryInfo() {
    }

    @Test
    public void formatPlatformChannelName() {

        appendDeliveryInfoService = new AppendDeliveryInfoService();
        String platformName = appendDeliveryInfoService.formatChannelName(DeliveryEntityEnum.PLATFORM.getValue(), "美团-城市代理");
        Assert.assertTrue(platformName.equals("美团-城市代理"));

        String elme = appendDeliveryInfoService.formatChannelName(DeliveryEntityEnum.PLATFORM.getValue(), "饿了么");

        Assert.assertTrue(elme.equals("饿了么"));
        String meituan = appendDeliveryInfoService.formatChannelName(DeliveryEntityEnum.PLATFORM.getValue(), "美团-美团-美团");
        Assert.assertTrue(meituan.equals("美团-美团-美团"));


    }


    @Test
    public void formatThirdPartAndSelfChannelName() {

        appendDeliveryInfoService = new AppendDeliveryInfoService();
        String platformName = appendDeliveryInfoService.formatChannelName(DeliveryEntityEnum.DELIVER_BY_SELF.getValue(), "美团-城市代理");
        Assert.assertTrue(platformName.equals("美团-城市代理"));

        String elme = appendDeliveryInfoService.formatChannelName(DeliveryEntityEnum.THIRD_PART.getValue(), "饿了么");

        Assert.assertTrue(elme.equals("饿了么"));

    }
}