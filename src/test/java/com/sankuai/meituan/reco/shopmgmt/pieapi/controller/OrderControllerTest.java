package com.sankuai.meituan.reco.shopmgmt.pieapi.controller;

import com.google.common.collect.ImmutableMap;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.common.json.JSONUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.Objects;

/***
 * author : <EMAIL>
 * data : 2021/7/12 
 * time : 上午11:53
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class OrderControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    @Resource
    private TenantWrapper tenantWrapper;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext)
                .build();
    }

    @Test
    public void testPartRefundReason() throws Exception {
        MvcResult result =  mockMvc.perform(
                MockMvcRequestBuilders.post("/pieapi/order/partrefund")
                        .param("channelOrderId", "11235432")
                        .header("storeId", 4987267)
                        .header("tenantId", 1000094)
                        .header("authorization", "Bearer 7f29zOSwSenAlbgKql7uizPW44WOJu5rlLGGDNg-LMMbtp3SEEW0Z0Bekx2y5EGFBEnnRFmJo_VciY66LtOffQ")
                        .header("appid", 272)
                        .header("uuid", "00000000000008A35B9512AC748B995B135EDE46E1B92A156000684784720752")
                        .header("etoken", "7f29zOSwSenAlbgKql7uizPW44WOJu5rlLGGDNg-LMMbtp3SEEW0Z0Bekx2y5EGFBEnnRFmJo_VciY66LtOffQ")
                        .header("os", "iOS")
                        .header("appVersion", "1.0.8")
                        .header("jsVersion", "1.0.0")
                        .header("authid", 5)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSONUtil.toJSONString(ImmutableMap.of("channelOrderId", "1233", "storeId", "12345")))
        ).andReturn();
        log.info("返回结果:{}", result.getResponse().getContentAsString());
    }

    private void buildSessionAndApiMethodParamThreadLocal () {
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTenantId(1000094L);
        sessionInfo.setAccountId(52098L);
        sessionInfo.setAccountName("ylq_94_Test01");
        sessionInfo.setAccountType(1);
        sessionInfo.setEmpName("杨柳青测试账号");
        sessionInfo.setLoginTokenId(0);
        sessionInfo.setAppId(5);
        sessionInfo.setStaffId(10036962L);
        sessionInfo.setCreateTime(1621825789000L);
        sessionInfo.setEpAccountId(113396070L);
        sessionInfo.setExpireTime(0L);

        SessionContext.init(sessionInfo);

        ApiMethodParamThreadLocal.setStoreIds("4987269");
        ApiMethodParamThreadLocal.setAuthId(5);
        TenantWrapper.EmployeeBaseInfo operator = tenantWrapper.getEmployeeInfo(sessionInfo.getTenantId(), sessionInfo.getStaffId());

        ApiMethodParamThreadLocal.setUserInfo(new User(operator.getEmployeeName(),sessionInfo.getTenantId(), sessionInfo.getStaffId(),
                sessionInfo.getAccountId(), sessionInfo.getAccountType(), sessionInfo.getAccountName(), sessionInfo.getEpAccountId(), operator.getEmployeePhone()));
    }
}
