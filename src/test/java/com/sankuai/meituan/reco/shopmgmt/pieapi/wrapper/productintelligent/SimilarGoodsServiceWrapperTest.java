package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.productintelligent;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.productintelligent.SimilarGoodsRequestVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.productintelligent.SimilarGoodVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.productintelligent.SimilarGoodsResponseVO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodDTO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodsRequestDTO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodsResultListDTO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.service.SimilarGoodsThriftService;
import io.swagger.models.auth.In;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SimilarGoodsServiceWrapperTest {

    @InjectMocks
    private SimilarGoodsServiceWrapper similarGoodsServiceWrapper = new SimilarGoodsServiceWrapper();

    @Mock
    private SimilarGoodsThriftService similarGoodsThriftService;

    @Test
    public void marketSimilarGoods() {
        SimilarGoodsRequestVO similarGoodsRequestVO = new SimilarGoodsRequestVO();
        similarGoodsRequestVO.setTenantId(Long.valueOf(100));
        similarGoodsRequestVO.setStoreId(Long.valueOf(1000));
        similarGoodsRequestVO.setChannelId(Integer.valueOf(10));
        similarGoodsRequestVO.setSpuId("10000");

        SimilarGoodDTO similarGoodDTO = new SimilarGoodDTO();
        similarGoodDTO.setSpec("testSpec");
        similarGoodDTO.setSourceName("testSourceName");
        similarGoodDTO.setOutStoreName("testOutStoreName");
        similarGoodDTO.setOutSkuId("testOutSkuId");
        similarGoodDTO.setOutSkuName("testOutSkuName");
        similarGoodDTO.setOriginPrice(Long.valueOf(100));
        similarGoodDTO.setSalePrice(Long.valueOf(1000));
        similarGoodDTO.setTotalSales(Integer.valueOf(100));
        similarGoodDTO.setMonthSales(Integer.valueOf(10));

        SimilarGoodsResultListDTO similarGoodsResultListDTO = new SimilarGoodsResultListDTO();
        similarGoodsResultListDTO.setCode(0);
        similarGoodsResultListDTO.setMsg("success");
        similarGoodsResultListDTO.setSimilarGoods(Collections.singletonList(similarGoodDTO));

        Mockito.when(similarGoodsThriftService.querySimilarGoods(Mockito.any(SimilarGoodsRequestDTO.class))).
                thenReturn(similarGoodsResultListDTO);

        CommonResponse<SimilarGoodsResponseVO> commonResponse =
                similarGoodsServiceWrapper.marketSimilarGoods(similarGoodsRequestVO);
        SimilarGoodVO similarGoodVO = commonResponse.getData().getSimilarGoodVOList().get(0);

        Assert.assertEquals(0, commonResponse.getCode());
        Assert.assertEquals("success", commonResponse.getMessage());
        Assert.assertEquals(similarGoodDTO.getSpec(), similarGoodVO.getSpec());
        Assert.assertEquals(similarGoodDTO.getSourceName(), similarGoodVO.getSourceName());
        Assert.assertEquals(similarGoodDTO.getOutStoreName(), similarGoodVO.getOutStoreName());
        Assert.assertEquals(similarGoodDTO.getOutSkuId(), similarGoodVO.getOutSkuId());
        Assert.assertEquals(similarGoodDTO.getOutSkuName(), similarGoodVO.getOutSkuName());
        Assert.assertEquals(similarGoodDTO.getOriginPrice(), similarGoodVO.getOriginPrice());
        Assert.assertEquals(similarGoodDTO.getSalePrice(), similarGoodVO.getSalePrice());
        Assert.assertEquals(similarGoodDTO.getTotalSales(), similarGoodVO.getTotalSales());
        Assert.assertEquals(similarGoodDTO.getMonthSales(), similarGoodVO.getMonthSales());
    }
}