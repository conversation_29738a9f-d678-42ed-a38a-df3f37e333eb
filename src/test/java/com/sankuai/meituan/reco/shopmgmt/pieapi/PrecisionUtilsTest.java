package com.sankuai.meituan.reco.shopmgmt.pieapi;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.PrecisionUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

@Slf4j
public class PrecisionUtilsTest {
	private static final ObjectMapper objectMapper = new ObjectMapper();

	@Test
	public void testStockNotWeight() throws JsonProcessingException {
		String value = PrecisionUtils.stockPrecisionConvert(1.2352, false);

		log.info("value = {}", value);
		Assert.assertEquals("1", value);
	}

	@Test
	public void testStockNeedWeight() {
		String value = PrecisionUtils.stockPrecisionConvert(1.2352, true);

		log.info("value = {}", value);
		Assert.assertEquals("1.235", value);
	}

	@Test
	public void testMoney() {
		String value = PrecisionUtils.moneyPrecisionConvert(1.2356);

		log.info("value = {}", value);
		Assert.assertEquals("1.24", value);
	}

	@Test
	public void testTime() {
		long time = 1540349776779L;
		//TimeUtils
	}
}
