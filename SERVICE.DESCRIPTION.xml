<?xml version="1.0" encoding="UTF-8"?>
<serviceCatalog
        xmlns="http://service.sankuai.com/1.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://service.sankuai.com/1.0.0
            http://pixel.sankuai.com/repository/releases/com/meituan/apidoc/servicecatalog/1.0.0/servicecatalog-1.0.0.xsd">
    <!-- Service description -->
    <serviceDescs>
        <serviceDesc>
            <name>蔬果派APP的API服务</name>
            <appkey>com.sankuai.sgshopmgmt.empower.pieapi</appkey>
            <description>蔬果派restful接口,主要满足于菜场代运营类型商家的经营管理和履约管理</description>
            <scenarios>蔬果派接口</scenarios>
            <notice>无</notice>

            <interfaceDescs>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.AccountController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.ManagementController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.UserPushConfigController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.OrderController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.PickController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.warehouse.WarehouseController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.SkuAndStockController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.SecurityDepositController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.MessageCenterController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.PriceController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.RegionSpuController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.StoreSpuController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.ProblemSpuController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.TenantSpuController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.RealTimeTradeController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.BoothController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.PriceTrendController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.ChannelSpuController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.assistanttask.AssistantTaskController</class>
                </interfaceDesc>
                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.DxSSOController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider.RiderDeliveryController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider.RiderManageController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider.RiderPickingController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.productintelligent.SimilarGoodsController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.wechatpullnew.WeChatPullNewController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.image.ImageController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.PoiController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.revenue.BoothHistorySettlementController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.rider.RiderLocatingLogController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.FrontCategorySpuController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.spu.MerchantStoreCategoryController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.AppModelController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.AuthController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.BackendCategoryController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.CheckController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.DeliveryController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.ElecContractController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.MyDeliveryConfigController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.PendingTaskController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.TenantChannelController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.TenantController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.TenantEmployeeController</class>
                </interfaceDesc>

                <interfaceDesc>
                    <type>restful</type>
                    <class>com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable.ConsumableController</class>
                </interfaceDesc>
            </interfaceDescs>
        </serviceDesc>
    </serviceDescs>

</serviceCatalog>