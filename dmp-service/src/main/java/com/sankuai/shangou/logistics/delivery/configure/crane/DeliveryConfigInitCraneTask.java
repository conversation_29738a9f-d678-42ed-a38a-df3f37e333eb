package com.sankuai.shangou.logistics.delivery.configure.crane;

import com.alibaba.fastjson.JSON;
import com.cip.crane.client.spring.annotation.Crane;
import com.cip.crane.client.spring.annotation.CraneConfiguration;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderLabelEnum;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelTimeOutConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfigQueryResponse;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.commons.utils.retry.RetryTemplateUtil;
import com.sankuai.shangou.logistics.delivery.config.LionConfigUtils;
import com.sankuai.shangou.logistics.delivery.configure.crane.param.InitDeliveryConfigTaskParam;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryConfig;
import com.sankuai.shangou.logistics.delivery.configure.value.SecondDeliveryForbiddenCondition;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.IStoreConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.mapper.StoreDimensionConfigDOMapper;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDO;
import com.sankuai.shangou.logistics.delivery.dao.config.model.StoreConfigDOExample;
import com.sankuai.shangou.logistics.delivery.mapper.SelfDeliveryPoiConfigPOMapper;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPO;
import com.sankuai.shangou.logistics.delivery.model.SelfDeliveryPoiConfigPOExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-08-05
 * @email <EMAIL>
 */
@Slf4j
@CraneConfiguration
@Service
public class DeliveryConfigInitCraneTask {

    @Resource
    private StoreDimensionConfigDOMapper storeDimensionConfigDOMapper;
    @Resource
    private StoreConfigDOMapper storeConfigDOMapper;
    @Resource
    private IStoreConfigDOMapper iStoreConfigDOMapper;
    @Resource
    private SelfDeliveryPoiConfigPOMapper selfDeliveryPoiConfigPOMapper;
    @Resource
    private StoreFulfillConfigThriftService.Iface storeFulfillConfigThriftService;

    private static List<Integer> CAN_TURN_SECOND_PLATFORM = Lists.newArrayList(
            DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode(),
            DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode(),
            DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()
    );

    /**
     * NOTE
     */
    @Crane("init-delivery-config-task")
    public void initDeliveryConfigTask(InitDeliveryConfigTaskParam param) throws Exception {
        log.info("initDeliveryConfigTask begin, param = {}", param);
        int fetchLimit = LionConfigUtils.getFetchLimit();
        //1.迁移渠道配置
        if (CollectionUtils.isEmpty(param.getIncludeTenantIds()) && CollectionUtils.isEmpty(param.getIncludeStoreIds())) {
            //全量
            Long nextId = 0L;
            while (Objects.nonNull(nextId)) {
                StoreConfigDOExample storeConfigDOExample = new StoreConfigDOExample();
                storeConfigDOExample.createCriteria()
                        .andIdGreaterThan(nextId);
                storeConfigDOExample.setOrderByClause("id asc");
                List<StoreConfigDO> storeConfigDOS = iStoreConfigDOMapper.selectByExampleWithLimit(storeConfigDOExample, fetchLimit);
                if (CollectionUtils.isEmpty(storeConfigDOS) || storeConfigDOS.size() < fetchLimit) {
                    nextId = null;
                } else {
                    nextId = storeConfigDOS.get(storeConfigDOS.size() - 1).getId();
                }
                //处理逻辑

            }
        } else {
            //租户维度分开处理
            if (CollectionUtils.isNotEmpty(param.getIncludeTenantIds())) {
                for (Long includeTenantId : param.getIncludeTenantIds()) {
                    StoreConfigDOExample storeConfigDOExample = new StoreConfigDOExample();
                    StoreConfigDOExample.Criteria criteria = storeConfigDOExample.createCriteria()
                            .andTenantIdEqualTo(includeTenantId);
                    if (CollectionUtils.isNotEmpty(param.getIncludeStoreIds())) {
                        criteria.andStoreIdIn(param.getIncludeStoreIds());
                    }
                    List<StoreConfigDO> storeConfigDOS = iStoreConfigDOMapper.selectByExampleWithLimit(storeConfigDOExample, fetchLimit);
                    //处理逻辑
                }

            }
        }
    }

    private void handleInitStoreConfig(List<StoreConfigDO> storeConfigDOS, boolean forceInit) {
        if (CollectionUtils.isEmpty(storeConfigDOS)) {
            return;
        }
        //歪马和赋能处理不同，分开处理
        List<StoreConfigDO> dhStoreConfigs = IListUtils.nullSafeFilterElement(storeConfigDOS, storeConfigDO -> LionConfigUtils.getDHTenantIdList().contains(String.valueOf(storeConfigDO.getTenantId())));
        List<StoreConfigDO> qnhStoreConfigs = IListUtils.nullSafeFilterElement(storeConfigDOS, storeConfigDO -> !LionConfigUtils.getDHTenantIdList().contains(String.valueOf(storeConfigDO.getTenantId())));
        //歪马
        if (CollectionUtils.isNotEmpty(dhStoreConfigs)) {
            Map<Long, List<StoreConfigDO>> storeIdConfigListMap =
                    dhStoreConfigs.stream()
                            //如果已经初始化过了 就放弃初始化.
                            .filter(storeConfigDO -> forceInit || StringUtils.isBlank(storeConfigDO.getSecondDeliveryPlatform()))
                            .collect(Collectors.groupingBy(StoreConfigDO::getStoreId));
            //歪马根据是否开启转青云来控制的
            SelfDeliveryPoiConfigPOExample selfDeliveryPoiConfigPOExample = new SelfDeliveryPoiConfigPOExample();
            selfDeliveryPoiConfigPOExample.createCriteria().andPoiIdIn(Lists.newArrayList(storeIdConfigListMap.keySet())).andIsDeletedEqualTo(0);
            List<SelfDeliveryPoiConfigPO> selfDeliveryPoiConfigPOS = selfDeliveryPoiConfigPOMapper.selectByExample(selfDeliveryPoiConfigPOExample);
            Map<Long, Integer> storeIdEnableTurnAggMap = IListUtils.nullSafeAndOverrideCollectToMap(selfDeliveryPoiConfigPOS, SelfDeliveryPoiConfigPO::getPoiId, SelfDeliveryPoiConfigPO::getEnableTurnDelivery);

            for (Map.Entry<Long, List<StoreConfigDO>> entry : storeIdConfigListMap.entrySet()) {
                //自送是必须的
                Set<Integer> turnDeliveryPlatforms = Sets.newHashSet(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode());
                if (storeIdEnableTurnAggMap.containsKey(entry.getKey()) && storeIdEnableTurnAggMap.get(entry.getKey()) == 1) {
                    turnDeliveryPlatforms.add(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode());
                }

                //订单实付金额小于150元 && 不是餐馆 && 不是封签交付订单 && 不是发财酒订单
                SecondDeliveryForbiddenCondition secondDeliveryForbiddenCondition = new SecondDeliveryForbiddenCondition();
                secondDeliveryForbiddenCondition.setOrderTags(Lists.newArrayList(OrderLabelEnum.SEAL_DELIVERY_ORDER.getId(), OrderLabelEnum.PRIORITY_DELIVERY_ORDER.getId(), OrderLabelEnum.MT_FACAI_WINE.getId()));
                secondDeliveryForbiddenCondition.setOrderActualPayment("150");

                for (StoreConfigDO storeConfigDO : entry.getValue()) {
                    storeConfigDO.setSecondDeliveryPlatform(JSON.toJSONString(Lists.newArrayList(turnDeliveryPlatforms)));
                    storeConfigDO.setTurnSecondDeliveryPlatformCondition(JSON.toJSONString(secondDeliveryForbiddenCondition));
                }
            }
        }

        //赋能
        if (CollectionUtils.isNotEmpty(qnhStoreConfigs)) {
            Map<Long, List<StoreConfigDO>> storeIdConfigListMap =
                    qnhStoreConfigs.stream()
                            //如果已经初始化过了 就放弃初始化.
                            .filter(storeConfigDO -> forceInit || StringUtils.isBlank(storeConfigDO.getSecondDeliveryPlatform()))
                            .collect(Collectors.groupingBy(StoreConfigDO::getStoreId));
            //手动转辅配,赋能原本是看每个渠道开通了什么
            for (List<StoreConfigDO> singStoreConfigList : storeIdConfigListMap.values()) {
                queryBookingPushDownMap(singStoreConfigList.get(0).getTenantId(), singStoreConfigList.get(0).getStoreId(), IListUtils.mapTo(singStoreConfigList, StoreConfigDO::getChannelType));
                Set<Integer> turnDeliveryPlatforms = singStoreConfigList.stream().map(StoreConfigDO::getOpenAggrPlatform).filter(openAggrPlat -> CAN_TURN_SECOND_PLATFORM.contains(openAggrPlat)).collect(Collectors.toSet());
                //自送是必须的
                turnDeliveryPlatforms.add(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY.getCode());
                String secondDeliveryConfigJson = JSON.toJSONString(Lists.newArrayList(turnDeliveryPlatforms));
                singStoreConfigList.forEach(storeConfigDO -> {storeConfigDO.setSecondDeliveryPlatform(secondDeliveryConfigJson);});
            }
        }
    }

    private Map<Integer/*channelId*/, Integer/*bookingPushDownTime*/> queryBookingPushDownMap(Long tenantId, Long storeId, List<Integer> channelIds) {
        try {
            Map<Integer/*channelId*/, Integer/*bookingPushDownTime*/> channelBookingPushDownMap = Maps.newHashMap();
            //初始化,默认为40
            channelIds.forEach(channelId -> {channelBookingPushDownMap.put(channelId, 40);});

            FulfillConfigQueryResponse response = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(
                    (RetryCallback<FulfillConfigQueryResponse, Exception>) context -> storeFulfillConfigThriftService.batchQuery(tenantId,
                            Arrays.asList(storeId), 1L)
            );

            if (response == null) {
                return channelBookingPushDownMap;
            }
            List<FulfillConfig> fulfillConfigList = response.getFulfillConfigList();
            if (CollectionUtils.isEmpty(fulfillConfigList)) {
                return channelBookingPushDownMap;
            }
            FulfillConfig fulfillConfig = fulfillConfigList.get(0);
            buildMinutesOfPushDownInAdvanceByOrderBizType(fulfillConfig, channelBookingPushDownMap);
            return channelBookingPushDownMap;
        } catch (Exception e) {
            log.error("PickSelectRemoteService.queryFulfillStoreConfig tenantId:{},storeId:{}", tenantId, storeId, e);
            throw new SystemException("PickSelectRemoteService.queryFulfillStoreConfig error");
        }
    }

    private void buildMinutesOfPushDownInAdvanceByOrderBizType(FulfillConfig fulfillConfig, Map<Integer/*channelId*/, Integer/*bookingPushDownTime*/> channelBookingPushDownMap) {
        try {
            if (fulfillConfig.getTimeoutType() == 0) {
                channelBookingPushDownMap.entrySet().forEach(entry -> {entry.setValue(fulfillConfig.getMinutesOfPushDownInAdvance());});
            } else {
                channelBookingPushDownMap.entrySet().forEach(entry -> {
                    entry.setValue(getBookingPushDownMinsByOrderBizType(fulfillConfig, Optional.ofNullable(DynamicOrderBizType.channelId2OrderBizType(entry.getKey())).map(DynamicOrderBizType::getValue).orElse(null)));
                });
            }
        } catch (Exception e) {
            Cat.logEvent("GET_TIMEOUT_ERROR", "ERROR");
            log.error("getMinutesOfPushDownInAdvanceByOrderBizType error", e);
        }
    }


    private Integer getBookingPushDownMinsByOrderBizType(FulfillConfig fulfillConfig, Integer orderBizType) {
        // orderBiz相关的渠道 取不到，取美团的，美团的取不到，取默认
        final Integer orderBizTypeEnum = Optional.ofNullable(orderBizType)
                .orElse(DynamicOrderBizType.MEITUAN_WAIMAI.getValue());
        final Map<Integer, ChannelTimeOutConfig> timeOutConfigInfoMap = Optional.ofNullable(fulfillConfig.getChannelTimeOutConfig()).orElse(Collections.emptyList())
                .stream().filter(item -> Objects.nonNull(item.getOrderBizType()))
                .collect(Collectors.toMap(ChannelTimeOutConfig::getOrderBizType, it -> it, (a, b) -> a));
        final ChannelTimeOutConfig channelTimeOutConfigInfo = Optional
                .ofNullable(timeOutConfigInfoMap.get(orderBizTypeEnum))
                .orElse(timeOutConfigInfoMap.get(DynamicOrderBizType.MEITUAN_WAIMAI.getValue()));
        if (channelTimeOutConfigInfo == null) {
            // 默认为90分钟下发
            return 40;
        } else {
            return channelTimeOutConfigInfo.getMinutesOfPushDownInAdvance();
        }
    }
}
