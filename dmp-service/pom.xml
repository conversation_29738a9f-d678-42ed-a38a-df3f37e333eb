<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sankuai.shangou</groupId>
        <artifactId>logistics-dmp</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>dmp-service</artifactId>

    <dependencies>
        <!--外部业务服务依赖-->
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_tenant_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_management_client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.waimai</groupId>
            <artifactId>dws-openapi-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.saas</groupId>
            <artifactId>reco_store_saas_order_platform_common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
            <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
            <artifactId>shango_dh_labor-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
            <artifactId>shango_dh_labor-types</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_saas_auth_client</artifactId>
        </dependency>

        <!--基础组建-->
        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-common-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-high-level-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgshopmgmt.shangou.empower.regionselection</groupId>
            <artifactId>reco_shopmgmt_region_select-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cip.crane</groupId>
            <artifactId>crane-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mtrace</artifactId>
                    <groupId>com.meituan.mtrace</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--注解支持-->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!--spring重试支持-->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <!--单元测试组件-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <!--日志组件-->
        <dependency>
            <groupId>com.meituan.inf</groupId>
            <artifactId>xmd-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.log</groupId>
            <artifactId>scribe-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>shangou-thrift-publisher</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
            <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>udb-open-thrift</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtgis-remote-service</artifactId>
        </dependency>

        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>leaf-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>zebra-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.xframe</groupId>
            <artifactId>mafka-xframe-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-ds-monitor-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.zebra</groupId>
            <artifactId>zebra-dao</artifactId>
        </dependency>

        <!--数据计算-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-math3</artifactId>
            <version>3.6.1</version> <!-- 请使用最新的版本号 -->
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.xsupply</groupId>
            <artifactId>product_management-client</artifactId>
            <version>1.5.4</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.shangou.goodscenter</groupId>
            <artifactId>goods_center_client</artifactId>
            <version>1.0.14</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou</groupId>
            <artifactId>dmp-sdk</artifactId>
            <version>1.0.15-fusion-v2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.sgdata</groupId>
            <artifactId>query-api-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.bizmng</groupId>
            <artifactId>labor-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.infra</groupId>
            <artifactId>osw-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.shangou.empower</groupId>
            <artifactId>reco_store_sac_client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
            <artifactId>infrastructure-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>handle-unit-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>sdms-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.waima</groupId>
            <artifactId>support-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.logistics</groupId>
            <artifactId>oio-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>xm-pub-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
            <artifactId>open-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.qnh.ofc</groupId>
            <artifactId>qnh_ofc_ebase-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
            <artifactId>reco_qnh_poi_api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan.reco.pickselect</groupId>
            <artifactId>pick-select-ebase-idl</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.meituan.xframe.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>