version: v1
common:
  os: centos7
  tools:
    mt_oraclejdk: 8
build:
  env:
    SKIP_TEST: true
    PROFILE1: prod
    PROFILE2: online
  tools:
    mt_oraclejdk: 8
    thrift: 0.8.0
    maven: 3.3.3
  run:
    workDir: ./ # 可选修改, workDir是代码仓库的相对目录, ./代表代码仓库的根目录
    cmd:
      - sh deploy/compile.sh
  target:
    distDir: ./target/dist/
    files:
      - ./
autodeploy:
  targetDir: /opt/meituan/apps/reco_shopmgmt_pieapi/work  # 替换自己的部署路径
  env:
    ENVIRONMENT: prod
    APP_KEY: com.sankuai.sgshopmgmt.empower.pieapi    # 替换为自己项目本次使用的名字
    SERVER_NAME: reco_shopmgmt_pieapi
    CHECK_PORTS: "\"8412\"" #把数字替换为自己需要检测的端口
    CHECK_URL: http://localhost:8412/monitor/alive
  run: sh deploy/run.sh      
  check: sh deploy/check.sh  
  checkRetry: 60 # 默认值为1
  checkInterval: 10s # 默认值1s
  daemonToolsStopTimeout: 60s