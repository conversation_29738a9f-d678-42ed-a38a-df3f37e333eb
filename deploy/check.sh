#!/usr/bin/env bash
# ------------------------------------
# 检测服务端口是否有连接。需要配置环境变量,例如：CHECK_PORTS=“9090 9091”
# -----------------------------------

echo "check ......"
echo "curl -o /dev/null -s -w %{http_code} $CHECK_URL"

STATUS_CODE=`curl -o /dev/null -s -w %{http_code} $CHECK_URL`

echo "STATUS_CODE: $STATUS_CODE"
if [ "$STATUS_CODE" = "200" ]
then
    echo "check succeeded!"
    exit 0
elif [ "$STATUS_CODE" = "000" ]
then
     echo "check succeeded!"
     ## 此处需要改动为返回1
     exit 1
else
    echo "check failed!"
    exit 1
fi