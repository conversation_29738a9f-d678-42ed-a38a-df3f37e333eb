<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meituan.xframe</groupId>
        <artifactId>xframe-starter-parent</artifactId>
        <version>2.6.8</version>
    </parent>

    <groupId>com.sankuai.shangou</groupId>
    <artifactId>logistics-dmp</artifactId>
    <version>${revision}</version>
    <modules>
        <module>dmp-startup</module>
        <module>dmp-web</module>
        <module>dmp-service</module>
        <module>dmp-dao</module>
        <module>dmp-thrift</module>
        <module>dmp-sdk</module>
    </modules>

    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <env-suffix>-SNAPSHOT</env-suffix>
        <revision>1.0.15-fusion-v2-SNAPSHOT</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 工程依赖 -->
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>dmp-sdk</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>dmp-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>dmp-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>dmp-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>dmp-thrift</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 业务依赖 -->
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-auth</artifactId>
                <version>1.2.0${env-suffix}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_tenant_client</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
                <version>3.7.7</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_management_client</artifactId>
                <version>1.6.49</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.meituan.shangou</groupId>
                        <artifactId>store-saas-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.shangou.saas</groupId>
                        <artifactId>reco_store_saas_order_platform_common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                        <artifactId>reco_shopmgmt_ocms_service-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_platform_common</artifactId>
                <version>1.9.38</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.meituan.shangou</groupId>
                        <artifactId>store-saas-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.shangou.saas</groupId>
                        <artifactId>reco_store_saas_tenant_client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower</groupId>
                <artifactId>reco_store_saas_auth_client</artifactId>
                <version>1.6.15</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>reco_fulfillment_tms-rider-delivery-client</artifactId>
                <version>2.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan.reco.sgfulfillment</groupId>
                <artifactId>reco_fulfillment_tms-delivery-client</artifactId>
                <version>2.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
                <artifactId>shango_dh_labor-sdk</artifactId>
                <version>1.2.18${env-suffix}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.drunkhorsemgmt</groupId>
                <artifactId>shango_dh_labor-types</artifactId>
                <version>1.2.16</version>
            </dependency>


            <!-- 因reco_store_saas_tenant_client包没有处理好版本依赖冲突，显式指定版本避免版本冲突 -->
            <dependency>
                <groupId>com.meituan.shangou.linz</groupId>
                <artifactId>linz-boot</artifactId>
                <version>1.0.5</version>
            </dependency>

            <!-- 公共utils包 -->
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-common-utils</artifactId>
                <version>2.7.7${env-suffix}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.waimai</groupId>
                <artifactId>dws-openapi-client</artifactId>
                <version>1.2.6</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.infra</groupId>
                <artifactId>osw-api</artifactId>
                <version>1.0.25</version>
            </dependency>

            <!--外部开源包依赖-->
            <!--swagger依赖-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.7.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.20</version>
            </dependency>

            <!-- 中间件依赖 -->
            <!-- 日志 -->
            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-log4j2</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-common-log4j2</artifactId>
                <version>2.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.log</groupId>
                <artifactId>scribe-log4j2</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.service.inf</groupId>
                <artifactId>access-sdk-java</artifactId>
                <version>0.2.11</version>
            </dependency>
            <!--ES依赖-->
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-high-level-client</artifactId>
                <version>0.9.18_ES7</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 当poros-high-level-client在 [0.9.15,)区间时, httpcore，httpcore-nio需要符合版本区间[4.4.13,) -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>4.4.13</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore-nio</artifactId>
                <version>4.4.13</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>7.10.2-mt4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 缓存 -->
            <dependency>
                <groupId>com.dianping.squirrel</groupId>
                <artifactId>squirrel-client</artifactId>
                <version>2.5.0</version>
            </dependency>
            <!-- 调度 -->
            <dependency>
                <groupId>com.cip.crane</groupId>
                <artifactId>crane-client</artifactId>
                <version>1.4.4</version>
            </dependency>

            <!-- Gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.6</version>
            </dependency>


            <!-- easyExcel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.2.11</version>
            </dependency>

            <!-- mafka -->
            <dependency>
                <groupId>com.meituan.mafka</groupId>
                <artifactId>mafka-client_2.10</artifactId>
                <version>*******</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower</groupId>
                <artifactId>reco_store_sac_client</artifactId>
                <version>1.4.39</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-thrift-publisher</artifactId>
                <version>2.7.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>shangou-exception-common</artifactId>
                        <groupId>com.sankuai.shangou</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 异常处理 -->
            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-exception-collector</artifactId>
                <version>2.6.1</version>
            </dependency>
            <!-- 异常处理,Controller模块依赖的公共异常码与collector包一致 -->
            <dependency>
                <artifactId>shangou-controller</artifactId>
                <groupId>com.sankuai.shangou</groupId>
                <version>2.7.4</version>
            </dependency>

            <!-- 美团地区服务 -->
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>mtgis-remote-service</artifactId>
                <version>1.0.24.19</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.map.maf</groupId>
                <artifactId>openplatform-dependency</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.shangou.empower.ocms</groupId>
                <artifactId>reco_shopmgmt_ocms_channel_service-client</artifactId>
                <version>2.23.21</version>
            </dependency>

            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>1.18.2</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>udb-open-thrift</artifactId>
                <version>1.0.10</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>xm-pub-api-client</artifactId>
                <version>1.5.8</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.sgshopmgmt.shangou.empower.regionselection</groupId>
                <artifactId>reco_shopmgmt_region_select-sdk</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.sankuai.shangou</groupId>
                        <artifactId>shangou-exception-collector</artifactId>
                    </exclusion>
                </exclusions>
                <version>2.7.5</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgxsupply.wxmall</groupId>
                <artifactId>infrastructure-sdk</artifactId>
                <version>1.0.9${env-suffix}</version>
            </dependency>


            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-api</artifactId>
                <version>4.0.4.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-ds-monitor-client</artifactId>
                <version>4.0.4.5</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.zebra</groupId>
                <artifactId>zebra-dao</artifactId>
                <version>0.3.3</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.bizmng</groupId>
                <artifactId>labor-api</artifactId>
                <version>1.0.2${env-suffix}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.infra</groupId>
                <artifactId>osw-api</artifactId>
                <version>1.0.35</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.sgdata</groupId>
                <artifactId>query-api-sdk</artifactId>
                <version>1.0.17</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.shangou.qnh.ofc</groupId>
                <artifactId>qnh_ofc_ofw-client</artifactId>
                <version>1.0.3${env-suffix}</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.shangou.saas</groupId>
                <artifactId>reco_store_saas_order_biz_client</artifactId>
                <version>2.1.134</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>handle-unit-api</artifactId>
                <version>1.0.4</version>
            </dependency>

            <dependency>
                <groupId>com.meituan.service.mobile</groupId>
                <artifactId>mtthrift</artifactId>
                <version>2.5.9-annotation-extend</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou</groupId>
                <artifactId>shangou-common-lang</artifactId>
                <version>2.8.3</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>oio-api</artifactId>
                <version>1.1.3</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.logistics</groupId>
                <artifactId>sdms-sdk</artifactId>
                <version>1.0.6</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.waima</groupId>
                <artifactId>support-api</artifactId>
                <version>1.0.24</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.xm</groupId>
                <artifactId>xm-pub-api-client</artifactId>
                <version>1.5.7</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.dxenterprise.open.gateway</groupId>
                <artifactId>open-sdk</artifactId>
                <version>1.0.47-RELEASE</version>
            </dependency>

            <!--ofc-->
            <dependency>
                <groupId>com.sankuai.shangou.qnh.ofc</groupId>
                <artifactId>qnh_ofc_ebase-client</artifactId>
                <version>1.0.2-delivery-fusion-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.shangou.sgfnqnh.poi</groupId>
                <artifactId>reco_qnh_poi_api-client</artifactId>
                <version>1.0.10</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan.reco.pickselect</groupId>
                <artifactId>pick-select-ebase-idl</artifactId>
                <version>2.13.28</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <env-suffix>-SNAPSHOT</env-suffix>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <env-suffix>-SNAPSHOT</env-suffix>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env-suffix>-SNAPSHOT</env-suffix>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <env-suffix/>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env-suffix/>
            </properties>
            <distributionManagement>
                <repository>
                    <id>meituan-nexus-releases</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/releases</url>
                </repository>
                <snapshotRepository>
                    <id>meituan-nexus-snapshots</id>
                    <name>Meituan Nexus Repository</name>
                    <url>http://pixel.sankuai.com/repository/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>


    <build>
        <plugins>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <!--<version>1.2.2</version>-->
                <configuration>
                </configuration>
                <executions>
                    <!-- enable flattening -->
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <!-- ensure proper cleanup -->
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
